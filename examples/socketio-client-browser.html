<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Socket.IO 客户端示例</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
      body {
        font-family: 'Segoe UI', sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .status {
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
        font-weight: bold;
      }
      .connected {
        background-color: #d4edda;
        color: #155724;
      }
      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
      }
      .reconnecting {
        background-color: #fff3cd;
        color: #856404;
      }

      .log {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 10px;
        border-radius: 4px;
        height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        margin: 10px 0;
      }

      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }

      input,
      textarea {
        width: 100%;
        padding: 8px;
        margin: 5px 0;
        border: 1px solid #ced4da;
        border-radius: 4px;
      }

      .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔌 Socket.IO 客户端测试</h1>

      <!-- 连接状态 -->
      <div id="status" class="status disconnected">❌ 未连接</div>

      <!-- 连接控制 -->
      <div>
        <button id="connectBtn" onclick="connect()">连接服务器</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>
          断开连接
        </button>
        <button onclick="getServerStatus()">获取服务器状态</button>
        <button onclick="clearLog()">清空日志</button>
      </div>

      <div class="grid">
        <!-- 工作流测试 -->
        <div>
          <h3>🚀 工作流测试</h3>
          <input
            id="workflowName"
            placeholder="工作流名称"
            value="测试工作流"
          />
          <textarea id="workflowMessage" placeholder="消息内容" rows="3">
工作流执行成功，采集了100条数据</textarea
          >
          <div>
            <button onclick="sendWorkflowStart()">发送工作流开始</button>
            <button onclick="sendWorkflowProgress()">发送工作流进度</button>
            <button onclick="sendWorkflowComplete()">发送工作流完成</button>
            <button onclick="sendWorkflowError()">发送工作流错误</button>
          </div>
        </div>

        <!-- 心跳测试 -->
        <div>
          <h3>💗 心跳测试</h3>
          <div>
            <button onclick="sendHeartbeat()">发送心跳</button>
            <button id="autoHeartbeatBtn" onclick="toggleAutoHeartbeat()">
              开启自动心跳
            </button>
          </div>
          <p>心跳间隔: <span id="heartbeatCount">0</span></p>
          <p>最后心跳: <span id="lastHeartbeat">无</span></p>
        </div>
      </div>

      <!-- 数据发送测试 -->
      <div style="margin-top: 20px">
        <h3>📤 数据发送测试</h3>
        <input
          id="customData"
          placeholder="自定义数据 (JSON格式)"
          value='{"test": "data", "number": 123}'
        />
        <button onclick="sendCustomData()">发送数据</button>
      </div>

      <!-- 日志显示 -->
      <h3>📝 连接日志</h3>
      <div id="log" class="log"></div>
    </div>

    <script>
      let socket = null
      let heartbeatInterval = null
      let heartbeatCount = 0

      function log(message, type = 'info') {
        const logElement = document.getElementById('log')
        const timestamp = new Date().toLocaleString()
        const icon =
          {
            info: '📝',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            heartbeat: '💗',
          }[type] || '📝'

        logElement.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`
        logElement.scrollTop = logElement.scrollHeight
      }

      function updateStatus(status, message) {
        const statusElement = document.getElementById('status')
        statusElement.className = `status ${status}`
        statusElement.innerHTML = message
      }

      function connect() {
        if (socket && socket.connected) {
          log('已经连接到服务器', 'warning')
          return
        }

        log('正在连接到 Socket.IO 服务器...', 'info')
        updateStatus('reconnecting', '🔄 连接中...')

        // 创建 Socket.IO 连接
        socket = io('http://localhost:3001', {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          reconnection: true,
          reconnectionDelay: 2000,
          reconnectionAttempts: 5,
          reconnectionDelayMax: 5000,
        })

        // 连接成功
        socket.on('connect', () => {
          log(`✅ 连接成功! Socket ID: ${socket.id}`, 'success')
          updateStatus('connected', `✅ 已连接 (${socket.id})`)
          document.getElementById('connectBtn').disabled = true
          document.getElementById('disconnectBtn').disabled = false
        })

        // 接收欢迎消息
        socket.on('welcome', (data) => {
          log(`欢迎消息: ${JSON.stringify(data, null, 2)}`, 'success')
        })

        // 服务器心跳响应
        socket.on('server-heartbeat', (data) => {
          document.getElementById('lastHeartbeat').textContent = data.timestamp
          log(`服务器心跳响应: ${data.timestamp}`, 'heartbeat')
        })

        // 服务器 ping
        socket.on('server-ping', (data) => {
          log(`收到服务器 ping: ${data.timestamp}`, 'heartbeat')
          // 自动回复 pong
          socket.emit('pong', { timestamp: new Date().toISOString() })
        })

        // 服务器心跳广播
        socket.on('server-heartbeat-broadcast', (data) => {
          log(
            `服务器状态广播 - 连接数: ${
              data.connectedClients
            }, 运行时间: ${Math.round(data.uptime)}秒`,
            'info'
          )
        })

        // 工作流相关响应
        socket.on('acknowledgment', (data) => {
          log(`工作流完成确认: ${data.message}`, 'success')
        })

        socket.on('progress-received', (data) => {
          log(`进度更新确认: ${data.message}`, 'success')
        })

        socket.on('start-acknowledged', (data) => {
          log(`工作流开始确认: ${data.message}`, 'success')
        })

        socket.on('error-acknowledged', (data) => {
          log(`错误确认: ${data.message}`, 'success')
        })

        // 数据广播
        socket.on('data-broadcast', (data) => {
          log(
            `收到数据广播 (来自 ${data.from}): ${JSON.stringify(data.data)}`,
            'info'
          )
        })

        socket.on('data-sent', (data) => {
          log(
            `数据发送确认: ${data.message}, 接收者: ${data.recipients}`,
            'success'
          )
        })

        // 服务器状态
        socket.on('server-status', (data) => {
          log(
            `服务器状态: 连接数=${data.connectedClients}, 运行时间=${Math.round(
              data.uptime
            )}秒, 内存使用=${data.memory.heapUsed}`,
            'info'
          )
        })

        // 连接断开
        socket.on('disconnect', (reason) => {
          log(`连接断开: ${reason}`, 'error')
          updateStatus('disconnected', '❌ 连接断开')
          document.getElementById('connectBtn').disabled = false
          document.getElementById('disconnectBtn').disabled = true
          if (heartbeatInterval) {
            clearInterval(heartbeatInterval)
            heartbeatInterval = null
          }
        })

        // 重连中
        socket.on('reconnect_attempt', (attemptNumber) => {
          log(`重连尝试 #${attemptNumber}`, 'warning')
          updateStatus('reconnecting', `🔄 重连中... (${attemptNumber})`)
        })

        // 重连成功
        socket.on('reconnect', (attemptNumber) => {
          log(`重连成功! 尝试次数: ${attemptNumber}`, 'success')
          updateStatus('connected', `✅ 重连成功 (${socket.id})`)
        })

        // 重连失败
        socket.on('reconnect_failed', () => {
          log('重连失败，已达到最大重试次数', 'error')
          updateStatus('disconnected', '❌ 重连失败')
        })

        // 连接错误
        socket.on('connect_error', (error) => {
          log(`连接错误: ${error.message}`, 'error')
          updateStatus('disconnected', '❌ 连接错误')
        })

        // 服务器关闭通知
        socket.on('server-shutdown', (data) => {
          log(`服务器关闭通知: ${data.message}`, 'warning')
        })
      }

      function disconnect() {
        if (socket && socket.connected) {
          socket.disconnect()
          log('主动断开连接', 'info')
          if (heartbeatInterval) {
            clearInterval(heartbeatInterval)
            heartbeatInterval = null
            document.getElementById('autoHeartbeatBtn').textContent =
              '开启自动心跳'
          }
        }
      }

      function sendWorkflowStart() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        const data = {
          workflowName: document.getElementById('workflowName').value,
          message: '工作流开始执行',
          timestamp: new Date().toISOString(),
          startTime: Date.now(),
        }

        socket.emit('workflow-start', data)
        log(`发送工作流开始事件: ${JSON.stringify(data)}`, 'info')
      }

      function sendWorkflowProgress() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        const progress = Math.floor(Math.random() * 100)
        const data = {
          workflowName: document.getElementById('workflowName').value,
          progress: progress,
          message: `工作流执行进度: ${progress}%`,
          timestamp: new Date().toISOString(),
        }

        socket.emit('workflow-progress', data)
        log(`发送工作流进度: ${JSON.stringify(data)}`, 'info')
      }

      function sendWorkflowComplete() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        const data = {
          workflowName: document.getElementById('workflowName').value,
          message: document.getElementById('workflowMessage').value,
          timestamp: new Date().toISOString(),
          allVariables: {
            totalItems: 100,
            successCount: 95,
            errorCount: 5,
            executionTime: '2.5秒',
          },
        }

        socket.emit('workflow-complete', data)
        log(`发送工作流完成事件: ${JSON.stringify(data)}`, 'info')
      }

      function sendWorkflowError() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        const data = {
          workflowName: document.getElementById('workflowName').value,
          error: '网页加载超时，无法获取数据',
          timestamp: new Date().toISOString(),
          errorCode: 'TIMEOUT_ERROR',
        }

        socket.emit('workflow-error', data)
        log(`发送工作流错误事件: ${JSON.stringify(data)}`, 'error')
      }

      function sendHeartbeat() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        const data = {
          timestamp: new Date().toISOString(),
          clientInfo: {
            userAgent: navigator.userAgent,
            language: navigator.language,
          },
        }

        socket.emit('client-heartbeat', data)
        heartbeatCount++
        document.getElementById('heartbeatCount').textContent = heartbeatCount
        log(`发送心跳 #${heartbeatCount}`, 'heartbeat')
      }

      function toggleAutoHeartbeat() {
        const btn = document.getElementById('autoHeartbeatBtn')

        if (heartbeatInterval) {
          clearInterval(heartbeatInterval)
          heartbeatInterval = null
          btn.textContent = '开启自动心跳'
          log('停止自动心跳', 'info')
        } else {
          if (!socket || !socket.connected) {
            alert('请先连接到服务器')
            return
          }

          heartbeatInterval = setInterval(sendHeartbeat, 15000) // 每15秒发送一次心跳
          btn.textContent = '停止自动心跳'
          log('开启自动心跳 (15秒间隔)', 'info')
        }
      }

      function sendCustomData() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        try {
          const customData = JSON.parse(
            document.getElementById('customData').value
          )
          socket.emit('send-data', customData)
          log(`发送自定义数据: ${JSON.stringify(customData)}`, 'info')
        } catch (error) {
          alert('自定义数据必须是有效的JSON格式')
          log(`JSON解析错误: ${error.message}`, 'error')
        }
      }

      function getServerStatus() {
        if (!socket || !socket.connected) {
          alert('请先连接到服务器')
          return
        }

        socket.emit('get-server-status')
        log('请求服务器状态...', 'info')
      }

      function clearLog() {
        document.getElementById('log').innerHTML = ''
        heartbeatCount = 0
        document.getElementById('heartbeatCount').textContent = '0'
        document.getElementById('lastHeartbeat').textContent = '无'
      }

      // 页面加载完成后的初始化
      window.onload = function () {
        log('Socket.IO 客户端测试页面加载完成', 'info')
        log('点击"连接服务器"按钮开始测试', 'info')
      }

      // 页面关闭时断开连接
      window.onbeforeunload = function () {
        if (socket && socket.connected) {
          socket.disconnect()
        }
      }
    </script>
  </body>
</html>
