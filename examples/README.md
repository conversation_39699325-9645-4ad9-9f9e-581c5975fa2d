# Socket.IO 重构后的使用说明

## 📖 概述

本项目已将原生 WebSocket 服务器重构为基于 Socket.IO 的实时通信系统，提供了更稳定的连接、自动重连、心跳检测等高级功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 Socket.IO 服务器和客户端依赖
npm install socket.io socket.io-client

# 如果是开发环境，还需要安装类型定义
npm install --save-dev @types/socket.io @types/socket.io-client
```

### 2. 启动服务器

```bash
# 启动 Electron 应用（会自动启动 Socket.IO 服务器）
npm run dev

# 或者直接启动应用
npm start
```

服务器将在 `http://localhost:3001` 启动。

## 🌟 主要特性

### ✅ 已实现功能

- **自动重连机制**：客户端断线后自动重连，支持指数退避策略
- **心跳检测**：服务器和客户端双向心跳监控，及时发现连接异常
- **多传输协议**：支持 WebSocket 和 HTTP 长轮询，自动协商最佳协议
- **事件驱动**：基于事件的通信模式，支持多种工作流事件
- **连接管理**：实时连接状态跟踪和客户端信息管理
- **错误处理**：完善的错误捕获和处理机制
- **广播功能**：支持向所有连接的客户端广播消息

### 📡 支持的事件类型

#### 工作流相关事件

- `workflow-start` - 工作流开始
- `workflow-progress` - 工作流进度更新
- `workflow-complete` - 工作流完成
- `workflow-error` - 工作流错误

#### 系统事件

- `client-heartbeat` - 客户端心跳
- `server-heartbeat` - 服务器心跳响应
- `get-server-status` - 获取服务器状态
- `send-data` - 发送自定义数据

#### 服务器广播事件

- `server-heartbeat-broadcast` - 服务器状态广播
- `workflow-completed-broadcast` - 工作流完成广播
- `client-disconnected` - 客户端断开广播

## 📝 使用示例

### 浏览器客户端

打开 `examples/socketio-client-browser.html` 文件在浏览器中测试：

```html
<!DOCTYPE html>
<html>
  <head>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
  </head>
  <body>
    <script>
      // 连接到服务器
      const socket = io('http://localhost:3001')

      // 监听连接成功
      socket.on('connect', () => {
        console.log('连接成功:', socket.id)
      })

      // 发送工作流完成事件
      socket.emit('workflow-complete', {
        workflowName: '测试工作流',
        message: '工作流执行完成',
        timestamp: new Date().toISOString(),
        allVariables: { result: 'success' },
      })
    </script>
  </body>
</html>
```

### Node.js 客户端

运行 `examples/socketio-client-nodejs.js`：

```bash
node examples/socketio-client-nodejs.js
```

这将启动一个交互式命令行客户端，支持：

- 连接/断开管理
- 发送各种工作流事件
- 心跳测试
- 服务器状态查询

### Automa 插件集成

在 Automa 工作流的 JavaScript 代码块中使用：

```javascript
// 引入集成代码（复制 examples/automa-plugin-integration.js 内容）

// 基础使用
initAutomaSocket()

// 发送进度更新
updateProgress(50, '正在处理数据...')

// 发送完成通知
notifyComplete('采集完成', {
  totalItems: 100,
  successCount: 95,
})

// 发送错误通知
notifyError('页面加载失败', 'PAGE_LOAD_ERROR')

// 发送自定义数据
sendDataToElectron({
  hotelName: '黄龙饭店',
  price: 299,
  available: true,
})
```

## 🔧 配置选项

### 服务器配置

在 `websocket-server.js` 中可以修改以下配置：

```javascript
const io = new Server(httpServer, {
  cors: {
    origin: '*', // 允许的来源
    methods: ['GET', 'POST'], // 允许的 HTTP 方法
  },
  transports: ['websocket', 'polling'], // 支持的传输协议
  pingTimeout: 60000, // 60秒 ping 超时
  pingInterval: 25000, // 25秒 ping 间隔
  upgradeTimeout: 10000, // 10秒升级超时
  maxHttpBufferSize: 1e6, // 1MB 最大缓冲区
})
```

### 客户端配置

```javascript
const socket = io('http://localhost:3001', {
  transports: ['websocket', 'polling'], // 传输协议优先级
  timeout: 10000, // 连接超时时间
  reconnection: true, // 启用自动重连
  reconnectionDelay: 2000, // 重连延迟时间
  reconnectionAttempts: 5, // 最大重连次数
  reconnectionDelayMax: 5000, // 最大重连延迟
})
```

## 📊 心跳和重连机制

### 心跳检测

1. **服务器端心跳**：

   - 每 25 秒向客户端发送 ping
   - 60 秒内未收到 pong 则认为连接丢失
   - 每 60 秒广播服务器状态

2. **客户端心跳**：

   - 自动响应服务器 ping
   - 可以主动发送心跳检测连接状态
   - 支持自动心跳模式

3. **连接监控**：
   - 实时跟踪客户端最后活动时间
   - 检测僵死连接并及时清理
   - 记录连接统计信息

### 重连策略

1. **自动重连**：

   - 连接断开后自动尝试重连
   - 指数退避策略，避免频繁重连
   - 最大重连次数限制

2. **重连事件**：
   - `reconnect_attempt` - 重连尝试
   - `reconnect` - 重连成功
   - `reconnect_failed` - 重连失败

## 🛠️ 开发调试

### 启用调试模式

```bash
# 启用 Socket.IO 调试日志
DEBUG=socket.io* node your-app.js

# 或者在代码中启用
localStorage.debug = 'socket.io-client:*';
```

### 监控工具

1. **服务器状态监控**：

   ```javascript
   socket.emit('get-server-status')
   socket.on('server-status', (data) => {
     console.log('服务器状态:', data)
   })
   ```

2. **连接信息查看**：
   - 连接数量
   - 客户端列表
   - 内存使用情况
   - 运行时间

## ⚡ 性能优化

### 服务器端优化

1. **连接池管理**：

   - 限制最大连接数
   - 定期清理僵死连接
   - 内存使用监控

2. **消息处理**：
   - 批量处理消息
   - 消息优先级
   - 压缩大型消息

### 客户端优化

1. **连接管理**：

   - 避免频繁连接/断开
   - 合理设置重连参数
   - 及时清理事件监听器

2. **消息发送**：
   - 批量发送消息
   - 避免发送大型对象
   - 使用消息确认机制

## 🔒 安全考虑

### 访问控制

```javascript
// 服务器端验证
io.use((socket, next) => {
  const token = socket.handshake.auth.token
  if (isValidToken(token)) {
    next()
  } else {
    next(new Error('Authentication error'))
  }
})
```

### 数据验证

```javascript
// 验证传入数据
socket.on('workflow-complete', (data) => {
  if (!validateWorkflowData(data)) {
    socket.emit('error', { message: '数据格式错误' })
    return
  }
  handleWorkflowComplete(data)
})
```

## 🚨 故障排除

### 常见问题

1. **连接失败**：

   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证 CORS 配置

2. **频繁重连**：

   - 检查网络稳定性
   - 调整心跳间隔
   - 检查服务器资源

3. **消息丢失**：
   - 使用消息确认机制
   - 检查缓冲区大小
   - 监控连接状态

### 调试步骤

1. 启用详细日志
2. 检查网络连接
3. 验证事件名称
4. 确认数据格式
5. 监控服务器状态

## 📚 API 参考

### 服务器 API

```javascript
const {
  createWebSocketServer,
  broadcastMessage,
  closeServer,
} = require('./websocket-server')

// 创建服务器
const { io, httpServer } = createWebSocketServer(mainWindow)

// 广播消息
broadcastMessage('custom-event', { message: 'Hello all clients' })

// 关闭服务器
closeServer()
```

### 客户端 API

```javascript
// Automa 插件 API
initAutomaSocket() // 初始化连接
updateProgress(progress, message) // 更新进度
notifyComplete(message, results) // 完成通知
notifyError(error, code, details) // 错误通知
sendDataToElectron(data) // 发送数据
```

## 🎯 最佳实践

1. **连接管理**：

   - 应用启动时建立连接
   - 应用关闭时主动断开
   - 监听连接状态变化

2. **错误处理**：

   - 使用 try-catch 包装事件处理
   - 提供友好的错误信息
   - 记录详细的错误日志

3. **性能监控**：

   - 定期检查连接状态
   - 监控消息处理时间
   - 跟踪内存使用情况

4. **用户体验**：
   - 提供连接状态指示
   - 显示重连进度
   - 缓存离线消息

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台错误信息
2. 启用调试模式获取详细日志
3. 检查网络连接状态
4. 参考示例代码
5. 提交 Issue 或联系开发者

---

**注意**：本文档基于 Socket.IO v4.7.5，请确保使用兼容版本。
