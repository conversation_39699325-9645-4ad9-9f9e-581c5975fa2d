#!/usr/bin/env node
// examples/socketio-client-nodejs.js - Node.js Socket.IO 客户端示例

const { io } = require('socket.io-client')
const readline = require('readline')

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

class SocketIOClient {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.heartbeatInterval = null
    this.heartbeatCount = 0
    this.reconnectCount = 0
  }

  /**
   * 连接到服务器
   */
  connect() {
    if (this.socket && this.isConnected) {
      console.log('⚠️  已经连接到服务器')
      return
    }

    console.log('🔌 正在连接到 Socket.IO 服务器...')

    // 创建 Socket.IO 客户端连接
    this.socket = io('http://localhost:3001', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionDelay: 2000,
      reconnectionAttempts: 10,
      reconnectionDelayMax: 5000,
      randomizationFactor: 0.5,
      auth: {
        clientType: 'nodejs',
        version: '1.0.0',
      },
    })

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 连接成功
    this.socket.on('connect', () => {
      this.isConnected = true
      this.reconnectCount = 0
      console.log(`✅ 连接成功! Socket ID: ${this.socket.id}`)
      console.log(`🔗 传输协议: ${this.socket.io.engine.transport.name}`)
      this.showMenu()
    })

    // 接收欢迎消息
    this.socket.on('welcome', (data) => {
      console.log('📨 欢迎消息:', JSON.stringify(data, null, 2))
    })

    // 服务器心跳响应
    this.socket.on('server-heartbeat', (data) => {
      console.log(
        `💗 服务器心跳响应: ${data.timestamp}, 运行时间: ${Math.round(
          data.uptime
        )}秒`
      )
    })

    // 服务器 ping
    this.socket.on('server-ping', (data) => {
      console.log(`📡 收到服务器 ping: ${data.timestamp}`)
      // 自动回复 pong
      this.socket.emit('pong', {
        timestamp: new Date().toISOString(),
        clientType: 'nodejs',
      })
      console.log('📤 已回复 pong')
    })

    // 服务器心跳广播
    this.socket.on('server-heartbeat-broadcast', (data) => {
      const memoryMB = Math.round(data.memory.heapUsed / 1024 / 1024)
      console.log(
        `📊 服务器状态 - 连接数: ${
          data.connectedClients
        }, 运行时间: ${Math.round(data.uptime)}秒, 内存: ${memoryMB}MB`
      )
    })

    // 工作流相关响应
    this.socket.on('acknowledgment', (data) => {
      console.log(`✅ 工作流完成确认: ${data.message}`)
    })

    this.socket.on('progress-received', (data) => {
      console.log(`📊 进度更新确认: ${data.message}`)
    })

    this.socket.on('start-acknowledged', (data) => {
      console.log(`🚀 工作流开始确认: ${data.message}`)
    })

    this.socket.on('error-acknowledged', (data) => {
      console.log(`❌ 错误确认: ${data.message}`)
    })

    // 数据广播
    this.socket.on('data-broadcast', (data) => {
      console.log(`📡 收到数据广播 (来自 ${data.from}):`)
      console.log(JSON.stringify(data.data, null, 2))
    })

    this.socket.on('data-sent', (data) => {
      console.log(
        `📤 数据发送确认: ${data.message}, 接收者: ${data.recipients}`
      )
    })

    // 工作流完成广播
    this.socket.on('workflow-completed-broadcast', (data) => {
      console.log(`🎉 工作流完成广播:`)
      console.log(`   - 工作流: ${data.workflowName}`)
      console.log(`   - 执行者: ${data.completedBy}`)
      console.log(`   - 完成时间: ${data.completedAt}`)
      console.log(`   - 摘要: ${data.summary}`)
    })

    // 客户端断开广播
    this.socket.on('client-disconnected', (data) => {
      console.log(
        `👋 客户端断开: ${data.socketId}, 原因: ${data.reason}, 剩余: ${data.remainingClients}`
      )
    })

    // 服务器状态
    this.socket.on('server-status', (data) => {
      console.log('📊 服务器状态:')
      console.log(`   - 运行状态: ${data.isRunning ? '✅ 运行中' : '❌ 停止'}`)
      console.log(`   - 连接数: ${data.connectedClients}`)
      console.log(`   - 运行时间: ${Math.round(data.uptime)} 秒`)
      console.log(`   - 内存使用: ${data.memory.heapUsed}`)
      console.log(`   - 客户端列表:`)
      data.clients.forEach((client) => {
        const duration = Math.round(
          (new Date() - new Date(client.connectedAt)) / 1000
        )
        console.log(
          `     * ${client.id} (${client.transport}) - 连接 ${duration}秒`
        )
      })
    })

    // 传输协议升级
    this.socket.io.on('upgrade', () => {
      console.log(`🔄 传输协议升级到: ${this.socket.io.engine.transport.name}`)
    })

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      this.isConnected = false
      console.log(`❌ 连接断开: ${reason}`)

      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
        console.log('💗 自动心跳已停止')
      }
    })

    // 重连尝试
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.reconnectCount = attemptNumber
      console.log(`🔄 重连尝试 #${attemptNumber}`)
    })

    // 重连成功
    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true
      console.log(
        `✅ 重连成功! 尝试次数: ${attemptNumber}, 新 Socket ID: ${this.socket.id}`
      )
      this.showMenu()
    })

    // 重连失败
    this.socket.on('reconnect_failed', () => {
      console.log('❌ 重连失败，已达到最大重试次数')
      this.showMenu()
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.log(`❌ 连接错误: ${error.message}`)
    })

    // 服务器关闭通知
    this.socket.on('server-shutdown', (data) => {
      console.log(`⚠️  服务器关闭通知: ${data.message}`)
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket && this.isConnected) {
      this.socket.disconnect()
      console.log('🔌 主动断开连接')

      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
        console.log('💗 自动心跳已停止')
      }
    } else {
      console.log('⚠️  当前未连接到服务器')
    }
  }

  /**
   * 发送工作流开始事件
   */
  sendWorkflowStart() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const data = {
      workflowName: '携程酒店价格采集',
      message: '开始执行工作流',
      timestamp: new Date().toISOString(),
      startTime: Date.now(),
      params: {
        hotelId: '373059',
        dateRange: '2025-07-16 到 2025-07-20',
      },
    }

    this.socket.emit('workflow-start', data)
    console.log('🚀 已发送工作流开始事件')
  }

  /**
   * 发送工作流进度事件
   */
  sendWorkflowProgress() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const progress = Math.floor(Math.random() * 100)
    const data = {
      workflowName: '携程酒店价格采集',
      progress: progress,
      message: `正在处理第 ${progress} 个页面`,
      timestamp: new Date().toISOString(),
      details: {
        processed: progress,
        total: 100,
        currentUrl: `https://hotels.ctrip.com/hotels/373059.html`,
      },
    }

    this.socket.emit('workflow-progress', data)
    console.log(`📊 已发送工作流进度: ${progress}%`)
  }

  /**
   * 发送工作流完成事件
   */
  sendWorkflowComplete() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const data = {
      workflowName: '携程酒店价格采集',
      message: '工作流执行完成，成功采集酒店价格数据',
      timestamp: new Date().toISOString(),
      allVariables: {
        totalHotels: 1,
        totalRooms: 5,
        successCount: 5,
        errorCount: 0,
        executionTime: '125秒',
        dataSize: '2.3MB',
        priceRange: {
          min: 299,
          max: 899,
          average: 589,
        },
      },
    }

    this.socket.emit('workflow-complete', data)
    console.log('✅ 已发送工作流完成事件')
  }

  /**
   * 发送工作流错误事件
   */
  sendWorkflowError() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const data = {
      workflowName: '携程酒店价格采集',
      error: '页面加载超时，无法获取房间价格信息',
      timestamp: new Date().toISOString(),
      errorCode: 'PAGE_LOAD_TIMEOUT',
      errorDetails: {
        url: 'https://hotels.ctrip.com/hotels/373059.html',
        timeout: 30000,
        retries: 3,
      },
    }

    this.socket.emit('workflow-error', data)
    console.log('❌ 已发送工作流错误事件')
  }

  /**
   * 发送心跳
   */
  sendHeartbeat() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const data = {
      timestamp: new Date().toISOString(),
      clientInfo: {
        platform: process.platform,
        nodeVersion: process.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
    }

    this.socket.emit('client-heartbeat', data)
    this.heartbeatCount++
    console.log(`💗 已发送心跳 #${this.heartbeatCount}`)
  }

  /**
   * 切换自动心跳
   */
  toggleAutoHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      console.log('💗 自动心跳已停止')
    } else {
      if (!this.isConnected) {
        console.log('⚠️  请先连接到服务器')
        return
      }

      this.heartbeatInterval = setInterval(() => {
        this.sendHeartbeat()
      }, 20000) // 每20秒发送一次心跳

      console.log('💗 自动心跳已启动 (20秒间隔)')
    }
  }

  /**
   * 发送自定义数据
   */
  sendCustomData() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    const data = {
      type: 'hotel-data',
      content: {
        hotelName: '黄龙饭店',
        rooms: [
          { name: '标准间', price: 299, available: true },
          { name: '豪华间', price: 489, available: true },
          { name: '套房', price: 899, available: false },
        ],
        timestamp: new Date().toISOString(),
      },
      metadata: {
        source: 'ctrip',
        collector: 'nodejs-client',
        version: '1.0.0',
      },
    }

    this.socket.emit('send-data', data)
    console.log('📤 已发送自定义数据')
  }

  /**
   * 获取服务器状态
   */
  getServerStatus() {
    if (!this.isConnected) {
      console.log('⚠️  请先连接到服务器')
      return
    }

    this.socket.emit('get-server-status')
    console.log('📊 正在请求服务器状态...')
  }

  /**
   * 显示菜单
   */
  showMenu() {
    console.log('\n' + '='.repeat(50))
    console.log('🎛️  Socket.IO 客户端操作菜单')
    console.log('='.repeat(50))
    console.log('1.  发送工作流开始事件')
    console.log('2.  发送工作流进度事件')
    console.log('3.  发送工作流完成事件')
    console.log('4.  发送工作流错误事件')
    console.log('5.  发送心跳')
    console.log('6.  切换自动心跳')
    console.log('7.  发送自定义数据')
    console.log('8.  获取服务器状态')
    console.log('9.  断开连接')
    console.log('10. 重新连接')
    console.log('0.  退出程序')
    console.log('='.repeat(50))
    console.log(`当前状态: ${this.isConnected ? '✅ 已连接' : '❌ 未连接'}`)
    if (this.isConnected) {
      console.log(`Socket ID: ${this.socket.id}`)
      console.log(`传输协议: ${this.socket.io.engine.transport.name}`)
      console.log(`心跳状态: ${this.heartbeatInterval ? '✅ 自动' : '❌ 手动'}`)
    }
    console.log('='.repeat(50))

    this.promptUser()
  }

  /**
   * 提示用户输入
   */
  promptUser() {
    rl.question('请输入操作编号: ', (input) => {
      this.handleUserInput(input.trim())
    })
  }

  /**
   * 处理用户输入
   */
  handleUserInput(input) {
    switch (input) {
      case '1':
        this.sendWorkflowStart()
        break
      case '2':
        this.sendWorkflowProgress()
        break
      case '3':
        this.sendWorkflowComplete()
        break
      case '4':
        this.sendWorkflowError()
        break
      case '5':
        this.sendHeartbeat()
        break
      case '6':
        this.toggleAutoHeartbeat()
        break
      case '7':
        this.sendCustomData()
        break
      case '8':
        this.getServerStatus()
        break
      case '9':
        this.disconnect()
        break
      case '10':
        this.connect()
        break
      case '0':
        this.exit()
        return
      default:
        console.log('⚠️  无效的操作编号')
    }

    // 继续提示用户输入
    setTimeout(() => this.promptUser(), 1000)
  }

  /**
   * 退出程序
   */
  exit() {
    console.log('👋 正在退出...')

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    if (this.socket && this.isConnected) {
      this.socket.disconnect()
    }

    rl.close()
    process.exit(0)
  }

  /**
   * 启动客户端
   */
  start() {
    console.log('🚀 Socket.IO Node.js 客户端启动')
    console.log('服务器地址: http://localhost:3001')
    console.log('按 Ctrl+C 退出程序')
    console.log()

    this.connect()

    // 处理程序退出信号
    process.on('SIGINT', () => {
      console.log('\n收到退出信号...')
      this.exit()
    })

    process.on('SIGTERM', () => {
      console.log('\n收到终止信号...')
      this.exit()
    })
  }
}

// 启动客户端
const client = new SocketIOClient()
client.start()
