// examples/automa-plugin-integration.js - Automa 插件 Socket.IO 集成示例

/**
 * Automa Socket.IO 集成类
 * 用于在 Automa 工作流中与 Electron 应用进行实时通信
 */
class AutomaSocketIntegration {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.heartbeatInterval = null
    this.workflowContext = null

    // 初始化连接
    this.init()
  }

  /**
   * 初始化 Socket.IO 连接
   */
  init() {
    console.log('🔌 初始化 Automa Socket.IO 集成...')

    // 检查是否在 Automa 环境中
    if (typeof automaRefData !== 'undefined') {
      console.log('✅ 检测到 Automa 环境')
      this.workflowContext = {
        name: automaRefData('workflow', 'name') || 'Unknown Workflow',
        id: automaRefData('workflow', 'id') || 'unknown',
        version: automaRefData('workflow', 'version') || '1.0.0',
      }
    }

    this.connectToServer()
  }

  /**
   * 连接到 Socket.IO 服务器
   */
  connectToServer() {
    if (this.socket && this.isConnected) {
      console.log('⚠️ 已连接到服务器')
      return
    }

    console.log('🔗 正在连接到 Electron Socket.IO 服务器...')

    // 动态加载 Socket.IO 客户端库
    if (typeof io === 'undefined') {
      const script = document.createElement('script')
      script.src = 'https://cdn.socket.io/4.7.5/socket.io.min.js'
      script.onload = () => {
        this.createConnection()
      }
      document.head.appendChild(script)
    } else {
      this.createConnection()
    }
  }

  /**
   * 创建 Socket.IO 连接
   */
  createConnection() {
    this.socket = io('http://localhost:3001', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionDelay: 2000,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelayMax: 5000,
      auth: {
        clientType: 'automa-plugin',
        workflowName: this.workflowContext?.name || 'Unknown',
        version: '1.0.0',
      },
    })

    this.setupEventListeners()
    this.setupHeartbeat()
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 连接成功
    this.socket.on('connect', () => {
      this.isConnected = true
      this.reconnectAttempts = 0
      console.log(`✅ Socket.IO 连接成功! ID: ${this.socket.id}`)

      // 通知服务器插件已准备就绪
      this.socket.emit('automa-plugin-ready', {
        workflowContext: this.workflowContext,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      })
    })

    // 接收欢迎消息
    this.socket.on('welcome', (data) => {
      console.log('📨 服务器欢迎消息:', data)
    })

    // Electron 应用准备就绪
    this.socket.on('electron-app-ready', (data) => {
      console.log('✅ Electron 应用已准备就绪')
      console.log('📋 可用功能:', data.features)
    })

    // 服务器心跳
    this.socket.on('server-heartbeat', (data) => {
      console.log(`💗 服务器心跳: ${data.timestamp}`)
    })

    // 服务器 ping
    this.socket.on('server-ping', (data) => {
      console.log(`📡 收到服务器 ping`)
      this.socket.emit('pong', {
        timestamp: new Date().toISOString(),
        workflowName: this.workflowContext?.name,
      })
    })

    // 工作流相关确认消息
    this.socket.on('acknowledgment', (data) => {
      console.log('✅ 工作流完成确认:', data.message)
    })

    this.socket.on('start-acknowledged', (data) => {
      console.log('🚀 工作流开始确认:', data.message)
    })

    this.socket.on('progress-received', (data) => {
      console.log('📊 进度更新确认:', data.message)
    })

    this.socket.on('error-acknowledged', (data) => {
      console.log('❌ 错误确认:', data.message)
    })

    // 服务器广播消息
    this.socket.on('workflow-completed-broadcast', (data) => {
      if (data.completedBy !== this.socket.id) {
        console.log(
          `🎉 其他工作流完成通知: ${data.workflowName} (${data.completedBy})`
        )
      }
    })

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      this.isConnected = false
      console.log(`❌ 连接断开: ${reason}`)

      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }
    })

    // 重连
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.reconnectAttempts = attemptNumber
      console.log(`🔄 重连尝试 #${attemptNumber}`)
    })

    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true
      console.log(`✅ 重连成功! 尝试次数: ${attemptNumber}`)
    })

    this.socket.on('reconnect_failed', () => {
      console.log('❌ 重连失败，已达到最大重试次数')
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.log(`❌ 连接错误: ${error.message}`)
    })
  }

  /**
   * 设置心跳机制
   */
  setupHeartbeat() {
    // 每30秒发送一次心跳
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.sendHeartbeat()
      }
    }, 30000)
  }

  /**
   * 发送心跳
   */
  sendHeartbeat() {
    if (!this.isConnected) return

    this.socket.emit('client-heartbeat', {
      timestamp: new Date().toISOString(),
      workflowName: this.workflowContext?.name,
      currentUrl: window.location.href,
      clientInfo: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      },
    })
  }

  /**
   * 通知工作流开始
   */
  notifyWorkflowStart() {
    if (!this.isConnected) {
      console.log('⚠️ 未连接到服务器，无法发送工作流开始通知')
      return
    }

    const data = {
      workflowName: this.workflowContext?.name || 'Unknown Workflow',
      message: '工作流开始执行',
      timestamp: new Date().toISOString(),
      startTime: Date.now(),
      currentUrl: window.location.href,
      context: this.workflowContext,
    }

    this.socket.emit('workflow-start', data)
    console.log('🚀 已发送工作流开始通知')
  }

  /**
   * 更新工作流进度
   * @param {number} progress - 进度百分比 (0-100)
   * @param {string} message - 进度消息
   * @param {Object} details - 详细信息
   */
  updateProgress(progress, message = '', details = {}) {
    if (!this.isConnected) {
      console.log('⚠️ 未连接到服务器，无法发送进度更新')
      return
    }

    const data = {
      workflowName: this.workflowContext?.name || 'Unknown Workflow',
      progress: progress,
      message: message || `工作流执行进度: ${progress}%`,
      timestamp: new Date().toISOString(),
      currentUrl: window.location.href,
      details: {
        ...details,
        totalSteps: automaRefData?.('loopData')?.length || 0,
        currentStep: Math.floor(
          (progress / 100) * (automaRefData?.('loopData')?.length || 1)
        ),
      },
    }

    this.socket.emit('workflow-progress', data)
    console.log(`📊 已发送进度更新: ${progress}%`)
  }

  /**
   * 通知工作流完成
   * @param {string} message - 完成消息
   * @param {Object} results - 工作流结果数据
   */
  notifyWorkflowComplete(message = '工作流执行完成', results = {}) {
    if (!this.isConnected) {
      console.log('⚠️ 未连接到服务器，无法发送工作流完成通知')
      return
    }

    // 收集 Automa 变量数据
    let allVariables = {}
    if (typeof automaRefData !== 'undefined') {
      try {
        allVariables = automaRefData('variables') || {}
      } catch (error) {
        console.log('⚠️ 无法获取 Automa 变量数据:', error)
      }
    }

    const data = {
      workflowName: this.workflowContext?.name || 'Unknown Workflow',
      message: message,
      timestamp: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      currentUrl: window.location.href,
      allVariables: {
        ...allVariables,
        ...results,
      },
      context: this.workflowContext,
    }

    this.socket.emit('workflow-complete', data)
    console.log('✅ 已发送工作流完成通知')
  }

  /**
   * 通知工作流错误
   * @param {string} error - 错误信息
   * @param {string} errorCode - 错误代码
   * @param {Object} errorDetails - 错误详情
   */
  notifyWorkflowError(error, errorCode = 'UNKNOWN_ERROR', errorDetails = {}) {
    if (!this.isConnected) {
      console.log('⚠️ 未连接到服务器，无法发送错误通知')
      return
    }

    const data = {
      workflowName: this.workflowContext?.name || 'Unknown Workflow',
      error: error,
      errorCode: errorCode,
      timestamp: new Date().toISOString(),
      currentUrl: window.location.href,
      errorDetails: {
        ...errorDetails,
        userAgent: navigator.userAgent,
        stackTrace: new Error().stack,
      },
      context: this.workflowContext,
    }

    this.socket.emit('workflow-error', data)
    console.log('❌ 已发送工作流错误通知')
  }

  /**
   * 发送自定义数据
   * @param {Object} data - 要发送的数据
   */
  sendData(data) {
    if (!this.isConnected) {
      console.log('⚠️ 未连接到服务器，无法发送数据')
      return
    }

    const payload = {
      type: 'automa-data',
      workflowName: this.workflowContext?.name,
      timestamp: new Date().toISOString(),
      data: data,
    }

    this.socket.emit('send-data', payload)
    console.log('📤 已发送自定义数据')
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket && this.isConnected) {
      this.socket.disconnect()
      console.log('🔌 已断开连接')
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
}

// 全局实例
let automaSocket = null

/**
 * 初始化 Automa Socket 集成
 * 在工作流开始时调用
 */
function initAutomaSocket() {
  if (!automaSocket) {
    automaSocket = new AutomaSocketIntegration()

    // 延迟一秒后发送工作流开始通知
    setTimeout(() => {
      automaSocket.notifyWorkflowStart()
    }, 1000)
  }
  return automaSocket
}

/**
 * Automa 工作流中可直接使用的辅助函数
 */

// 发送进度更新
function updateProgress(progress, message = '', details = {}) {
  if (!automaSocket) {
    automaSocket = initAutomaSocket()
  }
  automaSocket.updateProgress(progress, message, details)
}

// 发送完成通知
function notifyComplete(message = '工作流执行完成', results = {}) {
  if (!automaSocket) {
    automaSocket = initAutomaSocket()
  }
  automaSocket.notifyWorkflowComplete(message, results)
}

// 发送错误通知
function notifyError(error, errorCode = 'WORKFLOW_ERROR', details = {}) {
  if (!automaSocket) {
    automaSocket = initAutomaSocket()
  }
  automaSocket.notifyWorkflowError(error, errorCode, details)
}

// 发送数据
function sendDataToElectron(data) {
  if (!automaSocket) {
    automaSocket = initAutomaSocket()
  }
  automaSocket.sendData(data)
}

/**
 * Automa 工作流代码示例
 * 可以直接在 Automa 的 JavaScript 代码块中使用
 */

// 示例 1: 基础使用
/*
// 在工作流开始时初始化连接
initAutomaSocket();

// 更新进度
updateProgress(25, '正在处理第一步');

// 发送数据到 Electron
sendDataToElectron({
    action: 'save-data',
    data: { hotelName: '黄龙饭店', price: 299 }
});

// 工作流完成时通知
notifyComplete('数据采集完成', {
    totalItems: 100,
    successCount: 95
});
*/

// 示例 2: 错误处理
/*
try {
    // 工作流逻辑
    const data = document.querySelector('.price-info');
    if (!data) {
        throw new Error('无法找到价格信息元素');
    }
    
    updateProgress(50, '成功获取价格信息');
    
} catch (error) {
    notifyError(error.message, 'ELEMENT_NOT_FOUND', {
        currentUrl: window.location.href,
        selector: '.price-info'
    });
}
*/

// 示例 3: 循环中的进度更新
/*
const hotels = automaRefData('loopData') || [];
hotels.forEach((hotel, index) => {
    const progress = Math.round((index / hotels.length) * 100);
    updateProgress(progress, `处理酒店: ${hotel.name}`);
    
    // 处理逻辑...
});
*/

// 自动初始化（如果在 Automa 环境中）
if (typeof automaRefData !== 'undefined') {
  console.log('🤖 检测到 Automa 环境，自动初始化 Socket 连接...')
  initAutomaSocket()
}

// 导出给外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    AutomaSocketIntegration,
    initAutomaSocket,
    updateProgress,
    notifyComplete,
    notifyError,
    sendDataToElectron,
  }
}
