{"compilerOptions": {"target": "es2020", "module": "nodenext", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "nodenext", "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": true, "declarationMap": true, "resolveJsonModule": true, "types": ["node"]}, "include": ["src/main/**/*"], "exclude": ["node_modules", "dist", "src/renderer"]}