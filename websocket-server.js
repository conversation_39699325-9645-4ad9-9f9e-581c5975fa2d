// websocket-server.js - Socket.IO 服务器代码
const { Server } = require('socket.io')
const { createServer } = require('http')
const { dialog } = require('electron')

let mainWindow
let io
let httpServer
let connectedClients = new Map() // 存储连接的客户端信息

/**
 * 创建 Socket.IO 服务器
 * @param {BrowserWindow} window - Electron 主窗口
 * @returns {Object} 服务器实例和 HTTP 服务器
 */
function createWebSocketServer(window) {
  mainWindow = window

  // 创建 HTTP 服务器
  httpServer = createServer()

  // 创建 Socket.IO 服务器
  io = new Server(httpServer, {
    cors: {
      origin: '*',
      methods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type'],
      credentials: true,
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000, // 60秒 ping 超时
    pingInterval: 25000, // 25秒 ping 间隔
    upgradeTimeout: 10000, // 10秒升级超时
    maxHttpBufferSize: 1e6, // 1MB 最大缓冲区
    allowEIO3: true, // 允许 Engine.IO v3 客户端
  })

  console.log('🔌 Socket.IO 服务器初始化完成')

  // 处理连接事件
  io.on('connection', (socket) => {
    console.log(
      `📱 新的 Socket.IO 连接: ${socket.id} (${socket.conn.transport.name})`
    )

    // 记录客户端信息
    connectedClients.set(socket.id, {
      id: socket.id,
      connectedAt: new Date(),
      lastSeen: new Date(),
      transport: socket.conn.transport.name,
      remoteAddress: socket.request.connection.remoteAddress,
      userAgent: socket.request.headers['user-agent'] || 'Unknown',
    })

    // 发送欢迎消息
    socket.emit('welcome', {
      message: '已连接到 Electron 应用',
      timestamp: new Date().toISOString(),
      socketId: socket.id,
      serverInfo: {
        version: '1.0.0',
        uptime: process.uptime(),
        connectedClients: connectedClients.size,
      },
    })

    // 设置心跳检测
    setupHeartbeat(socket)

    // 监听传输升级事件
    socket.conn.on('upgrade', () => {
      console.log(
        `🔄 客户端 ${socket.id} 传输协议升级到: ${socket.conn.transport.name}`
      )
      updateClientInfo(socket.id, { transport: socket.conn.transport.name })
    })

    // 处理工作流完成事件
    socket.on('workflow-complete', (data) => {
      console.log('📨 收到工作流完成事件:', data)
      updateClientLastSeen(socket.id)
      handleWorkflowComplete(data, socket)
    })

    // 处理工作流进度事件
    socket.on('workflow-progress', (data) => {
      console.log('📊 收到工作流进度事件:', data)
      updateClientLastSeen(socket.id)
      handleWorkflowProgress(data, socket)
    })

    // 处理工作流开始事件
    socket.on('workflow-start', (data) => {
      console.log('🚀 工作流开始:', data)
      updateClientLastSeen(socket.id)
      handleWorkflowStart(data, socket)
    })

    // 处理工作流错误事件
    socket.on('workflow-error', (data) => {
      console.log('❌ 工作流错误:', data)
      updateClientLastSeen(socket.id)
      handleWorkflowError(data, socket)
    })

    // 处理客户端心跳
    socket.on('client-heartbeat', (data) => {
      updateClientLastSeen(socket.id)
      socket.emit('server-heartbeat', {
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
        clientId: socket.id,
        uptime: process.uptime(),
      })
    })

    // 处理 ping 事件
    socket.on('ping', () => {
      console.log(`💗 收到客户端 ${socket.id} 的 ping`)
      updateClientLastSeen(socket.id)
    })

    // 处理 pong 事件
    socket.on('pong', () => {
      console.log(`💗 收到客户端 ${socket.id} 的 pong`)
      updateClientLastSeen(socket.id)
    })

    // 处理自定义数据发送
    socket.on('send-data', (data) => {
      console.log('📤 收到数据发送请求:', data)
      updateClientLastSeen(socket.id)

      // 广播给所有连接的客户端（除了发送者）
      socket.broadcast.emit('data-broadcast', {
        from: socket.id,
        data: data,
        timestamp: new Date().toISOString(),
      })

      // 确认发送
      socket.emit('data-sent', {
        message: '数据已广播',
        recipients: connectedClients.size - 1,
        timestamp: new Date().toISOString(),
      })
    })

    // 处理 Automa 插件就绪事件
    socket.on('automa-plugin-ready', (data) => {
      console.log('🔌 Automa 插件已准备就绪:', data)
      updateClientLastSeen(socket.id)

      socket.emit('electron-app-ready', {
        message: 'Electron 应用已准备就绪',
        features: [
          'workflow-execution',
          'data-storage',
          'file-operations',
          'desktop-notifications',
          'heartbeat-monitoring',
        ],
        timestamp: new Date().toISOString(),
      })
    })

    // 处理客户端状态查询
    socket.on('get-server-status', () => {
      updateClientLastSeen(socket.id)
      socket.emit('server-status', getServerStatus())
    })

    // 处理连接断开
    socket.on('disconnect', (reason) => {
      console.log(`🔌 Socket.IO 连接断开: ${socket.id}, 原因: ${reason}`)
      connectedClients.delete(socket.id)

      // 通知其他客户端
      socket.broadcast.emit('client-disconnected', {
        socketId: socket.id,
        reason: reason,
        timestamp: new Date().toISOString(),
        remainingClients: connectedClients.size,
      })
    })

    // 处理连接错误
    socket.on('error', (error) => {
      console.error(`❌ Socket.IO 连接错误 ${socket.id}:`, error)
    })

    // 处理重连事件
    socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 客户端 ${socket.id} 重连成功，尝试次数: ${attemptNumber}`)
      updateClientInfo(socket.id, {
        lastReconnect: new Date(),
        reconnectAttempts: attemptNumber,
      })
    })
  })

  // 启动服务器
  httpServer.listen(3001, 'localhost', () => {
    console.log('✅ Socket.IO 服务器成功启动在 http://localhost:3001')
    console.log('📊 服务器配置:')
    console.log('   - Ping 超时: 60秒')
    console.log('   - Ping 间隔: 25秒')
    console.log('   - 支持协议: WebSocket, Polling')
    console.log('   - CORS: 已启用')
  })

  // 设置服务器级别的心跳监控
  setupServerHeartbeat()

  return { io, httpServer }
}

/**
 * 设置客户端心跳检测
 */
function setupHeartbeat(socket) {
  const heartbeatInterval = setInterval(() => {
    const clientInfo = connectedClients.get(socket.id)
    if (!clientInfo) {
      clearInterval(heartbeatInterval)
      return
    }

    const now = new Date()
    const timeSinceLastSeen = now - clientInfo.lastSeen

    // 如果超过 90 秒没有活动，主动发送心跳
    if (timeSinceLastSeen > 90000) {
      console.log(`💗 向客户端 ${socket.id} 发送心跳检测`)
      socket.emit('server-ping', {
        timestamp: now.toISOString(),
        lastSeen: clientInfo.lastSeen.toISOString(),
      })
    }
  }, 30000) // 每30秒检查一次

  // 连接断开时清理定时器
  socket.on('disconnect', () => {
    clearInterval(heartbeatInterval)
  })
}

/**
 * 设置服务器级别的心跳监控
 */
function setupServerHeartbeat() {
  setInterval(() => {
    const now = new Date()
    const connectedCount = connectedClients.size

    if (connectedCount > 0) {
      console.log(`💗 服务器心跳 - 当前连接: ${connectedCount} 个客户端`)

      // 检查僵死连接
      connectedClients.forEach((clientInfo, socketId) => {
        const timeSinceLastSeen = now - clientInfo.lastSeen
        if (timeSinceLastSeen > 300000) {
          // 5分钟无活动
          console.log(
            `⚠️ 检测到僵死连接: ${socketId}, 最后活动: ${clientInfo.lastSeen}`
          )
        }
      })

      // 广播服务器状态
      io.emit('server-heartbeat-broadcast', {
        timestamp: now.toISOString(),
        connectedClients: connectedCount,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      })
    }
  }, 60000) // 每60秒一次服务器心跳
}

/**
 * 更新客户端最后活动时间
 */
function updateClientLastSeen(socketId) {
  const clientInfo = connectedClients.get(socketId)
  if (clientInfo) {
    clientInfo.lastSeen = new Date()
  }
}

/**
 * 更新客户端信息
 */
function updateClientInfo(socketId, updates) {
  const clientInfo = connectedClients.get(socketId)
  if (clientInfo) {
    Object.assign(clientInfo, updates)
  }
}

/**
 * 处理工作流完成
 */
function handleWorkflowComplete(data, socket) {
  console.log('🎉 处理工作流完成事件:', data)

  // 显示桌面通知
  if (mainWindow && !mainWindow.isDestroyed()) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Automa 工作流完成',
      message: '🎉 工作流执行完成！',
      detail: `工作流: ${data.workflowName || '未知'}\n消息: ${
        data.message || '无消息'
      }\n完成时间: ${new Date(data.timestamp).toLocaleString()}\n客户端: ${
        socket.id
      }`,
      buttons: ['确定'],
      defaultId: 0,
    })

    // 向渲染进程发送通知
    mainWindow.webContents.send('automa-notification', {
      type: 'workflow-complete',
      data: data,
      clientId: socket.id,
      timestamp: new Date().toISOString(),
    })
  }

  // 发送确认消息
  socket.emit('acknowledgment', {
    message: '工作流完成通知已接收',
    timestamp: new Date().toISOString(),
  })

  // 广播给其他客户端
  socket.broadcast.emit('workflow-completed-broadcast', {
    workflowName: data.workflowName,
    completedBy: socket.id,
    completedAt: new Date().toISOString(),
    summary: data.message,
  })
}

/**
 * 处理工作流进度
 */
function handleWorkflowProgress(data, socket) {
  console.log('📊 工作流进度更新:', data)

  // 向渲染进程发送进度更新
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('workflow-progress', {
      type: 'progress-update',
      data: data,
      clientId: socket.id,
      timestamp: new Date().toISOString(),
    })
  }

  socket.emit('progress-received', {
    message: '进度更新已接收',
    timestamp: new Date().toISOString(),
  })
}

/**
 * 处理工作流开始
 */
function handleWorkflowStart(data, socket) {
  console.log('🚀 工作流开始:', data)

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('workflow-start', {
      type: 'workflow-start',
      data: data,
      clientId: socket.id,
      timestamp: new Date().toISOString(),
    })
  }

  socket.emit('start-acknowledged', {
    message: '工作流开始确认',
    timestamp: new Date().toISOString(),
  })
}

/**
 * 处理工作流错误
 */
function handleWorkflowError(data, socket) {
  console.error('❌ 工作流错误:', data)

  // 显示错误对话框
  if (mainWindow && !mainWindow.isDestroyed()) {
    dialog.showMessageBox(mainWindow, {
      type: 'error',
      title: '工作流执行错误',
      message: '❌ 工作流执行失败',
      detail: `工作流: ${data.workflowName || '未知'}\n错误信息: ${
        data.error || '未知错误'
      }\n发生时间: ${new Date(data.timestamp).toLocaleString()}\n客户端: ${
        socket.id
      }`,
      buttons: ['确定'],
      defaultId: 0,
    })

    mainWindow.webContents.send('workflow-error', {
      type: 'workflow-error',
      data: data,
      clientId: socket.id,
      timestamp: new Date().toISOString(),
    })
  }

  socket.emit('error-acknowledged', {
    message: '错误已接收',
    timestamp: new Date().toISOString(),
  })
}

/**
 * 获取服务器状态
 */
function getServerStatus() {
  const memUsage = process.memoryUsage()

  return {
    isRunning: !!io,
    connectedClients: connectedClients.size,
    uptime: process.uptime(),
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
    },
    clients: Array.from(connectedClients.values()).map((client) => ({
      id: client.id,
      connectedAt: client.connectedAt,
      lastSeen: client.lastSeen,
      transport: client.transport,
    })),
    timestamp: new Date().toISOString(),
  }
}

/**
 * 广播消息给所有客户端
 */
function broadcastMessage(event, data) {
  if (io) {
    io.emit(event, {
      ...data,
      timestamp: new Date().toISOString(),
      broadcast: true,
      serverUptime: process.uptime(),
    })
    console.log(`📢 广播消息 "${event}" 给 ${connectedClients.size} 个客户端`)
  }
}

/**
 * 关闭服务器
 */
function closeServer() {
  if (io) {
    console.log('🔌 正在关闭 Socket.IO 服务器...')

    // 通知所有客户端服务器即将关闭
    io.emit('server-shutdown', {
      message: '服务器即将关闭',
      timestamp: new Date().toISOString(),
    })

    // 等待消息发送完成再关闭
    setTimeout(() => {
      io.close()
      if (httpServer) {
        httpServer.close()
      }
      connectedClients.clear()
      console.log('✅ Socket.IO 服务器已关闭')
    }, 1000)
  }
}

module.exports = {
  createWebSocketServer,
  getServerStatus,
  broadcastMessage,
  closeServer,
}
