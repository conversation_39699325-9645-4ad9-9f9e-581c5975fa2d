#!/usr/bin/env node

const { execSync } = require('child_process')
const semver = require('semver') // 需要安装: npm install semver
const packageJson = require('../package.json')

// 颜色输出函数
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkNodeVersion() {
  const currentNodeVersion = process.version
  const requiredNodeVersion = packageJson.engines.node

  console.log(`\n📦 Node.js 版本检查:`)
  console.log(`   当前版本: ${currentNodeVersion}`)
  console.log(`   要求版本: ${requiredNodeVersion}`)

  if (semver.satisfies(currentNodeVersion, requiredNodeVersion)) {
    log('green', '   ✅ Node.js 版本符合要求')
    return true
  } else {
    log('red', '   ❌ Node.js 版本不符合要求')
    log('yellow', `   请升级 Node.js 到 ${requiredNodeVersion} 或更高版本`)
    return false
  }
}

function checkPnpmVersion() {
  try {
    const pnpmVersionOutput = execSync('pnpm --version', {
      encoding: 'utf8',
    }).trim()
    const requiredPnpmVersion = packageJson.engines.pnpm

    console.log(`\n📦 pnpm 版本检查:`)
    console.log(`   当前版本: v${pnpmVersionOutput}`)
    console.log(`   要求版本: ${requiredPnpmVersion}`)

    if (semver.satisfies(pnpmVersionOutput, requiredPnpmVersion)) {
      log('green', '   ✅ pnpm 版本符合要求')
      return true
    } else {
      log('red', '   ❌ pnpm 版本不符合要求')
      log('yellow', `   请升级 pnpm 到 ${requiredPnpmVersion} 或更高版本`)
      log('blue', '   运行: npm install -g pnpm@latest')
      return false
    }
  } catch (error) {
    console.log(`\n📦 pnpm 版本检查:`)
    log('red', '   ❌ 未找到 pnpm，请先安装')
    log('blue', '   运行: npm install -g pnpm@latest')
    return false
  }
}

function main() {
  log('blue', '🔍 检查项目版本要求...\n')

  const nodeOk = checkNodeVersion()
  const pnpmOk = checkPnpmVersion()

  console.log('\n' + '='.repeat(50))

  if (nodeOk && pnpmOk) {
    log('green', '🎉 所有版本要求都满足！')
    process.exit(0)
  } else {
    log('red', '❌ 存在版本不兼容问题，请解决后再继续')
    console.log('\n📝 解决方案:')
    if (!nodeOk) {
      console.log('   1. 安装 Node.js: https://nodejs.org/')
      console.log('   2. 或使用 nvm: nvm install 18.20.1 && nvm use 18.20.1')
    }
    if (!pnpmOk) {
      console.log('   3. 安装 pnpm: npm install -g pnpm@latest')
    }
    process.exit(1)
  }
}

main()
