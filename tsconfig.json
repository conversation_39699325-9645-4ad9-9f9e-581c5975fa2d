{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "sourceMap": true, "declaration": false, "outDir": "./dist", "rootDir": "./src", "types": ["node"]}, "include": ["src/renderer/**/*"], "exclude": ["node_modules", "dist", "src/main"]}