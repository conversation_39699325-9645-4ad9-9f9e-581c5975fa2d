# 千里眼数据采集工作流平台

这是一个基于 **React + TypeScript + Electron** 技术栈构建的现代化桌面数据采集管理平台，集成了工作流自动化、数据采集、任务管理和实时通知功能。

## 🌟 核心功能

### 🔧 工作流管理

- ✅ **工具配置管理** - 支持多种采集工具的配置和版本控制
- ✅ **任务调度** - 灵活的任务创建、编辑和执行管理
- ✅ **Chrome 集成** - 专门针对 Chrome 浏览器的优化支持
- ✅ **自动化执行** - 与 Automa 插件深度集成的工作流执行

### 📊 数据管理

- ✅ **MySQL 数据库集成** - 完整的数据持久化存储
- ✅ **数据采集记录** - 详细的采集历史和状态追踪
- ✅ **文件管理** - Excel 导出、预览和下载功能
- ✅ **实时监控** - 采集进度和状态的实时更新

### 🔔 通知系统

- ✅ **钉钉通知集成** - 支持钉钉群组消息推送
- ✅ **桌面通知** - 系统级通知弹窗
- ✅ **实时消息** - WebSocket 实时通信支持

### 🎛️ 系统管理

- ✅ **用户配置** - 个性化设置和权限管理
- ✅ **系统监控** - 服务状态和性能监控
- ✅ **日志系统** - 完整的操作日志和错误追踪

## 📦 项目结构

```
crawl-electron/
├── src/
│   ├── main/                     # Electron 主进程
│   │   ├── config/              # 配置管理
│   │   │   ├── cosconfig.ts     # 腾讯云 COS 配置
│   │   │   ├── database.ts      # 数据库配置
│   │   │   ├── notification.ts  # 通知配置
│   │   │   └── store.ts         # 本地存储配置
│   │   ├── controller/          # 控制器层
│   │   │   └── mainController.ts # 主控制器
│   │   ├── converter/           # 数据转换层
│   │   ├── dao/                 # 数据访问层
│   │   ├── services/            # 业务逻辑层
│   │   │   ├── activity-service.ts
│   │   │   ├── collection-service.ts
│   │   │   ├── dashboard-service.ts
│   │   │   ├── dingtalk-service.ts
│   │   │   └── tool-config-service.ts
│   │   ├── cos-storage/         # 对象存储服务
│   │   └── main.ts             # 主进程入口
│   ├── renderer/               # Electron 渲染进程 (React)
│   │   ├── api/                # API 接口层
│   │   ├── components/         # 公共组件
│   │   │   ├── Layout/         # 布局组件
│   │   │   ├── NotificationContent/ # 通知内容组件
│   │   │   ├── RunLogModal/    # 运行日志弹窗
│   │   │   └── TaskForm/       # 任务表单组件
│   │   ├── pages/              # 页面组件
│   │   │   ├── Dashboard/      # 仪表板页面
│   │   │   ├── DataManagement/ # 数据管理页面
│   │   │   ├── MainPage/       # 主页面
│   │   │   ├── Settings/       # 设置页面
│   │   │   ├── TaskManagement/ # 任务管理页面
│   │   │   └── ToolManagement/ # 工具管理页面
│   │   ├── types/              # TypeScript 类型定义
│   │   ├── hooks/              # React Hooks
│   │   ├── utils/              # 工具函数
│   │   ├── App.tsx             # 应用主组件
│   │   └── index.tsx           # 渲染进程入口
│   └── workflow/               # 工作流引擎
│       ├── basic/              # 基础工作流
│       │   ├── automa/         # Automa 集成
│       │   ├── chrome/         # Chrome 扩展集成
│       │   └── tools/          # 工具集成
│       └── xc/                 # 携程专用工作流
├── dist/                       # 构建输出目录
├── scripts/                    # 构建和工具脚本
│   └── check-versions.js       # 版本检查脚本
├── examples/                   # 示例代码
├── assets/                     # 静态资源
├── package.json               # 项目配置
├── .nvmrc                     # Node.js 版本配置
├── .npmrc                     # npm/pnpm 配置
├── tsconfig.json              # TypeScript 配置
├── tsconfig.main.json         # 主进程 TypeScript 配置
└── webpack.config.js          # Webpack 配置
```

## 🛠️ 快速开始

### 环境要求

- **Node.js** >= 18.20.1
- **pnpm** >= 8.15.9
- **Chrome** 浏览器 (必需)
- **MySQL** 数据库 (可选，用于数据持久化)

### 1. 检查环境

```bash
# 检查 Node.js 版本
node --version  # 应该 >= v18.20.1

# 安装 pnpm (如果尚未安装)
npm install -g pnpm@latest

# 检查 pnpm 版本
pnpm --version  # 应该 >= 8.15.9

# 检查项目版本要求
pnpm run check-versions
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 配置数据库 (可选)

在应用设置中配置 MySQL 数据库连接：

```sql
-- 创建数据库
CREATE DATABASE hl_db;

-- 创建相关表结构 (应用会自动处理)
```

### 4. 构建应用

```bash
pnpm run build
```

### 5. 启动应用

```bash
pnpm start
```

### 6. 开发模式 (推荐)

```bash
pnpm run dev
```

> **注意**: 开发模式支持热重载，修改代码后会自动重新构建。

### 7. 配置和使用

#### 数据库配置

1. 打开应用，进入**设置**页面
2. 配置 MySQL 数据库连接信息
3. 测试连接确保数据库正常

#### 工具管理

1. 进入**工具管理**页面查看可用的采集工具
2. 配置工具参数和工作流模板
3. 测试工具执行确保功能正常

#### 任务管理

1. 在**任务管理**页面创建新的采集任务
2. 选择目标工具和配置参数
3. 设置执行时间和采集范围

#### Chrome 集成

1. 确保系统已安装 Chrome 浏览器
2. 应用会自动检测 Chrome 安装路径
3. 支持自动下载和打开 JSON 工作流文件

### 8. Automa 工作流集成

平台支持自动化的 Automa 工作流集成：

```javascript
// 在 Automa 工作流最后一步使用
fetch('http://localhost:3000/automa-complete', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: 'Workflow completed successfully',
    time: Date.now(),
    workflowName: '{{ workflow.name }}',
    data: '{{ data }}', // 采集的数据
    status: 0, // 0 表示成功，1 表示失败
    date: new Date().toISOString().split('T')[0],
    missionId: 123, // 任务ID
  }),
})
```

## 🌐 API 接口

### HTTP 通信端点

- **POST** `http://localhost:3000/automa-complete` - 接收 Automa 工作流完成通知
- **GET** `http://localhost:3000/status` - 检查 HTTP 服务器状态

### 内部 API 接口

#### 工具管理

- **GET** `/api/clairvoyant/queryToolConfig` - 查询工具配置列表
- **POST** `/api/clairvoyant/addOrUpdateToolConfig` - 添加或更新工具配置

#### 任务管理

- **GET** `/api/clairvoyant/queryTaskConfig` - 查询任务配置列表
- **POST** `/api/clairvoyant/addOrUpdateTaskConfig` - 创建或更新任务
- **DELETE** `/api/clairvoyant/deleteTaskConfig` - 删除任务

#### 数据管理

- **GET** `/api/clairvoyant/queryCollectionData` - 查询采集数据记录
- **GET** `/api/clairvoyant/previewCollectionFile` - 预览采集文件内容
- **DELETE** `/api/clairvoyant/deleteCollectionFile` - 删除采集文件

#### 系统配置

- **GET** `/api/clairvoyant/queryCoreMetric` - 查询核心指标数据
- **POST** `/api/clairvoyant/saveTencentCosConfig` - 保存腾讯云 COS 配置

### 通知集成

#### 钉钉通知

- 支持群组消息推送
- 自定义通知模板
- 错误通知和状态更新

#### 桌面通知

- 系统原生通知弹窗
- 任务状态变更提醒
- 采集完成通知

## 📋 核心模块对比

| 模块            | 功能描述                | 技术栈                   | 适用场景               |
| --------------- | ----------------------- | ------------------------ | ---------------------- |
| **工作流引擎**  | Automa 工作流执行和管理 | TypeScript + Chrome API  | 自动化数据采集         |
| **任务调度**    | 定时任务和手动执行管理  | Node.js + Express        | 批量采集任务管理       |
| **数据存储**    | MySQL 数据持久化存储    | MySQL + TypeORM          | 历史数据查询和统计     |
| **通知系统**    | 多渠道消息推送          | 钉钉 API + Electron 通知 | 实时状态通知           |
| **文件管理**    | Excel 文件生成和管理    | XLSX + 腾讯云 COS        | 数据导出和云端存储     |
| **Chrome 集成** | 浏览器自动化控制        | Playwright + Chrome      | 网页数据采集和文件处理 |

## 🔧 高级配置

### 数据库连接配置

在应用设置页面配置 MySQL 连接：

```javascript
// 数据库配置示例
{
  "host": "localhost",
  "port": 3306,
  "user": "your_username",
  "password": "your_password",
  "database": "hl_db"
}
```

### 钉钉通知配置

配置钉钉机器人推送：

```javascript
// 钉钉配置示例
{
  "clientId": "your_dingtalk_client_id",
  "clientSecret": "your_dingtalk_client_secret",
  "webhookUrl": "https://oapi.dingtalk.com/robot/send?access_token=xxx"
}
```

### 腾讯云 COS 存储配置

配置对象存储服务用于文件管理：

```javascript
// COS 配置示例
{
  "secretId": "your_secret_id",
  "secretKey": "your_secret_key",
  "bucket": "your-bucket-name",
  "region": "ap-guangzhou"
}
```

### Chrome 浏览器配置

系统会自动检测 Chrome 安装路径，也可以手动配置：

```javascript
// 不同平台的 Chrome 路径
const chromePaths = {
  darwin: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
  win32: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
  linux: '/usr/bin/google-chrome',
}
```

### 自定义端口配置

修改 `src/main/main.ts` 中的端口设置：

```javascript
const HTTP_PORT = 3000 // HTTP 服务器端口
const EXPRESS_PORT = 8080 // Express API 服务器端口
```

## 🎯 使用示例

### 工作流文件下载

平台支持自动下载和打开 JSON 工作流文件：

```typescript
// 下载携程工作流文件示例
import { downloadAndOpenJsonFile } from './workflow/xc/download-and-open-json'

const result = await downloadAndOpenJsonFile(
  'https://s3plus.meituan.net/assert-ptest/携程黄龙采集工作流.automa.json'
)

if (result.status === 200) {
  console.log('✅ 工作流文件下载成功并已在 Chrome 中打开')
  console.log(`文件路径: ${result.data?.filePath}`)
  console.log(`文件大小: ${result.data?.fileSize} 字节`)
} else {
  console.log('❌ 下载失败:', result.message)
}
```

### 数据采集完成通知

```javascript
// 在 Automa 工作流最后一步使用
fetch('http://localhost:3000/automa-complete', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: 'Workflow completed successfully',
    time: Date.now(),
    workflowName: '{{ workflow.name }}',
    data: JSON.stringify([
      {
        hotelName: '示例酒店',
        roomName: '标准间',
        salePrice: '298',
        lowestPrice: '268',
        breakfast: '含早',
        cancelRule: '免费取消',
        confirmRule: '立即确认',
        payWay: '到店付款',
        promotion: ['限时优惠', '会员价'],
        roomBasicInfo: ['WiFi', '空调', '电视'],
      },
    ]),
    status: 0, // 0 表示成功
    date: new Date().toISOString().split('T')[0],
    missionId: 123, // 对应的任务ID
  }),
})
```

### 任务执行示例

```typescript
// 创建并执行采集任务
const taskConfig = {
  taskName: '携程酒店数据采集',
  toolId: 1, // 携程工具ID
  targetUrl: JSON.stringify([
    {
      hotelName: '目标酒店',
      url: 'https://hotels.ctrip.com/hotel/123456.html',
    },
  ]),
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  collectionTime: ['09:00', '15:00', '21:00'],
  collectionDate: 7, // 采集近7天数据
  collectionMetric: [1, 2, 3], // 采集指标ID
}

// 通过 IPC 发送任务配置
ipcRenderer.send('open-chrome-extension', {
  toolId: taskConfig.toolId,
  configData: taskConfig,
})
```

## 🏗️ 构建和部署

### 开发模式

```bash
pnpm run dev
```

### 构建源代码

```bash
# 检查版本要求
pnpm run check-versions

# 构建所有代码
pnpm run build

# 只构建主进程
pnpm run build:main

# 只构建渲染进程
pnpm run build:renderer

# 监视模式构建 (开发时使用)
pnpm run build:watch
```

### 构建应用安装包

```bash
# 构建 Windows 版本
pnpm run build-win

# 构建 macOS 版本
pnpm run build-mac

# 构建 Linux 版本
pnpm run build-linux
```

### 应用打包

```bash
# 打包应用 (不创建安装包)
pnpm run pack

# 构建并分发
pnpm run dist
```

### 版本管理

```bash
# 检查所有版本要求
pnpm run check-versions

# 使用指定的 Node.js 版本 (需要安装 nvm)
nvm use  # 会自动使用 .nvmrc 中指定的版本

# 升级依赖包
pnpm update
```

## 🐛 故障排除

### 常见问题

1. **版本不兼容错误**

   ```bash
   # 检查版本要求
   pnpm run check-versions

   # 升级 Node.js
   nvm install 18.20.1 && nvm use 18.20.1

   # 升级 pnpm
   npm install -g pnpm@8.15.9
   ```

2. **数据库连接失败**

   - 确认 MySQL 服务正在运行
   - 检查数据库连接配置是否正确
   - 确认数据库 `hl_db` 已创建
   - 检查用户权限设置

3. **Chrome 浏览器相关问题**

   ```bash
   # 检查 Chrome 是否安装
   # macOS
   open -a "Google Chrome" --version

   # Windows
   "C:\Program Files\Google Chrome\Application\chrome.exe" --version

   # Linux
   google-chrome --version
   ```

4. **文件下载和打开失败**

   - 检查网络连接
   - 确认文件 URL 有效
   - 检查桌面写入权限
   - 查看 Chrome 是否能正常启动

5. **端口占用错误**

   - 检查端口 3000 和 8080 是否被占用
   - 修改配置文件中的端口设置
   - 使用 `lsof -i :3000` 查看端口占用情况

6. **Automa 通知失败**

   - 确认 Electron 应用正在运行
   - 检查 HTTP 服务器状态
   - 验证数据格式是否正确
   - 查看网络连接和防火墙设置

### 调试技巧

1. **启用详细日志**

   ```bash
   pnpm run dev
   ```

2. **检查服务器状态**

   ```bash
   # HTTP 服务器状态
   curl http://localhost:3000/status

   # 应用 API 状态
   curl http://localhost:8080/api/clairvoyant/queryCoreMetric
   ```

3. **查看应用日志**

   - 打开 Electron 开发者工具查看控制台输出
   - 检查主进程和渲染进程的日志
   - 查看网络请求的响应状态

4. **数据库调试**

   ```sql
   -- 检查数据库连接
   SELECT 1;

   -- 查看表结构
   SHOW TABLES;

   -- 检查数据
   SELECT * FROM tool_config LIMIT 5;
   ```

5. **Chrome 调试**

   ```bash
   # 手动启动 Chrome 并查看错误
   google-chrome --enable-logging --log-level=0

   # 检查 Chrome 进程
   ps aux | grep chrome
   ```

## 🛡️ 技术栈

### 前端技术

- **React** 18.x - 用户界面框架
- **TypeScript** 5.x - 类型安全的 JavaScript
- **Ant Design** 5.x - UI 组件库
- **Sass** - CSS 预处理器

### 后端技术

- **Electron** 27.x - 跨平台桌面应用框架
- **Node.js** >= 18.20.1 - JavaScript 运行时
- **Express** 5.x - Web 应用框架
- **MySQL** - 关系数据库

### 工具和服务

- **pnpm** >= 8.15.9 - 包管理器
- **Webpack** 5.x - 模块打包工具
- **Chrome** - 浏览器自动化
- **钉钉 API** - 消息通知
- **腾讯云 COS** - 对象存储

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献指南

欢迎为项目做出贡献！请遵循以下步骤：

1. **Fork** 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 **Pull Request**

### 开发规范

- 遵循 TypeScript 严格模式
- 使用 ESLint 进行代码检查
- 编写单元测试
- 更新相关文档

## 📞 技术支持

如有问题，请按以下顺序寻求帮助：

1. **查阅文档** - 仔细阅读本 README 和故障排除部分
2. **检查版本** - 运行 `pnpm run check-versions` 确保环境正确
3. **搜索问题** - 在 GitHub Issues 中搜索类似问题
4. **提交 Issue** - 详细描述问题和复现步骤
5. **联系维护者** - 在紧急情况下可直接联系项目维护者

### Bug 报告格式

```markdown
## 问题描述

简要描述遇到的问题

## 复现步骤

1. 第一步
2. 第二步
3. 第三步

## 期望行为

描述您期望发生的行为

## 实际行为

描述实际发生的行为

## 环境信息

- 操作系统: [如 macOS 13.0]
- Node.js 版本: [如 v18.20.1]
- pnpm 版本: [如 8.15.9]
- Chrome 版本: [如 120.0.6099.71]
```

---

**🎉 祝您使用愉快，数据采集高效便捷！**
