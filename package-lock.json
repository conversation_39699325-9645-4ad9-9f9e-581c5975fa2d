{"name": "electron-automa-bridge", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "electron-automa-bridge", "version": "1.0.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"@ant-design/icons": "^6.0.0", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@ui-tars/operator-browser": "^1.2.3", "@ui-tars/sdk": "^1.2.3", "@ui-tars/shared": "^1.2.3", "antd": "^5.26.4", "body-parser": "^2.2.0", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.16.0-beta.3", "dayjs": "^1.11.13", "electron-dl": "^4.0.0", "electron-store": "^8.2.0", "express": "^5.1.0", "jose": "^5.10.0", "lodash": "^4.17.21", "mysql2": "^3.14.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "sharp": "^0.33.5", "uuidv4": "^6.2.13", "ws": "^8.14.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/lodash": "^4.17.19", "@types/node": "^20.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "^27.0.0", "electron-builder": "^24.6.4", "html-webpack-plugin": "^5.5.0", "node-machine-id": "^1.1.12", "sass": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "^3.3.0", "ts-loader": "^9.4.0", "typescript": "^5.2.0", "wait-on": "^7.0.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/@agent-infra/browser": {"version": "0.1.1", "resolved": "http://r.npm.sankuai.com/@agent-infra/browser/download/@agent-infra/browser-0.1.1.tgz", "integrity": "sha1-47qJY8WLp9QjnkqhvPpFbn6RvaA=", "dependencies": {"@agent-infra/logger": "0.0.1", "@agent-infra/shared": "0.0.2", "edge-paths": "3.0.5", "puppeteer-core": "24.7.2", "which": "5.0.0"}}, "node_modules/@agent-infra/browser/node_modules/@types/which": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/@types/which/download/@types/which-1.3.2.tgz", "integrity": "sha1-nCRvwMk97TEchRLfKJH7QfYif98=", "license": "MIT"}, "node_modules/@agent-infra/browser/node_modules/edge-paths": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/edge-paths/download/edge-paths-2.2.1.tgz", "integrity": "sha1-0tkVEyJcBlFK6smEO/zlRqu/Q5E=", "license": "MIT", "dependencies": {"@types/which": "^1.3.2", "which": "^2.0.2"}}, "node_modules/@agent-infra/browser/node_modules/edge-paths/node_modules/isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/@agent-infra/browser/node_modules/edge-paths/node_modules/which": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/@agent-infra/browser/node_modules/isexe": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-3.1.1.tgz", "integrity": "sha1-SkB+K9eN37FL6gwnxvcHLd53Xw0=", "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/@agent-infra/browser/node_modules/which": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/which/download/which-5.0.0.tgz", "integrity": "sha1-2T8tk/eYNNQ2PH0MI+ANB8RmyNY=", "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/@agent-infra/logger": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/@agent-infra/logger/download/@agent-infra/logger-0.0.1.tgz", "integrity": "sha1-pwLy6CKSG1lMU4O5VyvGkTO3VI4="}, "node_modules/@agent-infra/shared": {"version": "0.0.2", "resolved": "http://r.npm.sankuai.com/@agent-infra/shared/download/@agent-infra/shared-0.0.2.tgz", "integrity": "sha1-dzM4oLLfc84965qYfDfJRpmd81g=", "dependencies": {"@types/turndown": "^5.0.5", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2"}}, "node_modules/@ant-design/colors": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-8.0.0.tgz", "integrity": "sha1-krWqHNRIlrYse2cTO01aagAmYWI=", "license": "MIT", "dependencies": {"@ant-design/fast-color": "^3.0.0"}}, "node_modules/@ant-design/cssinjs": {"version": "1.23.0", "resolved": "http://r.npm.sankuai.com/@ant-design/cssinjs/download/@ant-design/cssinjs-1.23.0.tgz", "integrity": "sha1-SS77qbFdZPQqTLXVaMqwYH0MKxY=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "@emotion/hash": "^0.8.0", "@emotion/unitless": "^0.7.5", "classnames": "^2.3.1", "csstype": "^3.1.3", "rc-util": "^5.35.0", "stylis": "^4.3.4"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/@ant-design/cssinjs-utils": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/@ant-design/cssinjs-utils/download/@ant-design/cssinjs-utils-1.1.3.tgz", "integrity": "sha1-XdeRJgV5IKaZLVezjdhOLAtweXc=", "license": "MIT", "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@babel/runtime": "^7.23.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@ant-design/fast-color": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-3.0.0.tgz", "integrity": "sha1-+1F4ID3oJfKEgJU49RQiA9DvPYA=", "license": "MIT", "engines": {"node": ">=8.x"}}, "node_modules/@ant-design/icons": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-6.0.0.tgz", "integrity": "sha1-MCyTW4sLQp5ERMvEWAkkcnYYbZQ=", "license": "MIT", "dependencies": {"@ant-design/colors": "^8.0.0", "@ant-design/icons-svg": "^4.4.0", "@rc-component/util": "^1.2.1", "classnames": "^2.2.6"}, "engines": {"node": ">=8"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/@ant-design/icons-svg": {"version": "4.4.2", "resolved": "http://r.npm.sankuai.com/@ant-design/icons-svg/download/@ant-design/icons-svg-4.4.2.tgz", "integrity": "sha1-7Svn+02CrH4dRaVKWwbWzs+L5vY=", "license": "MIT"}, "node_modules/@ant-design/react-slick": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/@ant-design/react-slick/download/@ant-design/react-slick-1.1.2.tgz", "integrity": "sha1-+Ezj5NDclB8CsW8dHW16Nx/7tPE=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.4", "classnames": "^2.2.5", "json2mq": "^0.2.0", "resize-observer-polyfill": "^1.5.1", "throttle-debounce": "^5.0.0"}, "peerDependencies": {"react": ">=16.9.0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.27.6.tgz", "integrity": "sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@develar/schema-utils": {"version": "2.6.5", "resolved": "http://r.npm.sankuai.com/@develar/schema-utils/download/@develar/schema-utils-2.6.5.tgz", "integrity": "sha1-Ps4ixYOEAkGabgQl+FdCuWHZtsY=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "resolved": "http://r.npm.sankuai.com/@discoveryjs/json-ext/download/@discoveryjs/json-ext-0.5.7.tgz", "integrity": "sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@electron/asar": {"version": "3.4.1", "resolved": "http://r.npm.sankuai.com/@electron/asar/download/@electron/asar-3.4.1.tgz", "integrity": "sha1-TpGWpLVPuhjFbNjVysZ8W9xYgGU=", "dev": true, "license": "MIT", "dependencies": {"commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}, "bin": {"asar": "bin/asar.js"}, "engines": {"node": ">=10.12.0"}}, "node_modules/@electron/asar/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@electron/asar/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@electron/get": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/@electron/get/download/@electron/get-2.0.3.tgz", "integrity": "sha1-+6VSaD04euvZ8/ytvK/I4S7k+WA=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "env-paths": "^2.2.0", "fs-extra": "^8.1.0", "got": "^11.8.5", "progress": "^2.0.3", "semver": "^6.2.0", "sumchecker": "^3.0.1"}, "engines": {"node": ">=12"}, "optionalDependencies": {"global-agent": "^3.0.0"}}, "node_modules/@electron/notarize": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/@electron/notarize/download/@electron/notarize-2.2.1.tgz", "integrity": "sha1-0KprxDy6gwxBv9hAuF2+Dic/Wf4=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.1", "promise-retry": "^2.0.1"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/notarize/node_modules/fs-extra": {"version": "9.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-9.1.0.tgz", "integrity": "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@electron/notarize/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/notarize/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/osx-sign": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/@electron/osx-sign/download/@electron/osx-sign-1.0.5.tgz", "integrity": "sha1-CvcUny/ORNGoIVZg/SWp+2EEVNg=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"compare-version": "^0.1.2", "debug": "^4.3.4", "fs-extra": "^10.0.0", "isbinaryfile": "^4.0.8", "minimist": "^1.2.6", "plist": "^3.0.5"}, "bin": {"electron-osx-flat": "bin/electron-osx-flat.js", "electron-osx-sign": "bin/electron-osx-sign.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@electron/osx-sign/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@electron/osx-sign/node_modules/isbinaryfile": {"version": "4.0.10", "resolved": "http://r.npm.sankuai.com/isbinaryfile/download/isbinaryfile-4.0.10.tgz", "integrity": "sha1-DFteMMJVei8G/r03tzIpRqruQrM=", "dev": true, "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/@electron/osx-sign/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/osx-sign/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/universal": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/@electron/universal/download/@electron/universal-1.5.1.tgz", "integrity": "sha1-8zi8W87++IVzzwqx1ZIPrBDQbuU=", "dev": true, "license": "MIT", "dependencies": {"@electron/asar": "^3.2.1", "@malept/cross-spawn-promise": "^1.1.0", "debug": "^4.3.1", "dir-compare": "^3.0.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "engines": {"node": ">=8.6"}}, "node_modules/@electron/universal/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@electron/universal/node_modules/fs-extra": {"version": "9.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-9.1.0.tgz", "integrity": "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@electron/universal/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/universal/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@electron/universal/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@emotion/hash": {"version": "0.8.0", "resolved": "http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.8.0.tgz", "integrity": "sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=", "license": "MIT"}, "node_modules/@emotion/unitless": {"version": "0.7.5", "resolved": "http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz", "integrity": "sha1-dyESkcGQCnALinjPr9oxYNdpSe0=", "license": "MIT"}, "node_modules/@hapi/hoek": {"version": "9.3.0", "resolved": "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-9.3.0.tgz", "integrity": "sha1-g2iGnctzW+Ln9ct2R9544WeiUfs=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz", "integrity": "sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@img/sharp-darwin-arm64": {"version": "0.33.5", "resolved": "http://r.npm.sankuai.com/@img/sharp-darwin-arm64/download/@img/sharp-darwin-arm64-0.33.5.tgz", "integrity": "sha1-71taB4YoBfHoFFo3fIum6YgTygg=", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.4"}}, "node_modules/@img/sharp-libvips-darwin-arm64": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/@img/sharp-libvips-darwin-arm64/download/@img/sharp-libvips-darwin-arm64-1.0.4.tgz", "integrity": "sha1-RHxQJnAMAamTx4BOuK9fbphowH8=", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz", "integrity": "sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/string-width-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz", "integrity": "sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@jimp/core": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/core/download/@jimp/core-1.6.0.tgz", "integrity": "sha1-PvJBvwL0BDG7OCrqZl5Rh6LAXu8=", "license": "MIT", "dependencies": {"@jimp/file-ops": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "await-to-js": "^3.0.0", "exif-parser": "^0.1.12", "file-type": "^16.0.0", "mime": "3"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/core/node_modules/mime": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/mime/download/mime-3.0.0.tgz", "integrity": "sha1-s3RVDco6DBhEOwyVCmpY8ZMc96c=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/@jimp/diff": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/diff/download/@jimp/diff-1.6.0.tgz", "integrity": "sha1-+NBYv61kdRxeXBNUmdGnhPeXxcg=", "license": "MIT", "dependencies": {"@jimp/plugin-resize": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "pixelmatch": "^5.3.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/file-ops": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/file-ops/download/@jimp/file-ops-1.6.0.tgz", "integrity": "sha1-rpxqplssmloWUVqP34O1X1EQAIc=", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@jimp/js-bmp": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/js-bmp/download/@jimp/js-bmp-1.6.0.tgz", "integrity": "sha1-/3xDBudkdFBj4knukm0N2AeSSr8=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "bmp-ts": "^1.0.9"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/js-gif": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/js-gif/download/@jimp/js-gif-1.6.0.tgz", "integrity": "sha1-DvpdgzF6idbtqTbirh3yt9Eio40=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "gifwrap": "^0.10.1", "omggif": "^1.0.10"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/js-jpeg": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/js-jpeg/download/@jimp/js-jpeg-1.6.0.tgz", "integrity": "sha1-5H2mdYNGVIB58KyP8hXQ2dHsQ14=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "jpeg-js": "^0.4.4"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/js-png": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/js-png/download/@jimp/js-png-1.6.0.tgz", "integrity": "sha1-yFet/b/LcQemURw7KTn/utD+/tw=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "pngjs": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/js-tiff": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/js-tiff/download/@jimp/js-tiff-1.6.0.tgz", "integrity": "sha1-8Y+j1Z9S/aM5rP3K2+c2O+2RLoE=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "utif2": "^4.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-blit": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-blit/download/@jimp/plugin-blit-1.6.0.tgz", "integrity": "sha1-/tNa77tXV1maQpmp/2wGzDRm9G8=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-blur": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-blur/download/@jimp/plugin-blur-1.6.0.tgz", "integrity": "sha1-eBs76d4nROXrarhuwF7n0s5Qkug=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/utils": "1.6.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-circle": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-circle/download/@jimp/plugin-circle-1.6.0.tgz", "integrity": "sha1-IxTceVUGjLSgAN5OzrAokOsTHIg=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-color": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-color/download/@jimp/plugin-color-1.6.0.tgz", "integrity": "sha1-knyD7pMgcK0oUmaEByjCGsOb8ns=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "tinycolor2": "^1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-contain": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-contain/download/@jimp/plugin-contain-1.6.0.tgz", "integrity": "sha1-0IkA7PhaxWSm+fP8DWHMjV5DYm4=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/plugin-blit": "1.6.0", "@jimp/plugin-resize": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-cover": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-cover/download/@jimp/plugin-cover-1.6.0.tgz", "integrity": "sha1-B/+y89asU2FsZvETHNZs7Rfjyj4=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/plugin-crop": "1.6.0", "@jimp/plugin-resize": "1.6.0", "@jimp/types": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-crop": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-crop/download/@jimp/plugin-crop-1.6.0.tgz", "integrity": "sha1-WfKyCGkzD9do0XQ9hFuLqe2bxSo=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-displace": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-displace/download/@jimp/plugin-displace-1.6.0.tgz", "integrity": "sha1-QbMlemwPZMdJwpwaLmS6ffMaeiU=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-dither": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-dither/download/@jimp/plugin-dither-1.6.0.tgz", "integrity": "sha1-EMFwcNy+xWWQTxG3mG6QriCFC28=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-fisheye": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-fisheye/download/@jimp/plugin-fisheye-1.6.0.tgz", "integrity": "sha1-KDHABgWYsnvwBL+KcK3+7AA9T8w=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-flip": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-flip/download/@jimp/plugin-flip-1.6.0.tgz", "integrity": "sha1-dch72w8MqdtEsyDMlnGqIB5StcM=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-hash": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-hash/download/@jimp/plugin-hash-1.6.0.tgz", "integrity": "sha1-jeid+7tr5nH5zbK1mBas8/B8Qpg=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/js-bmp": "1.6.0", "@jimp/js-jpeg": "1.6.0", "@jimp/js-png": "1.6.0", "@jimp/js-tiff": "1.6.0", "@jimp/plugin-color": "1.6.0", "@jimp/plugin-resize": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "any-base": "^1.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-mask": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-mask/download/@jimp/plugin-mask-1.6.0.tgz", "integrity": "sha1-K1pDfl2amQbcq7enuvTVzX0jYbE=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-print": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-print/download/@jimp/plugin-print-1.6.0.tgz", "integrity": "sha1-zO8yf1OvtHYXqmbKZUNURzgPrzQ=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/js-jpeg": "1.6.0", "@jimp/js-png": "1.6.0", "@jimp/plugin-blit": "1.6.0", "@jimp/types": "1.6.0", "parse-bmfont-ascii": "^1.0.6", "parse-bmfont-binary": "^1.0.6", "parse-bmfont-xml": "^1.1.6", "simple-xml-to-json": "^1.2.2", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-quantize": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-quantize/download/@jimp/plugin-quantize-1.6.0.tgz", "integrity": "sha1-iACV/A6tQTIdlL9UiV42bdfQedY=", "license": "MIT", "dependencies": {"image-q": "^4.0.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-resize": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-resize/download/@jimp/plugin-resize-1.6.0.tgz", "integrity": "sha1-Mx6JEu1odGhGFFAZvG4uoFfm8XU=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/types": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-rotate": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-rotate/download/@jimp/plugin-rotate-1.6.0.tgz", "integrity": "sha1-3icfOaOsnoU7AuAdPUSrCG0S4Jk=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/plugin-crop": "1.6.0", "@jimp/plugin-resize": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/plugin-threshold": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/plugin-threshold/download/@jimp/plugin-threshold-1.6.0.tgz", "integrity": "sha1-EUec9ZEx6pXcr/ahQDrxlkWTo/o=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/plugin-color": "1.6.0", "@jimp/plugin-hash": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0", "zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/types": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/types/download/@jimp/types-1.6.0.tgz", "integrity": "sha1-JwInMP1nNlPhQw5r2KxvbeFZb4k=", "license": "MIT", "dependencies": {"zod": "^3.23.8"}, "engines": {"node": ">=18"}}, "node_modules/@jimp/utils": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@jimp/utils/download/@jimp/utils-1.6.0.tgz", "integrity": "sha1-4ZbzlT6h68iPUM8NSQrbJK7/5ZY=", "license": "MIT", "dependencies": {"@jimp/types": "1.6.0", "tinycolor2": "^1.6.0"}, "engines": {"node": ">=18"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.12.tgz", "integrity": "sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.10", "resolved": "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.10.tgz", "integrity": "sha1-o1cURGouhFA/+b/mbx0dSEbyB1s=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.4.tgz", "integrity": "sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.29.tgz", "integrity": "sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@malept/cross-spawn-promise": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/@malept/cross-spawn-promise/download/@malept/cross-spawn-promise-1.1.1.tgz", "integrity": "sha1-UEryAK9rmOGYvOdovBcwxpNq4B0=", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.malept-cross-spawn-promise?utm_medium=referral&utm_source=npm_fund"}], "license": "Apache-2.0", "dependencies": {"cross-spawn": "^7.0.1"}, "engines": {"node": ">= 10"}}, "node_modules/@malept/flatpak-bundler": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/@malept/flatpak-bundler/download/@malept/flatpak-bundler-0.4.0.tgz", "integrity": "sha1-6KMsMKldIMKxu2NcxYCYGgY4mFg=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@malept/flatpak-bundler/node_modules/fs-extra": {"version": "9.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-9.1.0.tgz", "integrity": "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@malept/flatpak-bundler/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@malept/flatpak-bundler/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@mixmark-io/domino": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/@mixmark-io/domino/download/@mixmark-io/domino-2.2.0.tgz", "integrity": "sha1-To7Gm/Gv63oU8GKLfiwPNb2zNsM=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@parcel/watcher": {"version": "2.5.1", "resolved": "http://r.npm.sankuai.com/@parcel/watcher/download/@parcel/watcher-2.5.1.tgz", "integrity": "sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "node_modules/@parcel/watcher-darwin-arm64": {"version": "2.5.1", "resolved": "http://r.npm.sankuai.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.1.tgz", "integrity": "sha1-PSbc443mWQ73nEfsLFV5PAatT2c=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher/node_modules/node-addon-api": {"version": "7.1.1", "resolved": "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz", "integrity": "sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=", "dev": true, "license": "MIT", "optional": true}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz", "integrity": "sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@puppeteer/browsers": {"version": "2.10.2", "resolved": "http://r.npm.sankuai.com/@puppeteer/browsers/download/@puppeteer/browsers-2.10.2.tgz", "integrity": "sha1-wqY87mmca1uXG5/LqQlQmJcPFkg=", "license": "Apache-2.0", "dependencies": {"debug": "^4.4.0", "extract-zip": "^2.0.1", "progress": "^2.0.3", "proxy-agent": "^6.5.0", "semver": "^7.7.1", "tar-fs": "^3.0.8", "yargs": "^17.7.2"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "engines": {"node": ">=18"}}, "node_modules/@puppeteer/browsers/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@rc-component/async-validator": {"version": "5.0.4", "resolved": "http://r.npm.sankuai.com/@rc-component/async-validator/download/@rc-component/async-validator-5.0.4.tgz", "integrity": "sha1-UpGtkvAKFLZ2b8gXNcI0J3+D6Ug=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.4"}, "engines": {"node": ">=14.x"}}, "node_modules/@rc-component/color-picker": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/@rc-component/color-picker/download/@rc-component/color-picker-2.0.1.tgz", "integrity": "sha1-a5uWFSRmqdRHXL5ytAtZS/2hZL4=", "license": "MIT", "dependencies": {"@ant-design/fast-color": "^2.0.6", "@babel/runtime": "^7.23.6", "classnames": "^2.2.6", "rc-util": "^5.38.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/color-picker/node_modules/@ant-design/fast-color": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz", "integrity": "sha1-q01EVcFULJAX02fC+oyj5CFdC6I=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7"}, "engines": {"node": ">=8.x"}}, "node_modules/@rc-component/context": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@rc-component/context/download/@rc-component/context-1.4.0.tgz", "integrity": "sha1-3G+wIdZ3NUavjwFq5M6a6giDleg=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/mini-decimal": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@rc-component/mini-decimal/download/@rc-component/mini-decimal-1.1.0.tgz", "integrity": "sha1-e3o2KxSgpUy1vG/SuCcx8p8R2bA=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0"}, "engines": {"node": ">=8.x"}}, "node_modules/@rc-component/mutate-observer": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@rc-component/mutate-observer/download/@rc-component/mutate-observer-1.1.0.tgz", "integrity": "sha1-7lPMiLeKrePNBlNgkhWkR3k4b9g=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/portal": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/@rc-component/portal/download/@rc-component/portal-1.1.2.tgz", "integrity": "sha1-VdseUdeE4DRELpcAU2+qpqtj/HE=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/qrcode": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/@rc-component/qrcode/download/@rc-component/qrcode-1.0.0.tgz", "integrity": "sha1-SKjeXrEdDmWSbxN3xLHvTIiJl/U=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/tour": {"version": "1.15.1", "resolved": "http://r.npm.sankuai.com/@rc-component/tour/download/@rc-component/tour-1.15.1.tgz", "integrity": "sha1-m3mAglQYX8GelkFy2Z4l6MaADe0=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/portal": "^1.0.0-9", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/trigger": {"version": "2.2.7", "resolved": "http://r.npm.sankuai.com/@rc-component/trigger/download/@rc-component/trigger-2.2.7.tgz", "integrity": "sha1-orl+y7kygKPEJOUfpBWzcbNV12o=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.2", "@rc-component/portal": "^1.1.0", "classnames": "^2.3.2", "rc-motion": "^2.0.0", "rc-resize-observer": "^1.3.1", "rc-util": "^5.44.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/util": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/@rc-component/util/download/@rc-component/util-1.2.1.tgz", "integrity": "sha1-LDFY8RpBk0eM7ETKQpFdox9n2KA=", "license": "MIT", "dependencies": {"react-is": "^18.2.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}, "node_modules/@remix-run/router": {"version": "1.23.0", "resolved": "http://r.npm.sankuai.com/@remix-run/router/download/@remix-run/router-1.23.0.tgz", "integrity": "sha1-NTkNDnd5YmwCaxE3baZ4nrg4kkI=", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/@sideway/address": {"version": "4.1.5", "resolved": "http://r.npm.sankuai.com/@sideway/address/download/@sideway/address-4.1.5.tgz", "integrity": "sha1-S8FJoAdmI87ZnKggi6eA1lqZudU=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/@sideway/formula/download/@sideway/formula-3.0.1.tgz", "integrity": "sha1-gPy8uvfOAx4O8t0psb/Hw/WDYR8=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz", "integrity": "sha1-z/j/rcNyrSn9P3gneusp5jLMcN8=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sindresorhus/is": {"version": "4.6.0", "resolved": "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-4.6.0.tgz", "integrity": "sha1-PHycRuZ4/u/nouW7YJ09vWZf+z8=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}}, "node_modules/@szmarczak/http-timer": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/@szmarczak/http-timer/download/@szmarczak/http-timer-4.0.6.tgz", "integrity": "sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac=", "dev": true, "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@tokenizer/token": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/@tokenizer/token/download/@tokenizer/token-0.3.0.tgz", "integrity": "sha1-/pipP+eJJH6ZjHXnTpx8YyF6onY=", "license": "MIT"}, "node_modules/@tootallnate/once": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-2.0.0.tgz", "integrity": "sha1-9UShSNOrNYAcH2M6dEH9h8LkhL8=", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/@tootallnate/quickjs-emscripten": {"version": "0.23.0", "resolved": "http://r.npm.sankuai.com/@tootallnate/quickjs-emscripten/download/@tootallnate/quickjs-emscripten-0.23.0.tgz", "integrity": "sha1-207P1JmpdlqyQALDtpbQLm0yoSw=", "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.6", "resolved": "http://r.npm.sankuai.com/@types/body-parser/download/@types/body-parser-1.19.6.tgz", "integrity": "sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/cacheable-request": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/@types/cacheable-request/download/@types/cacheable-request-6.0.3.tgz", "integrity": "sha1-pDCzJgRmyntcpb/XNWk7Nuep0YM=", "dev": true, "license": "MIT", "dependencies": {"@types/http-cache-semantics": "*", "@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "http://r.npm.sankuai.com/@types/connect/download/@types/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.19", "resolved": "http://r.npm.sankuai.com/@types/cors/download/@types/cors-2.8.19.tgz", "integrity": "sha1-2T6iZz/YyfaXNn9e7vwrv6lPA0I=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/debug": {"version": "4.1.12", "resolved": "http://r.npm.sankuai.com/@types/debug/download/@types/debug-4.1.12.tgz", "integrity": "sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=", "dev": true, "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/eslint": {"version": "9.6.1", "resolved": "http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-9.6.1.tgz", "integrity": "sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "resolved": "http://r.npm.sankuai.com/@types/eslint-scope/download/@types/eslint-scope-3.7.7.tgz", "integrity": "sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=", "dev": true, "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.8.tgz", "integrity": "sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "5.0.3", "resolved": "http://r.npm.sankuai.com/@types/express/download/@types/express-5.0.3.tgz", "integrity": "sha1-bEvGrN3C4qWHFC4di+C84gdX6VY=", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.7", "resolved": "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.7.tgz", "integrity": "sha1-L6lIecnUaxGl30x0rHW+/WsoPeY=", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/fs-extra": {"version": "9.0.13", "resolved": "http://r.npm.sankuai.com/@types/fs-extra/download/@types/fs-extra-9.0.13.tgz", "integrity": "sha1-dZT7rgT+fxkYzos9IT90/0SsH0U=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/history": {"version": "4.7.11", "resolved": "http://r.npm.sankuai.com/@types/history/download/@types/history-4.7.11.tgz", "integrity": "sha1-VliLF66PUMU5g6Uk/DzEdDeWnWQ=", "dev": true, "license": "MIT"}, "node_modules/@types/html-minifier-terser": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=", "dev": true, "license": "MIT"}, "node_modules/@types/http-cache-semantics": {"version": "4.0.4", "resolved": "http://r.npm.sankuai.com/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.4.tgz", "integrity": "sha1-uXnrrTkZeZyXmxfHJiHAvAoxxsQ=", "dev": true, "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/@types/http-errors/download/@types/http-errors-2.0.5.tgz", "integrity": "sha1-W3SasrFroRNCP+saZKldzTA5hHI=", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/keyv": {"version": "3.1.4", "resolved": "http://r.npm.sankuai.com/@types/keyv/download/@types/keyv-3.1.4.tgz", "integrity": "sha1-PM2xxnUbDH5SMAvNrNW8v4+qdbY=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/lodash": {"version": "4.17.20", "resolved": "https://registry.npmmirror.com/@types/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==", "dev": true}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/@types/mime/download/@types/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "license": "MIT"}, "node_modules/@types/ms": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/@types/ms/download/@types/ms-2.1.0.tgz", "integrity": "sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "20.19.7", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/plist": {"version": "3.0.5", "resolved": "http://r.npm.sankuai.com/@types/plist/download/@types/plist-3.0.5.tgz", "integrity": "sha1-mgxJwPmIbIyGlqeQTdcD9ihANuA=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "xmlbuilder": ">=11.0.1"}}, "node_modules/@types/prop-types": {"version": "15.7.15", "resolved": "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.15.tgz", "integrity": "sha1-5uWobWAr6spxzlFj+t9fldcJMcc=", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "http://r.npm.sankuai.com/@types/qs/download/@types/qs-6.14.0.tgz", "integrity": "sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "license": "MIT"}, "node_modules/@types/react": {"version": "18.3.23", "resolved": "http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.23.tgz", "integrity": "sha1-hq5va5WkjEGP7NrMyAaeD7tjaWo=", "dev": true, "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.3.7", "resolved": "http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.7.tgz", "integrity": "sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^18.0.0"}}, "node_modules/@types/react-router": {"version": "5.1.20", "resolved": "http://r.npm.sankuai.com/@types/react-router/download/@types/react-router-5.1.20.tgz", "integrity": "sha1-iOzKoSKoJAXvPvvKql3N2fAhOHw=", "dev": true, "license": "MIT", "dependencies": {"@types/history": "^4.7.11", "@types/react": "*"}}, "node_modules/@types/react-router-dom": {"version": "5.3.3", "resolved": "http://r.npm.sankuai.com/@types/react-router-dom/download/@types/react-router-dom-5.3.3.tgz", "integrity": "sha1-6da0pm/NvWUaXxBsJlajAIjMHoM=", "dev": true, "license": "MIT", "dependencies": {"@types/history": "^4.7.11", "@types/react": "*", "@types/react-router": "*"}}, "node_modules/@types/responselike": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/@types/responselike/download/@types/responselike-1.0.3.tgz", "integrity": "sha1-zClwbwo5fP5t+J3r/kv1zqFZ21A=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/send": {"version": "0.17.5", "resolved": "http://r.npm.sankuai.com/@types/send/download/@types/send-0.17.5.tgz", "integrity": "sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.8", "resolved": "http://r.npm.sankuai.com/@types/serve-static/download/@types/serve-static-1.15.8.tgz", "integrity": "sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/turndown": {"version": "5.0.5", "resolved": "http://r.npm.sankuai.com/@types/turndown/download/@types/turndown-5.0.5.tgz", "integrity": "sha1-YU3iT8ms5NjA2Ug7qB3IwZdt0m8=", "license": "MIT"}, "node_modules/@types/uuid": {"version": "8.3.4", "resolved": "https://registry.npmmirror.com/@types/uuid/-/uuid-8.3.4.tgz", "integrity": "sha512-c/I8ZRb51j+pYGAu5CrFMRxqZ2ke4y2grEBO5AUjgSkSk+qT2Ea+OdWElz/OiMf5MNpn2b17kuVBwZLQJXzihw=="}, "node_modules/@types/verror": {"version": "1.10.11", "resolved": "http://r.npm.sankuai.com/@types/verror/download/@types/verror-1.10.11.tgz", "integrity": "sha1-09a0GJeMiqIC1B5bs0gyJ7bswbs=", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/yauzl": {"version": "2.10.3", "resolved": "http://r.npm.sankuai.com/@types/yauzl/download/@types/yauzl-2.10.3.tgz", "integrity": "sha1-6bKAi08QlQSgPNqVglmHb2EBeZk=", "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@ui-tars/action-parser": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/@ui-tars/action-parser/download/@ui-tars/action-parser-1.2.3.tgz", "integrity": "sha1-fwf0wxc8o6Ny3PPd92N0ex4Sv44=", "license": "Apache-2.0", "dependencies": {"@ui-tars/shared": "1.2.3", "lodash.isnumber": "3.0.3"}}, "node_modules/@ui-tars/operator-browser": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/@ui-tars/operator-browser/download/@ui-tars/operator-browser-1.2.3.tgz", "integrity": "sha1-jRMz4psWaA9AvTfvcl//diGEBY8=", "license": "Apache-2.0", "dependencies": {"@agent-infra/browser": "0.1.1", "@agent-infra/logger": "0.0.1", "@ui-tars/sdk": "1.2.3"}}, "node_modules/@ui-tars/sdk": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/@ui-tars/sdk/download/@ui-tars/sdk-1.2.3.tgz", "integrity": "sha1-9EsoCbCWeOb37hwAc8VFl3n0TtQ=", "license": "Apache-2.0", "dependencies": {"@ui-tars/action-parser": "1.2.3", "@ui-tars/shared": "1.2.3", "async-retry": "1.3.3", "jimp": "1.6.0", "openai": "^5.5.1"}}, "node_modules/@ui-tars/shared": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/@ui-tars/shared/download/@ui-tars/shared-1.2.3.tgz", "integrity": "sha1-NocvTTRUBuFe/cYouK1OuXfVLOo=", "license": "Apache-2.0"}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.14.1.tgz", "integrity": "sha1-qfagfysDyVyNOMRTah/ftSH/VbY=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.13.2.tgz", "integrity": "sha1-/Moe7dscxOe27tT8eVbWgTshufs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.13.2.tgz", "integrity": "sha1-4KFhUiSLw42u523X4h8Vxe86sec=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.14.1.tgz", "integrity": "sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.13.2.tgz", "integrity": "sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.13.2.tgz", "integrity": "sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.14.1.tgz", "integrity": "sha1-lindqcRDDqtUtZEFPW3G87oFA0g=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.13.2.tgz", "integrity": "sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=", "dev": true, "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.13.2.tgz", "integrity": "sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=", "dev": true, "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.13.2.tgz", "integrity": "sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.14.1.tgz", "integrity": "sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.14.1.tgz", "integrity": "sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.14.1.tgz", "integrity": "sha1-5vce18yuRngcIGAX08FMUO+oEGs=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.14.1.tgz", "integrity": "sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.14.1.tgz", "integrity": "sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webpack-cli/configtest": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/@webpack-cli/configtest/download/@webpack-cli/configtest-2.1.1.tgz", "integrity": "sha1-Oy+FLpHaxuO4X7KjFPuL70bZRkY=", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}}, "node_modules/@webpack-cli/info": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/@webpack-cli/info/download/@webpack-cli/info-2.0.2.tgz", "integrity": "sha1-zD+/Iu/riP9iMQz4hcWwn0SuD90=", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}}, "node_modules/@webpack-cli/serve": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/@webpack-cli/serve/download/@webpack-cli/serve-2.0.5.tgz", "integrity": "sha1-Ml20I5XNSf5sFAV/mpAOQn34gQ4=", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "resolved": "http://r.npm.sankuai.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.10.tgz", "integrity": "sha1-oTN8pCaqYc75/hW1so40CnL2+pk=", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "resolved": "http://r.npm.sankuai.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "dev": true, "license": "Apache-2.0"}, "node_modules/7zip-bin": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/7zip-bin/download/7zip-bin-5.2.0.tgz", "integrity": "sha1-egMxRoTdZXK336ieaM4x1gKGhU0=", "dev": true, "license": "MIT"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz", "integrity": "sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/accepts/download/accepts-2.0.0.tgz", "integrity": "sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/mime-db": {"version": "1.54.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/mime-types": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-phases": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}, "peerDependencies": {"acorn": "^8.14.0"}}, "node_modules/adler-32": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/adler-32/download/adler-32-1.3.1.tgz", "integrity": "sha1-Hb8LNt2gASGJoys2eQYZMt8YIeI=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/ajv-formats/download/ajv-formats-2.1.1.tgz", "integrity": "sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "license": "MIT"}, "node_modules/ajv-keywords": {"version": "3.5.2", "resolved": "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/antd": {"version": "5.26.4", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.2.1", "@ant-design/cssinjs": "^1.23.0", "@ant-design/cssinjs-utils": "^1.1.3", "@ant-design/fast-color": "^2.0.6", "@ant-design/icons": "^5.6.1", "@ant-design/react-slick": "~1.1.2", "@babel/runtime": "^7.26.0", "@rc-component/color-picker": "~2.0.1", "@rc-component/mutate-observer": "^1.1.0", "@rc-component/qrcode": "~1.0.0", "@rc-component/tour": "~1.15.1", "@rc-component/trigger": "^2.2.7", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "rc-cascader": "~3.34.0", "rc-checkbox": "~3.5.0", "rc-collapse": "~3.9.0", "rc-dialog": "~9.6.0", "rc-drawer": "~7.3.0", "rc-dropdown": "~4.2.1", "rc-field-form": "~2.7.0", "rc-image": "~7.12.0", "rc-input": "~1.8.0", "rc-input-number": "~9.5.0", "rc-mentions": "~2.20.0", "rc-menu": "~9.16.1", "rc-motion": "^2.9.5", "rc-notification": "~5.6.4", "rc-pagination": "~5.1.0", "rc-picker": "~4.11.3", "rc-progress": "~4.0.0", "rc-rate": "~2.13.1", "rc-resize-observer": "^1.4.3", "rc-segmented": "~2.7.0", "rc-select": "~14.16.8", "rc-slider": "~11.1.8", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-table": "~7.51.1", "rc-tabs": "~15.6.1", "rc-textarea": "~1.10.0", "rc-tooltip": "~6.4.0", "rc-tree": "~5.13.1", "rc-tree-select": "~5.27.0", "rc-upload": "~4.9.2", "rc-util": "^5.44.4", "scroll-into-view-if-needed": "^3.1.0", "throttle-debounce": "^5.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/antd/node_modules/@ant-design/colors": {"version": "7.2.1", "resolved": "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.2.1.tgz", "integrity": "sha1-O7wcbBhVACDRYioAZ/8DSSMY35g=", "license": "MIT", "dependencies": {"@ant-design/fast-color": "^2.0.6"}}, "node_modules/antd/node_modules/@ant-design/fast-color": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz", "integrity": "sha1-q01EVcFULJAX02fC+oyj5CFdC6I=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7"}, "engines": {"node": ">=8.x"}}, "node_modules/antd/node_modules/@ant-design/icons": {"version": "5.6.1", "resolved": "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.6.1.tgz", "integrity": "sha1-cpD83D2W/z/KeT7TmQU80prV29M=", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-svg": "^4.4.0", "@babel/runtime": "^7.24.8", "classnames": "^2.2.6", "rc-util": "^5.31.1"}, "engines": {"node": ">=8"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/any-base": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/any-base/download/any-base-1.1.0.tgz", "integrity": "sha1-rhAaYrwIpZe0yatbcInUVmMFSf4=", "license": "MIT"}, "node_modules/app-builder-bin": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/app-builder-bin/download/app-builder-bin-4.0.0.tgz", "integrity": "sha1-HfjmVL0TleSjGdglRcmGZ9fu0vA=", "dev": true, "license": "MIT"}, "node_modules/app-builder-lib": {"version": "24.13.3", "resolved": "http://r.npm.sankuai.com/app-builder-lib/download/app-builder-lib-24.13.3.tgz", "integrity": "sha1-NuR7Zf7Lh4C7c7/w/uTgSAwoJ0s=", "dev": true, "license": "MIT", "dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/notarize": "2.2.1", "@electron/osx-sign": "1.0.5", "@electron/universal": "1.5.1", "@malept/flatpak-bundler": "^0.4.0", "@types/fs-extra": "9.0.13", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.8", "electron-publish": "24.13.1", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^5.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^5.1.1", "read-config-file": "6.3.2", "sanitize-filename": "^1.6.3", "semver": "^7.3.8", "tar": "^6.1.12", "temp-file": "^3.4.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"dmg-builder": "24.13.3", "electron-builder-squirrel-windows": "24.13.3"}}, "node_modules/app-builder-lib/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/app-builder-lib/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/app-builder-lib/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/app-builder-lib/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/archiver": {"version": "5.3.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "engines": {"node": ">= 10"}}, "node_modules/archiver-utils": {"version": "2.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "peer": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/archiver-utils/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/archiver-utils/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/ast-types": {"version": "0.13.4", "resolved": "http://r.npm.sankuai.com/ast-types/download/ast-types-0.13.4.tgz", "integrity": "sha1-7g13s0MmOWXsw/ti2hbnIisrZ4I=", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/astral-regex": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz", "integrity": "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "dev": true, "license": "MIT"}, "node_modules/async-exit-hook": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/async-exit-hook/download/async-exit-hook-2.0.1.tgz", "integrity": "sha1-i9iwJLDsmxwBzMua+dspvXF9+vM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/async-retry": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/async-retry/download/async-retry-1.3.3.tgz", "integrity": "sha1-Dn82wE2EeOeli9vtgM7fl3eF8oA=", "license": "MIT", "dependencies": {"retry": "0.13.1"}}, "node_modules/async-retry/node_modules/retry": {"version": "0.13.1", "resolved": "http://r.npm.sankuai.com/retry/download/retry-0.13.1.tgz", "integrity": "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/at-least-node/download/at-least-node-1.0.0.tgz", "integrity": "sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/atomically": {"version": "1.7.0", "resolved": "https://registry.npmmirror.com/atomically/-/atomically-1.7.0.tgz", "integrity": "sha512-Xcz9l0z7y9yQ9rdDaxlmaI4uJHf/T8g9hOEzJcsEqX2SjCj4J20uK7+ldkDHMbpJDK76wF7xEIgxc/vSlsfw5w==", "engines": {"node": ">=10.12.0"}}, "node_modules/await-to-js": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/await-to-js/download/await-to-js-3.0.0.tgz", "integrity": "sha1-cJKZlBhWFvRnWpGvYWfrYcySho8=", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "engines": {"node": "*"}}, "node_modules/aws-ssl-profiles": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/aws-ssl-profiles/download/aws-ssl-profiles-1.1.2.tgz", "integrity": "sha1-FX3Xfp8ZsdEjZ46T8SDm8ZMCJkE=", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/aws4": {"version": "1.13.2", "resolved": "https://registry.npmmirror.com/aws4/-/aws4-1.13.2.tgz", "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw=="}, "node_modules/axios": {"version": "1.10.0", "resolved": "http://r.npm.sankuai.com/axios/download/axios-1.10.0.tgz", "integrity": "sha1-rzIK7oYy6vKkALahl5+nWFbzjVQ=", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/b4a": {"version": "1.6.7", "resolved": "http://r.npm.sankuai.com/b4a/download/b4a-1.6.7.tgz", "integrity": "sha1-qZWH1Ou/vVpuOyG9tdX6OFdnq+Q=", "license": "Apache-2.0"}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/bare-events": {"version": "2.6.0", "resolved": "http://r.npm.sankuai.com/bare-events/download/bare-events-2.6.0.tgz", "integrity": "sha1-EdlQbaEJ42Oi868FD7sAXM2z7o8=", "license": "Apache-2.0", "optional": true}, "node_modules/bare-fs": {"version": "4.1.6", "resolved": "http://r.npm.sankuai.com/bare-fs/download/bare-fs-4.1.6.tgz", "integrity": "sha1-CSVSHnMQ9lyx8VTKsmTwtkenze8=", "license": "Apache-2.0", "optional": true, "dependencies": {"bare-events": "^2.5.4", "bare-path": "^3.0.0", "bare-stream": "^2.6.4"}, "engines": {"bare": ">=1.16.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}}, "node_modules/bare-os": {"version": "3.6.1", "resolved": "http://r.npm.sankuai.com/bare-os/download/bare-os-3.6.1.tgz", "integrity": "sha1-mSH29Z7b6Br6n1aRBlhCLA9IWNQ=", "license": "Apache-2.0", "optional": true, "engines": {"bare": ">=1.14.0"}}, "node_modules/bare-path": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/bare-path/download/bare-path-3.0.0.tgz", "integrity": "sha1-tZ0YEwulKmr5J22z6WouPT6lIXg=", "license": "Apache-2.0", "optional": true, "dependencies": {"bare-os": "^3.0.1"}}, "node_modules/bare-stream": {"version": "2.6.5", "resolved": "http://r.npm.sankuai.com/bare-stream/download/bare-stream-2.6.5.tgz", "integrity": "sha1-u6joeWdMTCf34ngF3wBcFdeiygc=", "license": "Apache-2.0", "optional": true, "dependencies": {"streamx": "^2.21.0"}, "peerDependencies": {"bare-buffer": "*", "bare-events": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}, "bare-events": {"optional": true}}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/basic-ftp": {"version": "5.0.5", "resolved": "http://r.npm.sankuai.com/basic-ftp/download/basic-ftp-5.0.5.tgz", "integrity": "sha1-FKR09f/+zKH09AbxwmsY+AAiWsA=", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bl": {"version": "4.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "dev": true, "license": "MIT"}, "node_modules/bluebird-lst": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/bluebird-lst/download/bluebird-lst-1.0.9.tgz", "integrity": "sha1-pkoOQ2Vli5q1/odeud+2lBibtBw=", "dev": true, "license": "MIT", "dependencies": {"bluebird": "^3.5.5"}}, "node_modules/bmp-ts": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/bmp-ts/download/bmp-ts-1.0.9.tgz", "integrity": "sha1-D9EkuoEr6beGsp5bGG7nbXT/VTg=", "license": "MIT"}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/body-parser/download/body-parser-2.2.0.tgz", "integrity": "sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true, "license": "ISC"}, "node_modules/boolean": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/boolean/download/boolean-3.2.0.tgz", "integrity": "sha1-nlKUr06YMUSUy7F5efpUyhWfEWs=", "dev": true, "license": "MIT", "optional": true}, "node_modules/brace-expansion": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "http://r.npm.sankuai.com/browserslist/download/browserslist-4.25.1.tgz", "integrity": "sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-equal": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/buffer-equal/download/buffer-equal-1.0.1.tgz", "integrity": "sha1-L3ZRvlsbPwV/zW5+4WzzR2cHfZA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "dev": true, "license": "MIT"}, "node_modules/builder-util": {"version": "24.13.1", "resolved": "http://r.npm.sankuai.com/builder-util/download/builder-util-24.13.1.tgz", "integrity": "sha1-SkxPlGawFrhcaZCg6hWqFO3saBY=", "dev": true, "license": "MIT", "dependencies": {"@types/debug": "^4.1.6", "7zip-bin": "~5.2.0", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0"}}, "node_modules/builder-util-runtime": {"version": "9.2.4", "resolved": "http://r.npm.sankuai.com/builder-util-runtime/download/builder-util-runtime-9.2.4.tgz", "integrity": "sha1-E80XY9piHlNFhzmh5j9/y6ZzxCo=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/builder-util/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/builder-util/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/builder-util/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacheable-lookup": {"version": "5.0.4", "resolved": "http://r.npm.sankuai.com/cacheable-lookup/download/cacheable-lookup-5.0.4.tgz", "integrity": "sha1-WmuGWyxENXvj1evCpGewMnGacAU=", "dev": true, "license": "MIT", "engines": {"node": ">=10.6.0"}}, "node_modules/cacheable-request": {"version": "7.0.4", "resolved": "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-7.0.4.tgz", "integrity": "sha1-ejPr8IYTF4tANjW+e4mdPmm76Bc=", "dev": true, "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camel-case": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/camel-case/download/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "dev": true, "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/caniuse-lite": {"version": "1.0.30001727", "resolved": "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001727.tgz", "integrity": "sha1-IulwZCKtN6pQVWr4wQ5A4tk6i4U=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/caseless": {"version": "0.12.0", "resolved": "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="}, "node_modules/cfb": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/cfb/download/cfb-1.2.2.tgz", "integrity": "sha1-lOaHYoxwDlFVQ22sBfdOCN8jvEQ=", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "engines": {"node": ">=0.8"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chokidar": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/chokidar/download/chokidar-4.0.3.tgz", "integrity": "sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=", "dev": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz", "integrity": "sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/chromium-bidi": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/chromium-bidi/download/chromium-bidi-4.1.1.tgz", "integrity": "sha1-4cNBVN3ZRHPxgP0VFYok02BJ49U=", "license": "Apache-2.0", "dependencies": {"mitt": "^3.0.1", "zod": "^3.24.1"}, "peerDependencies": {"devtools-protocol": "*"}}, "node_modules/chromium-pickle-js": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/chromium-pickle-js/download/chromium-pickle-js-0.2.0.tgz", "integrity": "sha1-BKEGZywYsIWrd02YPfo+oTjyIgU=", "dev": true, "license": "MIT"}, "node_modules/ci-info": {"version": "3.9.0", "resolved": "http://r.npm.sankuai.com/ci-info/download/ci-info-3.9.0.tgz", "integrity": "sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/classnames": {"version": "2.5.1", "resolved": "http://r.npm.sankuai.com/classnames/download/classnames-2.5.1.tgz", "integrity": "sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=", "license": "MIT"}, "node_modules/clean-css": {"version": "5.3.3", "resolved": "http://r.npm.sankuai.com/clean-css/download/clean-css-5.3.3.tgz", "integrity": "sha1-szBlPNO9a3UAnMJccUyue5M1HM0=", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "node_modules/cli-truncate": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-2.1.0.tgz", "integrity": "sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "http://r.npm.sankuai.com/cliui/download/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone-deep": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/clone-response": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.3.tgz", "integrity": "sha1-ryAyqkeBY5nPXwodDbkC9ReruMM=", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/codepage": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/codepage/download/codepage-1.15.0.tgz", "integrity": "sha1-LgBRkCSzlCTsZu6z7AcifmkmGKs=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/color": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/color/download/color-4.2.3.tgz", "integrity": "sha1-14HsteVyJO5D6pYnVgEHwODGRjo=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz", "integrity": "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/colorette": {"version": "2.0.20", "resolved": "http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz", "integrity": "sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/commander/download/commander-5.1.0.tgz", "integrity": "sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/compare-version": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/compare-version/download/compare-version-0.1.2.tgz", "integrity": "sha1-AWLsLZNR9d3VmpICy6k1NmpyUIA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/compress-commons": {"version": "4.1.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/compute-scroll-into-view": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-3.1.1.tgz", "integrity": "sha1-AsM4bsUx+2qYgZZziOU+hWTz6ao=", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/concurrently": {"version": "8.2.2", "resolved": "http://r.npm.sankuai.com/concurrently/download/concurrently-8.2.2.tgz", "integrity": "sha1-NTFBmFwZjPpeSj75AILDNrWFF4Q=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "rxjs": "^7.8.1", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1", "tree-kill": "^1.2.2", "yargs": "^17.7.2"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "engines": {"node": "^14.13.0 || >=16.0.0"}, "funding": {"url": "https://github.com/open-cli-tools/concurrently?sponsor=1"}}, "node_modules/concurrently/node_modules/supports-color": {"version": "8.1.1", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/conf": {"version": "10.2.0", "resolved": "https://registry.npmmirror.com/conf/-/conf-10.2.0.tgz", "integrity": "sha512-8fLl9F04EJqjSqH+QjITQfJF8BrOVaYr1jewVgSRAEWePfxT0sku4w2hrGQ60BC/TNLGQ2pgxNlTbWQmMPFvXg==", "dependencies": {"ajv": "^8.6.3", "ajv-formats": "^2.1.1", "atomically": "^1.7.0", "debounce-fn": "^4.0.0", "dot-prop": "^6.0.1", "env-paths": "^2.2.1", "json-schema-typed": "^7.0.3", "onetime": "^5.1.2", "pkg-up": "^3.1.0", "semver": "^7.3.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/conf/node_modules/ajv": {"version": "8.17.1", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/conf/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="}, "node_modules/conf/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/config-file-ts": {"version": "0.2.6", "resolved": "http://r.npm.sankuai.com/config-file-ts/download/config-file-ts-0.2.6.tgz", "integrity": "sha1-tCT/dGEvs39ibWUo8I+S3fXSICc=", "dev": true, "license": "MIT", "dependencies": {"glob": "^10.3.10", "typescript": "^5.3.3"}}, "node_modules/config-file-ts/node_modules/glob": {"version": "10.4.5", "resolved": "http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz", "integrity": "sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/config-file-ts/node_modules/minimatch": {"version": "9.0.5", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/config-file-ts/node_modules/minipass": {"version": "7.1.2", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz", "integrity": "sha1-k6libOXl5mvU24aEnnUV6SNApwc=", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/content-disposition": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/content-disposition/download/content-disposition-1.0.0.tgz", "integrity": "sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.2", "resolved": "http://r.npm.sankuai.com/cookie/download/cookie-0.7.2.tgz", "integrity": "sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.2.2.tgz", "integrity": "sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/copy-to-clipboard": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz", "integrity": "sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=", "license": "MIT", "dependencies": {"toggle-selection": "^1.0.6"}}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "http://r.npm.sankuai.com/cors/download/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cos-nodejs-sdk-v5": {"version": "2.16.0-beta.3", "resolved": "https://registry.npmmirror.com/cos-nodejs-sdk-v5/-/cos-nodejs-sdk-v5-2.16.0-beta.3.tgz", "integrity": "sha512-YKrnGBw790JNavsAocFKWzYG+m7ZVdtjWJYt+0lrBnJN7JvO/6fi1PCrh1iyT95ZHJHHBjuJxSAq+SP0oRnvIg==", "dependencies": {"conf": "^9.0.0", "cos-request": "^1.0.0", "fast-xml-parser": "4.2.5", "mime-types": "^2.1.35"}, "engines": {"node": ">= 9"}}, "node_modules/cos-nodejs-sdk-v5/node_modules/ajv": {"version": "7.2.4", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-7.2.4.tgz", "integrity": "sha512-nBeQgg/ZZA3u3SYxyaDvpvDtgZ/EZPF547ARgZBrG9Bhu1vKDwAIjtIf+sDtJUKa2zOcEbmRLBRSyMraS/Oy1A==", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/cos-nodejs-sdk-v5/node_modules/ajv-formats": {"version": "1.6.1", "resolved": "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-1.6.1.tgz", "integrity": "sha512-4CjkH20If1lhR5CGtqkrVg3bbOtFEG80X9v6jDOIUhbzzbB+UzPBGy8GQhUNVZ0yvMHdMpawCOcy5ydGMsagGQ==", "dependencies": {"ajv": "^7.0.0"}, "peerDependencies": {"ajv": "^7.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/cos-nodejs-sdk-v5/node_modules/conf": {"version": "9.0.2", "resolved": "https://registry.npmmirror.com/conf/-/conf-9.0.2.tgz", "integrity": "sha512-rLSiilO85qHgaTBIIHQpsv8z+NnVfZq3cKuYNCXN1AOqPzced0GWZEe/A517VldRLyQYXUMyV+vszavE2jSAqw==", "dependencies": {"ajv": "^7.0.3", "ajv-formats": "^1.5.1", "atomically": "^1.7.0", "debounce-fn": "^4.0.0", "dot-prop": "^6.0.1", "env-paths": "^2.2.0", "json-schema-typed": "^7.0.3", "make-dir": "^3.1.0", "onetime": "^5.1.2", "pkg-up": "^3.1.0", "semver": "^7.3.4"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cos-nodejs-sdk-v5/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="}, "node_modules/cos-nodejs-sdk-v5/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/cos-request": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/cos-request/-/cos-request-1.0.0.tgz", "integrity": "sha512-f5YUT8L/Xc6ZBsXKkEhyd7NXy9gfEf0QZlBnXVC7S29AwGdIweRlvT9Ba6jRypOAPCmYNZfgC76szc38GshJXA==", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.13.0", "safe-buffer": "^5.1.2", "tough-cookie": "~4.1.4", "tunnel-agent": "^0.6.0", "uuid": "^8.3.2"}, "engines": {"node": ">= 8"}}, "node_modules/cos-request/node_modules/form-data": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/cos-request/node_modules/qs": {"version": "6.13.1", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.13.1.tgz", "integrity": "sha512-EJPeIn0CYrGu+hli1xilKAPXODtJ12T0sP63Ijx2/khC2JtuaN3JyNIpvmnkmaEtha9ocbG4A4cMcr+TvqvwQg==", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/crc": {"version": "3.8.0", "resolved": "http://r.npm.sankuai.com/crc/download/crc-3.8.0.tgz", "integrity": "sha1-rWAmnCyFb4wpnixMwN5FVpFAVsY=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.1.0"}}, "node_modules/crc-32": {"version": "1.2.2", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/crc32-stream": {"version": "4.0.3", "dev": true, "license": "MIT", "peer": true, "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 10"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-loader": {"version": "6.11.0", "resolved": "http://r.npm.sankuai.com/css-loader/download/css-loader-6.11.0.tgz", "integrity": "sha1-M7rjv2Nj0KfCz5AxyWx0T/VNhbo=", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/css-loader/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/css-select": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/css-select/download/css-select-4.3.0.tgz", "integrity": "sha1-23EpsoRmYv2GKM/ElquytZ5BUps=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-what": {"version": "6.2.2", "resolved": "http://r.npm.sankuai.com/css-what/download/css-what-6.2.2.tgz", "integrity": "sha1-zcyPm2l3cZ/fvR3nrsJKv3Vrneo=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-uri-to-buffer": {"version": "6.0.2", "resolved": "http://r.npm.sankuai.com/data-uri-to-buffer/download/data-uri-to-buffer-6.0.2.tgz", "integrity": "sha1-ili7ZzhLJho47xi+oYEMsBut0os=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/date-fns": {"version": "2.30.0", "resolved": "http://r.npm.sankuai.com/date-fns/download/date-fns-2.30.0.tgz", "integrity": "sha1-82fmRIOf9XiU7GrEgN5AyuSw9NA=", "devOptional": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw=", "license": "MIT"}, "node_modules/debounce-fn": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/debounce-fn/-/debounce-fn-4.0.0.tgz", "integrity": "sha512-8pYCQiL9Xdcg0UPSD3d+0KMlOjp+KGU5EPwYddgzQ7DATsg4fuUDjQtsYLmWjnk2obnNHgV3vE2Y4jejSOJVBQ==", "dependencies": {"mimic-fn": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/debounce-fn/node_modules/mimic-fn": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-3.1.0.tgz", "integrity": "sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==", "engines": {"node": ">=8"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decompress-response": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/decompress-response/download/decompress-response-6.0.0.tgz", "integrity": "sha1-yjh2Et234QS9FthaqwDV7PCcZvw=", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decompress-response/node_modules/mimic-response": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/mimic-response/download/mimic-response-3.1.0.tgz", "integrity": "sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/defer-to-connect": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/defer-to-connect/download/defer-to-connect-2.0.1.tgz", "integrity": "sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/degenerator": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/degenerator/download/degenerator-5.0.1.tgz", "integrity": "sha1-lAO/KXxtrZoezkCbN9snlU+R8vU=", "license": "MIT", "dependencies": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/denque": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/denque/download/denque-2.1.0.tgz", "integrity": "sha1-6T4aZWn7XmbxajwqKWRhfTSdarE=", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/detect-libc": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/detect-libc/download/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "dev": true, "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/detect-node": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/detect-node/download/detect-node-2.1.0.tgz", "integrity": "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=", "dev": true, "license": "MIT", "optional": true}, "node_modules/devtools-protocol": {"version": "0.0.1425554", "resolved": "http://r.npm.sankuai.com/devtools-protocol/download/devtools-protocol-0.0.1425554.tgz", "integrity": "sha1-Ue0v7RQF9WeD0ko5P3x1tru1gCk=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/dir-compare": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/dir-compare/download/dir-compare-3.3.0.tgz", "integrity": "sha1-LHSflztcS10IfxHtqucw2zF4hBY=", "dev": true, "license": "MIT", "dependencies": {"buffer-equal": "^1.0.0", "minimatch": "^3.0.4"}}, "node_modules/dir-compare/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/dir-compare/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/dmg-builder": {"version": "24.13.3", "resolved": "http://r.npm.sankuai.com/dmg-builder/download/dmg-builder-24.13.3.tgz", "integrity": "sha1-ldW5nFh8WS+Q0WimFtfsVZB8flU=", "dev": true, "license": "MIT", "dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "fs-extra": "^10.1.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0"}, "optionalDependencies": {"dmg-license": "^1.0.11"}}, "node_modules/dmg-builder/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/dmg-builder/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/dmg-builder/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/dmg-license": {"version": "1.0.11", "resolved": "http://r.npm.sankuai.com/dmg-license/download/dmg-license-1.0.11.tgz", "integrity": "sha1-ezvDdF0bUr51BrTugMth325M15o=", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"@types/plist": "^3.0.1", "@types/verror": "^1.10.3", "ajv": "^6.10.0", "crc": "^3.8.0", "iconv-corefoundation": "^1.1.7", "plist": "^3.0.4", "smart-buffer": "^4.0.2", "verror": "^1.10.0"}, "bin": {"dmg-license": "bin/dmg-license.js"}, "engines": {"node": ">=8"}}, "node_modules/dom-converter": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/dom-converter/download/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "dev": true, "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/dom-serializer": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-1.4.1.tgz", "integrity": "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/domhandler/download/domhandler-4.3.1.tgz", "integrity": "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "resolved": "http://r.npm.sankuai.com/domutils/download/domutils-2.8.0.tgz", "integrity": "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/dot-case/download/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dot-prop": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/dot-prop/-/dot-prop-6.0.1.tgz", "integrity": "sha512-tE7ztYzXHIeyvc7N+hR3oi7FIbf/NIjVP9hmAt3yMXzrQ072/fpjGLx2GxNxGxUl5V73MEqYzioOMoVhGMJ5cA==", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dotenv": {"version": "9.0.2", "resolved": "http://r.npm.sankuai.com/dotenv/download/dotenv-9.0.2.tgz", "integrity": "sha1-2swgFgk1o33qY2SqG++Bn7m2qwU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz", "integrity": "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz", "integrity": "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=", "dev": true, "license": "MIT"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "resolved": "http://r.npm.sankuai.com/ejs/download/ejs-3.1.10.tgz", "integrity": "sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=", "dev": true, "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron": {"version": "27.3.11", "resolved": "http://r.npm.sankuai.com/electron/download/electron-27.3.11.tgz", "integrity": "sha1-ryFa8pZN2mEuQBrbbiF/30dHL8o=", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@electron/get": "^2.0.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1"}, "bin": {"electron": "cli.js"}, "engines": {"node": ">= 12.20.55"}}, "node_modules/electron-builder": {"version": "24.13.3", "resolved": "http://r.npm.sankuai.com/electron-builder/download/electron-builder-24.13.3.tgz", "integrity": "sha1-xQbf69NtmlCoPuiqMtgD2D2+RhY=", "dev": true, "license": "MIT", "dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "dmg-builder": "24.13.3", "fs-extra": "^10.1.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.3.2", "simple-update-notifier": "2.0.0", "yargs": "^17.6.2"}, "bin": {"electron-builder": "cli.js", "install-app-deps": "install-app-deps.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/electron-builder-squirrel-windows": {"version": "24.13.3", "dev": true, "license": "MIT", "peer": true, "dependencies": {"app-builder-lib": "24.13.3", "archiver": "^5.3.1", "builder-util": "24.13.1", "fs-extra": "^10.1.0"}}, "node_modules/electron-builder-squirrel-windows/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "peer": true, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-builder-squirrel-windows/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "peer": true, "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-builder-squirrel-windows/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-builder/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-builder/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-builder/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-dl": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/electron-dl/-/electron-dl-4.0.0.tgz", "integrity": "sha512-USiB9816d2JzKv0LiSbreRfTg5lDk3lWh0vlx/gugCO92ZIJkHVH0UM18EHvKeadErP6Xn4yiTphWzYfbA2Ong==", "dependencies": {"ext-name": "^5.0.0", "pupa": "^3.1.0", "unused-filename": "^4.0.1"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/electron-publish": {"version": "24.13.1", "resolved": "http://r.npm.sankuai.com/electron-publish/download/electron-publish-24.13.1.tgz", "integrity": "sha1-VyibL3rxhzfcKtE0ZozdShtXSgw=", "dev": true, "license": "MIT", "dependencies": {"@types/fs-extra": "^9.0.11", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "fs-extra": "^10.1.0", "lazy-val": "^1.0.5", "mime": "^2.5.2"}}, "node_modules/electron-publish/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-publish/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-publish/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-store": {"version": "8.2.0", "resolved": "https://registry.npmmirror.com/electron-store/-/electron-store-8.2.0.tgz", "integrity": "sha512-ukLL5Bevdil6oieAOXz3CMy+OgaItMiVBg701MNlG6W5RaC0AHN7rvlqTCmeb6O7jP0Qa1KKYTE0xV0xbhF4Hw==", "dependencies": {"conf": "^10.2.0", "type-fest": "^2.17.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/electron-store/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://registry.npmmirror.com/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/electron-to-chromium": {"version": "1.5.182", "dev": true, "license": "ISC"}, "node_modules/electron/node_modules/@types/node": {"version": "18.19.119", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-18.19.119.tgz", "integrity": "sha1-58IJi4wCQ68ABVA6bV2pLg2YnIQ=", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/electron/node_modules/undici-types": {"version": "5.26.5", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/encodeurl/download/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.5", "resolved": "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.5.tgz", "integrity": "sha1-c0TXEd6kDgt0q8LtSXeHQ8ztsIw=", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "5.18.2", "resolved": "http://r.npm.sankuai.com/enhanced-resolve/download/enhanced-resolve-5.18.2.tgz", "integrity": "sha1-eQPFsy/9SyFD7rS5JHK9aO/9VGQ=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/entities": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/entities/download/entities-2.2.0.tgz", "integrity": "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/env-paths/download/env-paths-2.2.1.tgz", "integrity": "sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/envinfo": {"version": "7.14.0", "resolved": "http://r.npm.sankuai.com/envinfo/download/envinfo-7.14.0.tgz", "integrity": "sha1-JtrF21RBjypMEVkVOgsq6YCDiq4=", "dev": true, "license": "MIT", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/err-code": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/err-code/download/err-code-2.0.3.tgz", "integrity": "sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=", "dev": true, "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "http://r.npm.sankuai.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz", "integrity": "sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es6-error": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/es6-error/download/es6-error-4.1.1.tgz", "integrity": "sha1-njr0B0Wd7tR+mpH5uIWoTrBcVh0=", "dev": true, "license": "MIT", "optional": true}, "node_modules/escalade": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-goat": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/escape-goat/-/escape-goat-4.0.0.tgz", "integrity": "sha512-2Sd4ShcWxbx6OY1IHyla/CVNwvg7XwZVoXZHcSu9w9SReNP1EzzD5T8NWKIR38fIqEns9kDWKUQTXXAmlDrdPg==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/escodegen/download/escodegen-2.1.0.tgz", "integrity": "sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-scope": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz", "integrity": "sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/events/download/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/exif-parser": {"version": "0.1.12", "resolved": "http://r.npm.sankuai.com/exif-parser/download/exif-parser-0.1.12.tgz", "integrity": "sha1-WKnS1ywCwfbwKg70qRZicrd2CSI="}, "node_modules/express": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/express/download/express-5.1.0.tgz", "integrity": "sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/mime-db": {"version": "1.54.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/mime-types": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/ext-list": {"version": "2.2.2", "resolved": "https://registry.npmmirror.com/ext-list/-/ext-list-2.2.2.tgz", "integrity": "sha512-u+SQgsubraE6zItfVA0tBuCBhfU9ogSRnsvygI7wht9TS510oLkBRXBsqopeUG/GBOIQyKZO9wjTqIu/sf5zFA==", "dependencies": {"mime-db": "^1.28.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ext-name": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/ext-name/-/ext-name-5.0.0.tgz", "integrity": "sha512-yblEwXAbGv1VQDmow7s38W77hzAgJAO50ztBLMcUyUBfxv1HC+LGwtiEN+Co6LtlqT/5uwVOxsD4TNIilWhwdQ==", "dependencies": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "node_modules/extract-zip": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/extract-zip/download/extract-zip-2.0.1.tgz", "integrity": "sha1-Zj3KVv5G34kNXxMe9KBtIruLoTo=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/extsprintf": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.4.1.tgz", "integrity": "sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "optional": true}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-fifo": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/fast-fifo/download/fast-fifo-1.3.2.tgz", "integrity": "sha1-KG4x3pbrltOKl4mYFXQLoqTzZAw=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.6", "resolved": "http://r.npm.sankuai.com/fast-uri/download/fast-uri-3.0.6.tgz", "integrity": "sha1-iPEwt3z66iN41Wv5cN6iElemh0g=", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fast-xml-parser": {"version": "4.2.5", "resolved": "https://registry.npmmirror.com/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz", "integrity": "sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==", "funding": [{"type": "paypal", "url": "https://paypal.me/naturalintelligence"}, {"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "dependencies": {"strnum": "^1.0.5"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/fastest-levenshtein": {"version": "1.0.16", "resolved": "http://r.npm.sankuai.com/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz", "integrity": "sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4.9.1"}}, "node_modules/fd-slicer": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/fd-slicer/download/fd-slicer-1.1.0.tgz", "integrity": "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=", "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/file-type": {"version": "16.5.4", "resolved": "http://r.npm.sankuai.com/file-type/download/file-type-16.5.4.tgz", "integrity": "sha1-R0+09wS+5CdoH5jdOQBYoXKmwv0=", "license": "MIT", "dependencies": {"readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/file-type?sponsor=1"}}, "node_modules/filelist": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz", "integrity": "sha1-94l4oelEd1/55i50RCTyFeWDUrU=", "dev": true, "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/finalhandler/download/finalhandler-2.1.0.tgz", "integrity": "sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "resolved": "http://r.npm.sankuai.com/flat/download/flat-5.0.2.tgz", "integrity": "sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz", "integrity": "sha1-Mujp7Rtoo0l777msK2rfkqY4V28=", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/frac": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/frac/download/frac-1.1.2.tgz", "integrity": "sha1-PXT39keMiKG1AgMG10fcYxPHTQs=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/fresh": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/fresh/download/fresh-2.0.0.tgz", "integrity": "sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/fs-extra": {"version": "8.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz", "integrity": "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/generate-function": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/generate-function/download/generate-function-2.3.1.tgz", "integrity": "sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=", "license": "MIT", "dependencies": {"is-property": "^1.0.2"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz", "integrity": "sha1-SWaheV7lrOZecGxLe+txJX1uItM=", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-uri": {"version": "6.0.4", "resolved": "http://r.npm.sankuai.com/get-uri/download/get-uri-6.0.4.tgz", "integrity": "sha1-barunhL5dZ4Z5VujE5Vog+9Q4Kc=", "license": "MIT", "dependencies": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/gifwrap": {"version": "0.10.1", "resolved": "http://r.npm.sankuai.com/gifwrap/download/gifwrap-0.10.1.tgz", "integrity": "sha1-ntRqXVGRO0gtQiHOnHJwgCYLaB4=", "license": "MIT", "dependencies": {"image-q": "^4.0.0", "omggif": "^1.0.10"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz", "integrity": "sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/global-agent": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/global-agent/download/global-agent-3.0.0.tgz", "integrity": "sha1-rnzTG9NYO5PFoWQ3oa/ifMM6GrY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "es6-error": "^4.1.1", "matcher": "^3.0.0", "roarr": "^2.15.3", "semver": "^7.3.2", "serialize-error": "^7.0.1"}, "engines": {"node": ">=10.0"}}, "node_modules/global-agent/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/globalthis": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz", "integrity": "sha1-dDDtOpddl7+1m8zkH1yruvplEjY=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "11.8.6", "resolved": "http://r.npm.sankuai.com/got/download/got-11.8.6.tgz", "integrity": "sha1-J26Cfq2Hcu3bz8lxcFkLhBgjIzo=", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^4.0.0", "@szmarczak/http-timer": "^4.0.5", "@types/cacheable-request": "^6.0.1", "@types/responselike": "^1.0.0", "cacheable-lookup": "^5.0.3", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "http2-wrapper": "^1.0.0-beta.5.2", "lowercase-keys": "^2.0.0", "p-cancelable": "^2.0.0", "responselike": "^2.0.0"}, "engines": {"node": ">=10.19.0"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hosted-git-info": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-4.1.0.tgz", "integrity": "sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/html-minifier-terser": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "node_modules/html-minifier-terser/node_modules/commander": {"version": "8.3.0", "resolved": "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz", "integrity": "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=", "dev": true, "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/html-webpack-plugin": {"version": "5.6.3", "resolved": "http://r.npm.sankuai.com/html-webpack-plugin/download/html-webpack-plugin-5.6.3.tgz", "integrity": "sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=", "dev": true, "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.20.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/htmlparser2": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-6.1.0.tgz", "integrity": "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/http-cache-semantics": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-4.2.0.tgz", "integrity": "sha1-IF9Ntk+FYrdqT/kjWqUnmDmgndU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy-agent": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-5.0.0.tgz", "integrity": "sha1-USmAAgNSDUNPFCvHj/PBcIAPK0M=", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/http2-wrapper": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/http2-wrapper/download/http2-wrapper-1.0.3.tgz", "integrity": "sha1-uPVeDB8l1OvQizsMLAeflZCACz0=", "dev": true, "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz", "integrity": "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/iconv-corefoundation": {"version": "1.1.7", "resolved": "http://r.npm.sankuai.com/iconv-corefoundation/download/iconv-corefoundation-1.1.7.tgz", "integrity": "sha1-MQZearLJJyFUyLCCEVHiyI8bACo=", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"cli-truncate": "^2.1.0", "node-addon-api": "^1.6.3"}, "engines": {"node": "^8.11.2 || >=10"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/icss-utils/download/icss-utils-5.1.0.tgz", "integrity": "sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/image-q": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/image-q/download/image-q-4.0.0.tgz", "integrity": "sha1-MeB1vnuuPB9CqFxGm0cyw1iYF3Y=", "license": "MIT", "dependencies": {"@types/node": "16.9.1"}}, "node_modules/image-q/node_modules/@types/node": {"version": "16.9.1", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-16.9.1.tgz", "integrity": "sha1-BhGzfbQkbJN/7vUp3cwBjPjjVwg=", "license": "MIT"}, "node_modules/immutable": {"version": "5.1.3", "dev": true, "license": "MIT"}, "node_modules/import-local": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/import-local/download/import-local-3.2.0.tgz", "integrity": "sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/interpret": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/interpret/download/interpret-3.1.1.tgz", "integrity": "sha1-W+DO7WfKecbEvFzw1+6EPc6hEMQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/ip-address": {"version": "9.0.5", "resolved": "http://r.npm.sankuai.com/ip-address/download/ip-address-9.0.5.tgz", "integrity": "sha1-EXqWCBmwh4DDvR8U7zwcwdPz6lo=", "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/jsbn": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/jsbn/download/jsbn-1.1.0.tgz", "integrity": "sha1-sBMHyym2GKHtJux56RH4A8TaAEA=", "license": "MIT"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "integrity": "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=", "license": "MIT"}, "node_modules/is-ci": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/is-ci/download/is-ci-3.0.1.tgz", "integrity": "sha1-227L7RvWWcQ9rA9FZh52dBA9GGc=", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^3.2.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-obj": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-obj/-/is-obj-2.0.0.tgz", "integrity": "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "integrity": "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==", "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/is-promise/download/is-promise-4.0.0.tgz", "integrity": "sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=", "license": "MIT"}, "node_modules/is-property": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-property/download/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=", "license": "MIT"}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/isbinaryfile": {"version": "5.0.4", "resolved": "http://r.npm.sankuai.com/isbinaryfile/download/isbinaryfile-5.0.4.tgz", "integrity": "sha1-Ki7e+nbK+mZhP+TB6lL38DEBe98=", "dev": true, "license": "MIT", "engines": {"node": ">= 18.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="}, "node_modules/jackspeak": {"version": "3.4.3", "resolved": "http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz", "integrity": "sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "resolved": "http://r.npm.sankuai.com/jake/download/jake-10.9.2.tgz", "integrity": "sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=", "dev": true, "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/jake/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/jest-worker": {"version": "27.5.1", "resolved": "http://r.npm.sankuai.com/jest-worker/download/jest-worker-27.5.1.tgz", "integrity": "sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/jimp": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/jimp/download/jimp-1.6.0.tgz", "integrity": "sha1-fH5RM8jcBnBuHtNedxxoWvOTv9I=", "license": "MIT", "dependencies": {"@jimp/core": "1.6.0", "@jimp/diff": "1.6.0", "@jimp/js-bmp": "1.6.0", "@jimp/js-gif": "1.6.0", "@jimp/js-jpeg": "1.6.0", "@jimp/js-png": "1.6.0", "@jimp/js-tiff": "1.6.0", "@jimp/plugin-blit": "1.6.0", "@jimp/plugin-blur": "1.6.0", "@jimp/plugin-circle": "1.6.0", "@jimp/plugin-color": "1.6.0", "@jimp/plugin-contain": "1.6.0", "@jimp/plugin-cover": "1.6.0", "@jimp/plugin-crop": "1.6.0", "@jimp/plugin-displace": "1.6.0", "@jimp/plugin-dither": "1.6.0", "@jimp/plugin-fisheye": "1.6.0", "@jimp/plugin-flip": "1.6.0", "@jimp/plugin-hash": "1.6.0", "@jimp/plugin-mask": "1.6.0", "@jimp/plugin-print": "1.6.0", "@jimp/plugin-quantize": "1.6.0", "@jimp/plugin-resize": "1.6.0", "@jimp/plugin-rotate": "1.6.0", "@jimp/plugin-threshold": "1.6.0", "@jimp/types": "1.6.0", "@jimp/utils": "1.6.0"}, "engines": {"node": ">=18"}}, "node_modules/joi": {"version": "17.13.3", "resolved": "http://r.npm.sankuai.com/joi/download/joi-17.13.3.tgz", "integrity": "sha1-D1zBFpyZmzDTRDZtOEsS2SVYvOw=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/jose": {"version": "5.10.0", "resolved": "http://r.npm.sankuai.com/jose/download/jose-5.10.0.tgz", "integrity": "sha1-w3NGoJnWRnxAE1GpoMIWHg9SxL4=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/jpeg-js": {"version": "0.4.4", "resolved": "http://r.npm.sankuai.com/jpeg-js/download/jpeg-js-0.4.4.tgz", "integrity": "sha1-qfHG8fnw+oDNs0hO2WNQVNKJNqo=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true, "license": "MIT"}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-schema-typed": {"version": "7.0.3", "resolved": "https://registry.npmmirror.com/json-schema-typed/-/json-schema-typed-7.0.3.tgz", "integrity": "sha512-7DE8mpG+/fVw+dTpjbxnx47TaMnDfOI1jwft9g1VybltZCduyRQPJPvc+zzKY9WPHxhPWczyFuYa6I8Mw4iU5A=="}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "license": "ISC"}, "node_modules/json2mq": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz", "integrity": "sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=", "license": "MIT", "dependencies": {"string-convert": "^0.2.0"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/jsprim/node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "engines": ["node >=0.6.0"]}, "node_modules/jsprim/node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/lazy-val": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/lazy-val/download/lazy-val-1.0.5.tgz", "integrity": "sha1-bPO59bwxzufuPjacCDK3WD3Nkj0=", "dev": true, "license": "MIT"}, "node_modules/lazystream": {"version": "1.0.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lazystream/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "peer": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/lazystream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/lazystream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/loader-runner": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/loader-runner/download/loader-runner-4.3.0.tgz", "integrity": "sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=", "dev": true, "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash.defaults": {"version": "4.2.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.difference": {"version": "4.5.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.flatten": {"version": "4.4.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz", "integrity": "sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.union": {"version": "4.6.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/long": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/long/download/long-5.3.2.tgz", "integrity": "sha1-HYRGMJWZkmLX17f4v9SozFUWf4M=", "license": "Apache-2.0"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/lower-case/download/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lowercase-keys": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz", "integrity": "sha1-JgPni3tLAAbLyi+8yKMgJVislHk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lru.min": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/lru.min/download/lru.min-1.1.2.tgz", "integrity": "sha1-Ac4dcsxQx/r4vR+Anr8F1DMQIes=", "license": "MIT", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/matcher": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/matcher/download/matcher-3.0.0.tgz", "integrity": "sha1-vZBg9MW3CqgEHMxvgDaHYJlPMMo=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"escape-string-regexp": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/media-typer/download/media-typer-1.1.0.tgz", "integrity": "sha1-ardLjy0zIPIGSyqHo455Mf86VWE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-2.0.0.tgz", "integrity": "sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true, "license": "MIT"}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "2.6.0", "resolved": "http://r.npm.sankuai.com/mime/download/mime-2.6.0.tgz", "integrity": "sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/mimic-response/download/mimic-response-1.0.1.tgz", "integrity": "sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "5.1.6", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz", "integrity": "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz", "integrity": "sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mitt": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/mitt/download/mitt-3.0.1.tgz", "integrity": "sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=", "license": "MIT"}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz", "integrity": "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==", "optional": true, "peer": true, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/mysql2": {"version": "3.14.2", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-3.14.2.tgz", "integrity": "sha1-IfpyUWLaktSSYdziY2LeU54R5co=", "license": "MIT", "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "engines": {"node": ">= 8.0"}}, "node_modules/named-placeholders": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/named-placeholders/download/named-placeholders-1.1.3.tgz", "integrity": "sha1-31lXmaNmVNpV3aYVK6ehN60dk1E=", "license": "MIT", "dependencies": {"lru-cache": "^7.14.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/named-placeholders/node_modules/lru-cache": {"version": "7.18.3", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz", "integrity": "sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/negotiator": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/negotiator/download/negotiator-1.0.0.tgz", "integrity": "sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "http://r.npm.sankuai.com/neo-async/download/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "dev": true, "license": "MIT"}, "node_modules/netmask": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/netmask/download/netmask-2.0.2.tgz", "integrity": "sha1-iwGgdkQGXVNjg4NYI7xSAE66xec=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/no-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/no-case/download/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-addon-api": {"version": "1.7.2", "resolved": "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-1.7.2.tgz", "integrity": "sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=", "dev": true, "license": "MIT", "optional": true}, "node_modules/node-machine-id": {"version": "1.1.12", "resolved": "http://r.npm.sankuai.com/node-machine-id/download/node-machine-id-1.1.12.tgz", "integrity": "sha1-N5BO7h5ZsyC7nF1sClnztGnLYmc=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz", "integrity": "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/nth-check": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/omggif": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/omggif/download/omggif-1.0.10.tgz", "integrity": "sha1-3ar5DUpC9TLp58s6lezdR/F8exk=", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openai": {"version": "5.10.1", "resolved": "http://r.npm.sankuai.com/openai/download/openai-5.10.1.tgz", "integrity": "sha1-RTWpYD9NA7I5K7LKQaYYqA/c+s0=", "license": "Apache-2.0", "bin": {"openai": "bin/cli"}, "peerDependencies": {"ws": "^8.18.0", "zod": "^3.23.8"}, "peerDependenciesMeta": {"ws": {"optional": true}, "zod": {"optional": true}}}, "node_modules/p-cancelable": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-2.1.1.tgz", "integrity": "sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pac-proxy-agent": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/pac-proxy-agent/download/pac-proxy-agent-7.2.0.tgz", "integrity": "sha1-nPrzP/Jdo29hR6IIRCMOySwG5d8=", "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "node_modules/pac-proxy-agent/node_modules/agent-base": {"version": "7.1.4", "resolved": "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.4.tgz", "integrity": "sha1-48121MVI7oldPD/Y3B9sW5Ay56g=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/pac-proxy-agent/node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/pac-proxy-agent/node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/pac-resolver": {"version": "7.0.1", "resolved": "http://r.npm.sankuai.com/pac-resolver/download/pac-resolver-7.0.1.tgz", "integrity": "sha1-VGdVWOo2i2TSEP2ckqZAtfO4q7Y=", "license": "MIT", "dependencies": {"degenerator": "^5.0.0", "netmask": "^2.0.2"}, "engines": {"node": ">= 14"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz", "integrity": "sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/pako": {"version": "1.0.11", "resolved": "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=", "license": "(MIT AND Zlib)"}, "node_modules/param-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/param-case/download/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "dev": true, "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parse-bmfont-ascii": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/parse-bmfont-ascii/download/parse-bmfont-ascii-1.0.6.tgz", "integrity": "sha1-Eaw8P/WPfCAgqyJ2kHkQjU36AoU=", "license": "MIT"}, "node_modules/parse-bmfont-binary": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/parse-bmfont-binary/download/parse-bmfont-binary-1.0.6.tgz", "integrity": "sha1-0Di0dtPp3Z2x4RoLDlOiJ5K2kAY=", "license": "MIT"}, "node_modules/parse-bmfont-xml": {"version": "1.1.6", "resolved": "http://r.npm.sankuai.com/parse-bmfont-xml/download/parse-bmfont-xml-1.1.6.tgz", "integrity": "sha1-AWtlXaeuvm2jjJBqyha/BBV3N2c=", "license": "MIT", "dependencies": {"xml-parse-from-string": "^1.0.0", "xml2js": "^0.5.0"}}, "node_modules/parse-bmfont-xml/node_modules/xml2js": {"version": "0.5.0", "resolved": "http://r.npm.sankuai.com/xml2js/download/xml2js-0.5.0.tgz", "integrity": "sha1-2UQGMfuy7YACA/rRBvJyT2LEk7c=", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/parse-bmfont-xml/node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz", "integrity": "sha1-vpuuHIoEbnazESdyY0fQrXACvrM=", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/pascal-case/download/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz", "integrity": "sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz", "integrity": "sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=", "dev": true, "license": "ISC"}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-8.2.0.tgz", "integrity": "sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/peek-readable": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/peek-readable/download/peek-readable-4.1.0.tgz", "integrity": "sha1-Ts4REb9cKtiGfDFMgTVoR+imLnI=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/pend": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/pend/download/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA=", "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pixelmatch": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/pixelmatch/download/pixelmatch-5.3.0.tgz", "integrity": "sha1-XlMhp6vt+3li1g2/NF3tqHy5Vgo=", "license": "ISC", "dependencies": {"pngjs": "^6.0.0"}, "bin": {"pixelmatch": "bin/pixelmatch"}}, "node_modules/pixelmatch/node_modules/pngjs": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/pngjs/download/pngjs-6.0.0.tgz", "integrity": "sha1-yp5dKqSNsCKKUsQZwzCOh3INqCE=", "license": "MIT", "engines": {"node": ">=12.13.0"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/pkg-up/-/pkg-up-3.1.0.tgz", "integrity": "sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up/node_modules/find-up": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/locate-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/p-locate": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/path-exists": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==", "engines": {"node": ">=4"}}, "node_modules/plist": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/plist/download/plist-3.1.0.tgz", "integrity": "sha1-eXpRapPmL1veVeC5zJyWf4YIk8k=", "dev": true, "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/pngjs": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/pngjs/download/pngjs-7.0.0.tgz", "integrity": "sha1-qLdEYCDrvGrHOdtsVBWmXRcJDiY=", "license": "MIT", "engines": {"node": ">=14.19.0"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "http://r.npm.sankuai.com/postcss/download/postcss-8.5.6.tgz", "integrity": "sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.1.0.tgz", "integrity": "sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.2.0.tgz", "integrity": "sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^7.0.0", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/postcss-modules-scope/download/postcss-modules-scope-3.2.1.tgz", "integrity": "sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz", "integrity": "sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-selector-parser": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz", "integrity": "sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz", "integrity": "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=", "dev": true, "license": "MIT"}, "node_modules/pretty-error": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/pretty-error/download/pretty-error-4.0.0.tgz", "integrity": "sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "node_modules/process": {"version": "0.11.10", "resolved": "http://r.npm.sankuai.com/process/download/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT", "peer": true}, "node_modules/progress": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise-retry": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/promise-retry/download/promise-retry-2.0.1.tgz", "integrity": "sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-agent": {"version": "6.5.0", "resolved": "http://r.npm.sankuai.com/proxy-agent/download/proxy-agent-6.5.0.tgz", "integrity": "sha1-nkmsuo5O4jSqy1Ofie2cI9AvIy0=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.6", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.1.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/agent-base": {"version": "7.1.4", "resolved": "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.4.tgz", "integrity": "sha1-48121MVI7oldPD/Y3B9sW5Ay56g=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/lru-cache": {"version": "7.18.3", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz", "integrity": "sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/psl": {"version": "1.15.0", "resolved": "https://registry.npmmirror.com/psl/-/psl-1.15.0.tgz", "integrity": "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/pump": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/pump/download/pump-3.0.3.tgz", "integrity": "sha1-FR2XnxopZo3AAl7FiaRVtTKCJo0=", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pupa": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/pupa/-/pupa-3.1.0.tgz", "integrity": "sha512-FLpr4flz5xZTSJxSeaheeMKN/EDzMdK7b8PTOC6a5PYFKTucWbdqjgqaEyH0shFiSJrVB1+Qqi4Tk19ccU6Aug==", "dependencies": {"escape-goat": "^4.0.0"}, "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/puppeteer-core": {"version": "24.7.2", "resolved": "http://r.npm.sankuai.com/puppeteer-core/download/puppeteer-core-24.7.2.tgz", "integrity": "sha1-c043elY0zh5Bn6POIK0pen4amf8=", "license": "Apache-2.0", "dependencies": {"@puppeteer/browsers": "2.10.2", "chromium-bidi": "4.1.1", "debug": "^4.4.0", "devtools-protocol": "0.0.1425554", "typed-query-selector": "^2.12.0", "ws": "^8.18.1"}, "engines": {"node": ">=18"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystringify": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/querystringify/-/querystringify-2.2.0.tgz", "integrity": "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="}, "node_modules/quick-lru": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/quick-lru/download/quick-lru-5.1.1.tgz", "integrity": "sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/raw-body/download/raw-body-3.0.0.tgz", "integrity": "sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rc-cascader": {"version": "3.34.0", "resolved": "http://r.npm.sankuai.com/rc-cascader/download/rc-cascader-3.34.0.tgz", "integrity": "sha1-Vvk2q2sSKbq31VhwHOm56WU2WCw=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.7", "classnames": "^2.3.1", "rc-select": "~14.16.2", "rc-tree": "~5.13.0", "rc-util": "^5.43.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-checkbox": {"version": "3.5.0", "resolved": "http://r.npm.sankuai.com/rc-checkbox/download/rc-checkbox-3.5.0.tgz", "integrity": "sha1-OuJEHjoyF3TTkPdlOecGhk/PX/A=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.25.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-collapse": {"version": "3.9.0", "resolved": "http://r.npm.sankuai.com/rc-collapse/download/rc-collapse-3.9.0.tgz", "integrity": "sha1-lyQEznck4cnR0kdlQ+EXVASjaAY=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.3.4", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dialog": {"version": "9.6.0", "resolved": "http://r.npm.sankuai.com/rc-dialog/download/rc-dialog-9.6.0.tgz", "integrity": "sha1-3HolXGrRy1YCHDphx96G7ojHw3E=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-8", "classnames": "^2.2.6", "rc-motion": "^2.3.0", "rc-util": "^5.21.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-drawer": {"version": "7.3.0", "resolved": "http://r.npm.sankuai.com/rc-drawer/download/rc-drawer-7.3.0.tgz", "integrity": "sha1-G7X+X52ji2orKn3/yfy2RyUqMo8=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@rc-component/portal": "^1.1.1", "classnames": "^2.2.6", "rc-motion": "^2.6.1", "rc-util": "^5.38.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dropdown": {"version": "4.2.1", "resolved": "http://r.npm.sankuai.com/rc-dropdown/download/rc-dropdown-4.2.1.tgz", "integrity": "sha1-RHKesqQnLgNT0xrAYNoh5gasyxw=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-util": "^5.44.1"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}}, "node_modules/rc-field-form": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-2.7.0.tgz", "integrity": "sha1-IkE+eT81v8HzWw7EYndNcnf1o5k=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/async-validator": "^5.0.3", "rc-util": "^5.32.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-image": {"version": "7.12.0", "resolved": "http://r.npm.sankuai.com/rc-image/download/rc-image-7.12.0.tgz", "integrity": "sha1-lekxRwHmaCF9ETwfKbTwGsAlyv4=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/portal": "^1.0.2", "classnames": "^2.2.6", "rc-dialog": "~9.6.0", "rc-motion": "^2.6.2", "rc-util": "^5.34.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-input": {"version": "1.8.0", "resolved": "http://r.npm.sankuai.com/rc-input/download/rc-input-1.8.0.tgz", "integrity": "sha1-0vRAS+/r8vvcKDkNVJTDAvdK6XQ=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.18.1"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/rc-input-number": {"version": "9.5.0", "resolved": "http://r.npm.sankuai.com/rc-input-number/download/rc-input-number-9.5.0.tgz", "integrity": "sha1-tHlj0PLL2Fqy8brf3AiakEwHPzg=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/mini-decimal": "^1.0.1", "classnames": "^2.2.5", "rc-input": "~1.8.0", "rc-util": "^5.40.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-mentions": {"version": "2.20.0", "resolved": "http://r.npm.sankuai.com/rc-mentions/download/rc-mentions-2.20.0.tgz", "integrity": "sha1-O76sA1KwLgzj4SRK20hwG7aQO/c=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.22.5", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-input": "~1.8.0", "rc-menu": "~9.16.0", "rc-textarea": "~1.10.0", "rc-util": "^5.34.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-menu": {"version": "9.16.1", "resolved": "http://r.npm.sankuai.com/rc-menu/download/rc-menu-9.16.1.tgz", "integrity": "sha1-nfEWjkHYfccWTFghc+Gh0yARiZ8=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.0.0", "classnames": "2.x", "rc-motion": "^2.4.3", "rc-overflow": "^1.3.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-motion": {"version": "2.9.5", "resolved": "http://r.npm.sankuai.com/rc-motion/download/rc-motion-2.9.5.tgz", "integrity": "sha1-Esbq1P01X5TwDem7TxXfV21nfgw=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.44.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-notification": {"version": "5.6.4", "resolved": "http://r.npm.sankuai.com/rc-notification/download/rc-notification-5.6.4.tgz", "integrity": "sha1-6onDnBPNUX/f2X/mPwM3b6u3hUQ=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.9.0", "rc-util": "^5.20.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-overflow": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/rc-overflow/download/rc-overflow-1.4.1.tgz", "integrity": "sha1-4bzwN1l5wkz/oth7+DoZ3tX830U=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-resize-observer": "^1.0.0", "rc-util": "^5.37.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-pagination": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/rc-pagination/download/rc-pagination-5.1.0.tgz", "integrity": "sha1-puY6LF2ynmL5kSgusYotPucluos=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-picker": {"version": "4.11.3", "resolved": "http://r.npm.sankuai.com/rc-picker/download/rc-picker-4.11.3.tgz", "integrity": "sha1-fn462DqkYcKEuDkcaXSS0cNNLLg=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.1", "rc-overflow": "^1.3.2", "rc-resize-observer": "^1.4.0", "rc-util": "^5.43.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"date-fns": ">= 2.x", "dayjs": ">= 1.x", "luxon": ">= 3.x", "moment": ">= 2.x", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}}}, "node_modules/rc-progress": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/rc-progress/download/rc-progress-4.0.0.tgz", "integrity": "sha1-U4IUfZrdM9Ol+9JkABNz32RA4SY=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.6", "rc-util": "^5.16.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-rate": {"version": "2.13.1", "resolved": "http://r.npm.sankuai.com/rc-rate/download/rc-rate-2.13.1.tgz", "integrity": "sha1-Ka96PUdoNi6dQ4j5Vai2OJUmt/0=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.0.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-resize-observer": {"version": "1.4.3", "resolved": "http://r.npm.sankuai.com/rc-resize-observer/download/rc-resize-observer-1.4.3.tgz", "integrity": "sha1-T9QfpWG6UTYrUVWgfDXXyJoepWk=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.7", "classnames": "^2.2.1", "rc-util": "^5.44.1", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-segmented": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/rc-segmented/download/rc-segmented-2.7.0.tgz", "integrity": "sha1-9WwgRKv48DlYs6mp0ymH8Q3MT8Q=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-motion": "^2.4.4", "rc-util": "^5.17.0"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/rc-select": {"version": "14.16.8", "resolved": "http://r.npm.sankuai.com/rc-select/download/rc-select-14.16.8.tgz", "integrity": "sha1-eOZ4LxzMHwPZADvD7/pO1gnSmpc=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.1.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-overflow": "^1.3.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-slider": {"version": "11.1.8", "resolved": "http://r.npm.sankuai.com/rc-slider/download/rc-slider-11.1.8.tgz", "integrity": "sha1-zzsw2srI+Y1E92hfcz9vfaFG/AY=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-steps": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/rc-steps/download/rc-steps-6.0.1.tgz", "integrity": "sha1-whNs0Ah3M/bVCSCahKXIDcKaJ00=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.16.7", "classnames": "^2.2.3", "rc-util": "^5.16.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-switch": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/rc-switch/download/rc-switch-4.1.0.tgz", "integrity": "sha1-832BtODFr9EnT9hTZ7FzBr8l59c=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "classnames": "^2.2.1", "rc-util": "^5.30.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-table": {"version": "7.51.1", "resolved": "http://r.npm.sankuai.com/rc-table/download/rc-table-7.51.1.tgz", "integrity": "sha1-zWmuMmLTth5Mk8l5wSeGkG6URpE=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/context": "^1.4.0", "classnames": "^2.2.5", "rc-resize-observer": "^1.1.0", "rc-util": "^5.44.3", "rc-virtual-list": "^3.14.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tabs": {"version": "15.6.1", "resolved": "http://r.npm.sankuai.com/rc-tabs/download/rc-tabs-15.6.1.tgz", "integrity": "sha1-8LbGU4TfoJpk61OehqBmfHplBwg=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "classnames": "2.x", "rc-dropdown": "~4.2.0", "rc-menu": "~9.16.0", "rc-motion": "^2.6.2", "rc-resize-observer": "^1.0.0", "rc-util": "^5.34.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-textarea": {"version": "1.10.0", "resolved": "http://r.npm.sankuai.com/rc-textarea/download/rc-textarea-1.10.0.tgz", "integrity": "sha1-+Pli74O+C44125fPA9v7ht3ZxGw=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.1", "rc-input": "~1.8.0", "rc-resize-observer": "^1.0.0", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tooltip": {"version": "6.4.0", "resolved": "http://r.npm.sankuai.com/rc-tooltip/download/rc-tooltip-6.4.0.tgz", "integrity": "sha1-6DLtA5KHICXlmSjPwa2QRWVkZ/0=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.1", "rc-util": "^5.44.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tree": {"version": "5.13.1", "resolved": "http://r.npm.sankuai.com/rc-tree/download/rc-tree-5.13.1.tgz", "integrity": "sha1-82ozqUoSgvSwloUhbAFIcIl0iRA=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.1"}, "engines": {"node": ">=10.x"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-tree-select": {"version": "5.27.0", "resolved": "http://r.npm.sankuai.com/rc-tree-select/download/rc-tree-select-5.27.0.tgz", "integrity": "sha1-PapilyroCEbayWv0d20ancnHxMY=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.7", "classnames": "2.x", "rc-select": "~14.16.2", "rc-tree": "~5.13.0", "rc-util": "^5.43.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-upload": {"version": "4.9.2", "resolved": "http://r.npm.sankuai.com/rc-upload/download/rc-upload-4.9.2.tgz", "integrity": "sha1-KX9S/RscKktXDD5CREYJt1MFMbs=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.5", "rc-util": "^5.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-util": {"version": "5.44.4", "resolved": "http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.4.tgz", "integrity": "sha1-ie6QN2g8ygHNYPGmu9p2FFfda6U=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "react-is": "^18.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-virtual-list": {"version": "3.19.1", "resolved": "http://r.npm.sankuai.com/rc-virtual-list/download/rc-virtual-list-3.19.1.tgz", "integrity": "sha1-eFtfQJsLu/oeqtzIEZcTWcg92fs=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.0", "classnames": "^2.2.6", "rc-resize-observer": "^1.0.0", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/react": {"version": "18.3.1", "resolved": "http://r.npm.sankuai.com/react/download/react-18.3.1.tgz", "integrity": "sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz", "integrity": "sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-is": {"version": "18.3.1", "resolved": "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz", "integrity": "sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=", "license": "MIT"}, "node_modules/react-router": {"version": "6.30.1", "resolved": "http://r.npm.sankuai.com/react-router/download/react-router-6.30.1.tgz", "integrity": "sha1-7LO4g8m6bb9dMZ3byZZ0f0q59MM=", "license": "MIT", "dependencies": {"@remix-run/router": "1.23.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/react-router-dom": {"version": "6.30.1", "resolved": "http://r.npm.sankuai.com/react-router-dom/download/react-router-dom-6.30.1.tgz", "integrity": "sha1-2iWAwnLdthMl5DVHhWa+lWOkojc=", "license": "MIT", "dependencies": {"@remix-run/router": "1.23.0", "react-router": "6.30.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/read-config-file": {"version": "6.3.2", "resolved": "http://r.npm.sankuai.com/read-config-file/download/read-config-file-6.3.2.tgz", "integrity": "sha1-VWiRqm/6vO2RbtV0V8sZLmGIBBE=", "dev": true, "license": "MIT", "dependencies": {"config-file-ts": "^0.2.4", "dotenv": "^9.0.2", "dotenv-expand": "^5.1.0", "js-yaml": "^4.1.0", "json5": "^2.2.0", "lazy-val": "^1.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readable-web-to-node-stream": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/readable-web-to-node-stream/download/readable-web-to-node-stream-3.0.4.tgz", "integrity": "sha1-OSujdwevW/Ytclw2wbXW70EZ7vw=", "license": "MIT", "dependencies": {"readable-stream": "^4.7.0"}, "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/readable-web-to-node-stream/node_modules/buffer": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-6.0.3.tgz", "integrity": "sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/readable-web-to-node-stream/node_modules/readable-stream": {"version": "4.7.0", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-4.7.0.tgz", "integrity": "sha1-ztvYoRRsE9//jasUBoAo1YwVrJE=", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/readdir-glob": {"version": "1.1.3", "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"minimatch": "^5.1.0"}}, "node_modules/readdirp": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/readdirp/download/readdirp-4.1.2.tgz", "integrity": "sha1-64WAFDX78qfuWPGeCSGwaPxplI0=", "dev": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/rechoir": {"version": "0.8.0", "resolved": "http://r.npm.sankuai.com/rechoir/download/rechoir-0.8.0.tgz", "integrity": "sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=", "dev": true, "license": "MIT", "dependencies": {"resolve": "^1.20.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/relateurl": {"version": "0.2.7", "resolved": "http://r.npm.sankuai.com/relateurl/download/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/renderkid": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/renderkid/download/renderkid-3.0.0.tgz", "integrity": "sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=", "dev": true, "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz", "integrity": "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-alpn": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/resolve-alpn/download/resolve-alpn-1.2.1.tgz", "integrity": "sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=", "dev": true, "license": "MIT"}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/responselike": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/responselike/download/responselike-2.0.1.tgz", "integrity": "sha1-mgvI/cJS8/scymiwFlkQWboUIrw=", "dev": true, "license": "MIT", "dependencies": {"lowercase-keys": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "http://r.npm.sankuai.com/retry/download/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/roarr": {"version": "2.15.4", "resolved": "http://r.npm.sankuai.com/roarr/download/roarr-2.15.4.tgz", "integrity": "sha1-9f55W3uDjM/jXcYI4Cgrnrouev0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}, "engines": {"node": ">=8.0"}}, "node_modules/router": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/router/download/router-2.2.0.tgz", "integrity": "sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.2.tgz", "integrity": "sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/sanitize-filename": {"version": "1.6.3", "resolved": "http://r.npm.sankuai.com/sanitize-filename/download/sanitize-filename-1.6.3.tgz", "integrity": "sha1-dV69dSBFkxl34wsgJdNA18kJA3g=", "dev": true, "license": "WTFPL OR ISC", "dependencies": {"truncate-utf8-bytes": "^1.0.0"}}, "node_modules/sass": {"version": "1.89.2", "resolved": "http://r.npm.sankuai.com/sass/download/sass-1.89.2.tgz", "integrity": "sha1-p3FxaurndOK1KfcsD/Lf1Gyd4Q4=", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "node_modules/sass-loader": {"version": "16.0.5", "resolved": "http://r.npm.sankuai.com/sass-loader/download/sass-loader-16.0.5.tgz", "integrity": "sha1-JXvJARmt4GaFHK/n8sPzUEx82pg=", "dev": true, "license": "MIT", "dependencies": {"neo-async": "^2.6.2"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/sax": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/sax/download/sax-1.4.1.tgz", "integrity": "sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=", "license": "ISC"}, "node_modules/scheduler": {"version": "0.23.2", "resolved": "http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz", "integrity": "sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/schema-utils": {"version": "4.3.2", "resolved": "http://r.npm.sankuai.com/schema-utils/download/schema-utils-4.3.2.tgz", "integrity": "sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/schema-utils/node_modules/ajv": {"version": "8.17.1", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/schema-utils/node_modules/ajv-keywords": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/schema-utils/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "dev": true, "license": "MIT"}, "node_modules/scroll-into-view-if-needed": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-3.1.0.tgz", "integrity": "sha1-+pUkUYx5m0Wi72u/+5K8rQKW0B8=", "license": "MIT", "dependencies": {"compute-scroll-into-view": "^3.0.2"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-compare": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/semver-compare/download/semver-compare-1.0.0.tgz", "integrity": "sha1-De4hahyUGrN+nvsXiPavxf9VN/w=", "dev": true, "license": "MIT", "optional": true}, "node_modules/send": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/send/download/send-1.2.0.tgz", "integrity": "sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/send/node_modules/mime-db": {"version": "1.54.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/send/node_modules/mime-types": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/seq-queue": {"version": "0.0.5", "resolved": "http://r.npm.sankuai.com/seq-queue/download/seq-queue-0.0.5.tgz", "integrity": "sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4="}, "node_modules/serialize-error": {"version": "7.0.1", "resolved": "http://r.npm.sankuai.com/serialize-error/download/serialize-error-7.0.1.tgz", "integrity": "sha1-8TYLBEf2H/tIPsQVfHN/q313jhg=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"type-fest": "^0.13.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "resolved": "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-6.0.2.tgz", "integrity": "sha1-3voeBVyDv21Z6oBdjahiJU62psI=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/serve-static/download/serve-static-2.2.0.tgz", "integrity": "sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/sharp": {"version": "0.33.5", "resolved": "http://r.npm.sankuai.com/sharp/download/sharp-0.33.5.tgz", "integrity": "sha1-E+DkEwzDCdapSXWWcVJAsuwMWU4=", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.3", "semver": "^7.6.3"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "0.33.5", "@img/sharp-darwin-x64": "0.33.5", "@img/sharp-libvips-darwin-arm64": "1.0.4", "@img/sharp-libvips-darwin-x64": "1.0.4", "@img/sharp-libvips-linux-arm": "1.0.5", "@img/sharp-libvips-linux-arm64": "1.0.4", "@img/sharp-libvips-linux-s390x": "1.0.4", "@img/sharp-libvips-linux-x64": "1.0.4", "@img/sharp-libvips-linuxmusl-arm64": "1.0.4", "@img/sharp-libvips-linuxmusl-x64": "1.0.4", "@img/sharp-linux-arm": "0.33.5", "@img/sharp-linux-arm64": "0.33.5", "@img/sharp-linux-s390x": "0.33.5", "@img/sharp-linux-x64": "0.33.5", "@img/sharp-linuxmusl-arm64": "0.33.5", "@img/sharp-linuxmusl-x64": "0.33.5", "@img/sharp-wasm32": "0.33.5", "@img/sharp-win32-ia32": "0.33.5", "@img/sharp-win32-x64": "0.33.5"}}, "node_modules/sharp/node_modules/detect-libc": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/detect-libc/download/detect-libc-2.0.4.tgz", "integrity": "sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/sharp/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "resolved": "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.3.tgz", "integrity": "sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-update-notifier": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/simple-update-notifier/download/simple-update-notifier-2.0.0.tgz", "integrity": "sha1-1wuSvat9bZDf1zkxGVowtuPXzrs=", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/simple-update-notifier/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/simple-xml-to-json": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/simple-xml-to-json/download/simple-xml-to-json-1.2.3.tgz", "integrity": "sha1-eccYj/ma4gmiZ7cO4NsGsORZd4c=", "license": "MIT", "engines": {"node": ">=20.12.2"}}, "node_modules/slice-ansi": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-3.0.0.tgz", "integrity": "sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/smart-buffer/download/smart-buffer-4.2.0.tgz", "integrity": "sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.8.6", "resolved": "http://r.npm.sankuai.com/socks/download/socks-2.8.6.tgz", "integrity": "sha1-4zVIaiVS80+TLwwn2Nu5PyvoZ6o=", "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "8.0.5", "resolved": "http://r.npm.sankuai.com/socks-proxy-agent/download/socks-proxy-agent-8.0.5.tgz", "integrity": "sha1-uc205+mYUJ12WdaJznaXrCFkW+4=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "engines": {"node": ">= 14"}}, "node_modules/socks-proxy-agent/node_modules/agent-base": {"version": "7.1.4", "resolved": "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.4.tgz", "integrity": "sha1-48121MVI7oldPD/Y3B9sW5Ay56g=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/sort-keys": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/sort-keys/-/sort-keys-1.1.2.tgz", "integrity": "sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==", "dependencies": {"is-plain-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sort-keys-length": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/sort-keys-length/-/sort-keys-length-1.0.1.tgz", "integrity": "sha512-GRbEOUqCxemTAk/b32F2xa8wDTs+Z1QHOkbhJDQTvv/6G3ZkbJ+frYWsTcc7cBB3Fu4wy4XlLCuNtJuMn7Gsvw==", "dependencies": {"sort-keys": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz", "integrity": "sha1-BP58f54e0tZiIzwoyys1ufY/bk8=", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spawn-command": {"version": "0.0.2", "resolved": "http://r.npm.sankuai.com/spawn-command/download/spawn-command-0.0.2.tgz", "integrity": "sha1-lUThpDygRfhTGqwaSMspva5iM44=", "dev": true}, "node_modules/sprintf-js": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.1.3.tgz", "integrity": "sha1-SRS5A6L4toXRf994pw6RfocuREo=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sqlstring": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/sqlstring/download/sqlstring-2.3.3.tgz", "integrity": "sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/ssf": {"version": "0.11.2", "resolved": "http://r.npm.sankuai.com/ssf/download/ssf-0.11.2.tgz", "integrity": "sha1-C5lpiyN1SNCI/EPN8rcMGnUSwGw=", "license": "Apache-2.0", "dependencies": {"frac": "~1.1.2"}, "engines": {"node": ">=0.8"}}, "node_modules/sshpk": {"version": "1.18.0", "resolved": "https://registry.npmmirror.com/sshpk/-/sshpk-1.18.0.tgz", "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stat-mode": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/stat-mode/download/stat-mode-1.0.0.tgz", "integrity": "sha1-aLVcth6mOf9XE282shaikYANFGU=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/statuses": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.2.tgz", "integrity": "sha1-j3XuzvdlteHPzcCA2llAntQk44I=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/streamx": {"version": "2.22.1", "resolved": "http://r.npm.sankuai.com/streamx/download/streamx-2.22.1.tgz", "integrity": "sha1-yXy7DOGNpPTbWpcdyato/13H9aU=", "license": "MIT", "dependencies": {"fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}, "optionalDependencies": {"bare-events": "^2.2.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-convert": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz", "integrity": "sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=", "license": "MIT"}, "node_modules/string-width": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strnum": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/strnum/-/strnum-1.1.2.tgz", "integrity": "sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}]}, "node_modules/strtok3": {"version": "6.3.0", "resolved": "http://r.npm.sankuai.com/strtok3/download/strtok3-6.3.0.tgz", "integrity": "sha1-NYuA/+bV1WIOGaBzqnjOlHqQ+aA=", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "peek-readable": "^4.1.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/style-loader": {"version": "3.3.4", "resolved": "http://r.npm.sankuai.com/style-loader/download/style-loader-3.3.4.tgz", "integrity": "sha1-8w94bDbbA6RcvVW2pw2TDEeQkOc=", "dev": true, "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/stylis": {"version": "4.3.6", "resolved": "http://r.npm.sankuai.com/stylis/download/stylis-4.3.6.tgz", "integrity": "sha1-fHuXGRy08ZXwPsq31S95Au03gyA=", "license": "MIT"}, "node_modules/sumchecker": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/sumchecker/download/sumchecker-3.0.1.tgz", "integrity": "sha1-Y3fplnlauwttNI6bPh37JDRajkI=", "dev": true, "license": "Apache-2.0", "dependencies": {"debug": "^4.1.0"}, "engines": {"node": ">= 8.0"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tapable": {"version": "2.2.2", "resolved": "http://r.npm.sankuai.com/tapable/download/tapable-2.2.2.tgz", "integrity": "sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/tar/download/tar-6.2.1.tgz", "integrity": "sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-fs": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/tar-fs/download/tar-fs-3.1.0.tgz", "integrity": "sha1-RnXiJU2BQQ5gnZFYGnYmCN6ZnSU=", "license": "MIT", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}}, "node_modules/tar-fs/node_modules/tar-stream": {"version": "3.1.7", "resolved": "http://r.npm.sankuai.com/tar-stream/download/tar-stream-3.1.7.tgz", "integrity": "sha1-JLP7XqutoZ/nM47W0m5ffEgueSs=", "license": "MIT", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}}, "node_modules/tar-stream": {"version": "2.2.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/temp-file": {"version": "3.4.0", "resolved": "http://r.npm.sankuai.com/temp-file/download/temp-file-3.4.0.tgz", "integrity": "sha1-dm6iiRHGg5lsJI7xog7qBNUWUsc=", "dev": true, "license": "MIT", "dependencies": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}}, "node_modules/temp-file/node_modules/fs-extra": {"version": "10.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz", "integrity": "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/temp-file/node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/temp-file/node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/terser": {"version": "5.43.1", "resolved": "http://r.npm.sankuai.com/terser/download/terser-5.43.1.tgz", "integrity": "sha1-iDh/T5eU/xop561h+yky4ltP220=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.14", "resolved": "http://r.npm.sankuai.com/terser-webpack-plugin/download/terser-webpack-plugin-5.3.14.tgz", "integrity": "sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "resolved": "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "dev": true, "license": "MIT"}, "node_modules/text-decoder": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/text-decoder/download/text-decoder-1.2.3.tgz", "integrity": "sha1-sZ2jZNmBsjJtX0MJnDEMyA13DGU=", "license": "Apache-2.0", "dependencies": {"b4a": "^1.6.4"}}, "node_modules/throttle-debounce": {"version": "5.0.2", "resolved": "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-5.0.2.tgz", "integrity": "sha1-7FVJ2E4FPwQ8n9Dypt2JL/hEVrE=", "license": "MIT", "engines": {"node": ">=12.22"}}, "node_modules/tinycolor2": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz", "integrity": "sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=", "license": "MIT"}, "node_modules/tmp": {"version": "0.2.3", "resolved": "http://r.npm.sankuai.com/tmp/download/tmp-0.2.3.tgz", "integrity": "sha1-63g8wivB6L69BnFHbUbqTrMqea4=", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/tmp-promise": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/tmp-promise/download/tmp-promise-3.0.3.tgz", "integrity": "sha1-YKGhzJjJiGdPy/0jtuM2e96sTOc=", "dev": true, "license": "MIT", "dependencies": {"tmp": "^0.2.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toggle-selection": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/toggle-selection/download/toggle-selection-1.0.6.tgz", "integrity": "sha1-bkWxJj8gF/oKzH2J14sVuL932jI=", "license": "MIT"}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/token-types": {"version": "4.2.1", "resolved": "http://r.npm.sankuai.com/token-types/download/token-types-4.2.1.tgz", "integrity": "sha1-D4l/A2ZYRpgoBuE4l32+ctRN91M=", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/tough-cookie": {"version": "4.1.4", "resolved": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-4.1.4.tgz", "integrity": "sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/universalify/-/universalify-0.2.0.tgz", "integrity": "sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==", "engines": {"node": ">= 4.0.0"}}, "node_modules/tree-kill": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/tree-kill/download/tree-kill-1.2.2.tgz", "integrity": "sha1-TKCakJLIi3OnzcXooBtQeweQoMw=", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/truncate-utf8-bytes": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/truncate-utf8-bytes/download/truncate-utf8-bytes-1.0.2.tgz", "integrity": "sha1-QFkjkJWS1W94pYGENLC3hInKXys=", "dev": true, "license": "WTFPL", "dependencies": {"utf8-byte-length": "^1.0.1"}}, "node_modules/ts-loader": {"version": "9.5.2", "resolved": "http://r.npm.sankuai.com/ts-loader/download/ts-loader-9.5.2.tgz", "integrity": "sha1-Hz1/S7cJtIeqomDo8ZswFjXQgCA=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "enhanced-resolve": "^5.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4", "source-map": "^0.7.4"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"typescript": "*", "webpack": "^5.0.0"}}, "node_modules/ts-loader/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/ts-loader/node_modules/source-map": {"version": "0.7.4", "resolved": "http://r.npm.sankuai.com/source-map/download/source-map-0.7.4.tgz", "integrity": "sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmmirror.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/turndown": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/turndown/download/turndown-7.2.0.tgz", "integrity": "sha1-Z9YU/oNx+1EQeakzRav9FWwP/PQ=", "license": "MIT", "dependencies": {"@mixmark-io/domino": "^2.2.0"}}, "node_modules/turndown-plugin-gfm": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/turndown-plugin-gfm/download/turndown-plugin-gfm-1.0.2.tgz", "integrity": "sha1-b4Z4o2HzUiCyvfVhnmBJrddb8cc=", "license": "MIT"}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "node_modules/type-fest": {"version": "0.13.1", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.13.1.tgz", "integrity": "sha1-AXLLW86AsL1ULqNI21DH4hg02TQ=", "dev": true, "license": "(MIT OR CC0-1.0)", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/type-is/download/type-is-2.0.1.tgz", "integrity": "sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/type-is/node_modules/mime-db": {"version": "1.54.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/type-is/node_modules/mime-types": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-query-selector": {"version": "2.12.0", "resolved": "http://r.npm.sankuai.com/typed-query-selector/download/typed-query-selector-2.12.0.tgz", "integrity": "sha1-krZdvApCZV/M9K6xoIsd3c6K9fI=", "license": "MIT"}, "node_modules/typescript": {"version": "5.8.3", "resolved": "http://r.npm.sankuai.com/typescript/download/typescript-5.8.3.tgz", "integrity": "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/universalify": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unused-filename": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/unused-filename/-/unused-filename-4.0.1.tgz", "integrity": "sha512-ZX6U1J04K1FoSUeoX1OicAhw4d0aro2qo+L8RhJkiGTNtBNkd/Fi1Wxoc9HzcVu6HfOzm0si/N15JjxFmD1z6A==", "dependencies": {"escape-string-regexp": "^5.0.0", "path-exists": "^5.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/unused-filename/node_modules/escape-string-regexp": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/unused-filename/node_modules/path-exists": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/path-exists/-/path-exists-5.0.0.tgz", "integrity": "sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/url-parse/-/url-parse-1.5.10.tgz", "integrity": "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/utf8-byte-length": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/utf8-byte-length/download/utf8-byte-length-1.0.5.tgz", "integrity": "sha1-+fY5ENFVNu4rLV3UZlOJcV6sXB4=", "dev": true, "license": "(WTFPL OR MIT)"}, "node_modules/utif2": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/utif2/download/utif2-4.1.0.tgz", "integrity": "sha1-52jTe9YZuZXVbZeAtdK0YRo9kys=", "license": "MIT", "dependencies": {"pako": "^1.0.11"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "license": "MIT"}, "node_modules/utila": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/utila/download/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true, "license": "MIT"}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/uuidv4": {"version": "6.2.13", "resolved": "https://registry.npmmirror.com/uuidv4/-/uuidv4-6.2.13.tgz", "integrity": "sha512-AXyzMjazYB3ovL3q051VLH06Ixj//Knx7QnUSi1T//Ie3io6CpsPu9nVMOx5MoLWh6xV0B9J0hIaxungxXUbPQ==", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.", "dependencies": {"@types/uuid": "8.3.4", "uuid": "8.3.2"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.1", "resolved": "http://r.npm.sankuai.com/verror/download/verror-1.10.1.tgz", "integrity": "sha1-S/Ce7M9FY7EJ7Us9RYOAyXKwzes=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/wait-on": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/wait-on/download/wait-on-7.2.0.tgz", "integrity": "sha1-12sg7T/B4r68BR+uXB/5O+eJKSg=", "dev": true, "license": "MIT", "dependencies": {"axios": "^1.6.1", "joi": "^17.11.0", "lodash": "^4.17.21", "minimist": "^1.2.8", "rxjs": "^7.8.1"}, "bin": {"wait-on": "bin/wait-on"}, "engines": {"node": ">=12.0.0"}}, "node_modules/watchpack": {"version": "2.4.4", "resolved": "http://r.npm.sankuai.com/watchpack/download/watchpack-2.4.4.tgz", "integrity": "sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=", "dev": true, "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/webpack": {"version": "5.100.0", "dev": true, "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.8", "@types/json-schema": "^7.0.15", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.15.0", "acorn-import-phases": "^1.0.3", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.2", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.3.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-cli": {"version": "5.1.4", "resolved": "http://r.npm.sankuai.com/webpack-cli/download/webpack-cli-5.1.4.tgz", "integrity": "sha1-yOBGun6q5JEdfnHislt3b8w1dZs=", "dev": true, "license": "MIT", "dependencies": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "colorette": "^2.0.14", "commander": "^10.0.1", "cross-spawn": "^7.0.3", "envinfo": "^7.7.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^5.7.3"}, "bin": {"webpack-cli": "bin/cli.js"}, "engines": {"node": ">=14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "5.x.x"}, "peerDependenciesMeta": {"@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}}, "node_modules/webpack-cli/node_modules/commander": {"version": "10.0.1", "resolved": "http://r.npm.sankuai.com/commander/download/commander-10.0.1.tgz", "integrity": "sha1-iB7ka0930cHczFgjQzqjmwIsvgY=", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/webpack-merge": {"version": "5.10.0", "resolved": "http://r.npm.sankuai.com/webpack-merge/download/webpack-merge-5.10.0.tgz", "integrity": "sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/webpack-sources": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/webpack-sources/download/webpack-sources-3.3.3.tgz", "integrity": "sha1-1L9/mQlnXXoHD/FNDvKk88mCxyM=", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wildcard": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/wildcard/download/wildcard-2.0.1.tgz", "integrity": "sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=", "dev": true, "license": "MIT"}, "node_modules/wmf": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wmf/download/wmf-1.0.2.tgz", "integrity": "sha1-fRnWIQcaCMK9xrfmiKnENSmMwto=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/word/download/word-0.3.0.tgz", "integrity": "sha1-hUIVfk+OhJ9KNjooiZLUdhLbmWE=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/ws": {"version": "8.18.3", "resolved": "http://r.npm.sankuai.com/ws/download/ws-8.18.3.tgz", "integrity": "sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xlsx": {"version": "0.18.5", "resolved": "http://r.npm.sankuai.com/xlsx/download/xlsx-0.18.5.tgz", "integrity": "sha1-FnEbkRPISAdrihdwInma01brp9A=", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "bin": {"xlsx": "bin/xlsx.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/xml-parse-from-string": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/xml-parse-from-string/download/xml-parse-from-string-1.0.1.tgz", "integrity": "sha1-qQKekp09vN7RafPG4oI42VpdWig=", "license": "MIT"}, "node_modules/xmlbuilder": {"version": "15.1.1", "resolved": "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-15.1.1.tgz", "integrity": "sha1-nc3OSe6mbY0QtCyulKecPI0MLsU=", "dev": true, "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "resolved": "http://r.npm.sankuai.com/yargs/download/yargs-17.7.2.tgz", "integrity": "sha1-mR3zmspnWhkrgW4eA2P5110qomk=", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yauzl": {"version": "2.10.0", "resolved": "http://r.npm.sankuai.com/yauzl/download/yauzl-2.10.0.tgz", "integrity": "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=", "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/zip-stream": {"version": "4.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"archiver-utils": "^3.0.4", "compress-commons": "^4.1.2", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/zip-stream/node_modules/archiver-utils": {"version": "3.0.4", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob": "^7.2.3", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/zod": {"version": "3.25.76", "resolved": "http://r.npm.sankuai.com/zod/download/zod-3.25.76.tgz", "integrity": "sha1-JoQcP2/SKmonYOfMtxkXl2hHHjQ=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}