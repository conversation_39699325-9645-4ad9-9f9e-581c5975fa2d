import axios from "axios";
import {
  DingtalkConfig,
  DingTalkPushNotificationTemplate,
} from "../config/notification";
import { CrawlSuccessNotificationInfo } from "../config/notification";
import { getStore } from "../config/store";

interface AccessTokenResponse {
  accessToken: string;
  expireIn: number;
}

interface UserByMobileResponse {
  errcode: number;
  errmsg: string;
  request_id: string;
  result: {
    userid: string;
  };
}

interface SendWorkNotificationResponse {
  errcode: number;
  errmsg: string;
  request_id: string;
  task_id: number;
}

interface MarkdownMessage {
  msgtype: "markdown";
  markdown: {
    title: string;
    text: string;
  };
}

interface SendWorkNotificationParams {
  agent_id: string;
  userid_list?: string;
  dept_id_list?: string;
  to_all_user?: boolean;
  msg: MarkdownMessage | any; // 为后续扩展其他消息类型留空间
}

/**
 * 钉钉API客户端
 */
export class DingTalkService {
  private static _client: DingTalkService;
  private static _accessToken: string;
  private static _tokenExpireTime: number = 0;
  //@ts-ignore
  private _reqConfig: DingtalkConfig;

  public static getClient(config?: DingtalkConfig) {
    if (!DingTalkService._client) {
      DingTalkService._client = DingTalkService.createClient(config);
    }
    return DingTalkService._client;
  }

  private static createClient(config?: DingtalkConfig): DingTalkService {
    const client = new DingTalkService();
    client._reqConfig = config as DingtalkConfig;
    return client;
  }

  /**
   * 获取钉钉访问令牌
   * @param clientId 应用的Client ID
   * @param clientSecret 应用的Client Secret
   * @returns Promise<string> 返回accessToken
   */
  public async getAccessToken(): Promise<string> {
    // 检查缓存的token是否还有效（提前5分钟过期）
    const now = Date.now();
    if (
      DingTalkService._accessToken &&
      DingTalkService._tokenExpireTime > now + 300000
    ) {
      return DingTalkService._accessToken;
    }

    try {
      const response = await axios.post<AccessTokenResponse>(
        "https://api.dingtalk.com/v1.0/oauth2/accessToken",
        {
          appKey: this._reqConfig.clientId,
          appSecret: this._reqConfig.clientSecret,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const { accessToken, expireIn } = response.data;

      // 缓存token和过期时间
      DingTalkService._accessToken = accessToken;
      DingTalkService._tokenExpireTime = now + expireIn * 1000;

      return accessToken;
    } catch (error) {
      console.error("获取钉钉accessToken失败:", error);
      throw new Error("获取钉钉accessToken失败");
    }
  }

  /**
   * 根据手机号查询用户ID
   * @param mobile 用户手机号
   * @returns Promise<string> 返回用户ID
   */
  public async getUserIdByMobile(mobile: string): Promise<string> {
    try {
      // 获取accessToken
      const accessToken = await this.getAccessToken();

      const response = await axios.post<UserByMobileResponse>(
        `https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=${accessToken}`,
        {
          mobile: mobile,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const { errcode, errmsg, result } = response.data;

      // 检查返回码
      if (errcode !== 0) {
        console.error(`查询用户ID失败: errcode=${errcode}, errmsg=${errmsg}`);
        throw new Error(`查询用户ID失败: ${errmsg}`);
      }

      return result.userid;
    } catch (error) {
      console.error("根据手机号查询用户ID失败:", error);
      if (axios.isAxiosError(error) && error.response?.data) {
        const { errcode, errmsg } = error.response.data;
        throw new Error(`查询用户ID失败: ${errmsg} (错误码: ${errcode})`);
      }
      throw new Error("根据手机号查询用户ID失败");
    }
  }

  /**
   * 发送Markdown工作通知
   * @param title Markdown标题
   * @param text Markdown文本内容
   * @param mobile 用户手机号
   * @returns Promise<number> 返回任务ID
   */
  public async sendMarkdownNotification(
    title: string,
    text: string,
    mobile: string
  ): Promise<number> {
    try {
      // 根据手机号获取用户ID
      const userId = await this.getUserIdByMobile(mobile);

      // 构建Markdown消息
      const markdownMessage: MarkdownMessage = {
        msgtype: "markdown",
        markdown: {
          title: title,
          text: text,
        },
      };

      return await this.sendWorkNotification({
        agent_id: this._reqConfig.agentId,
        userid_list: userId,
        msg: markdownMessage,
      });
    } catch (error) {
      console.error("发送Markdown工作通知失败:", error);
      //@ts-ignore
      throw new Error(`发送Markdown工作通知失败: ${error.message}`);
    }
  }

  /**
   * 发送工作通知（通用方法，支持扩展）
   * @param params 发送参数
   * @returns Promise<number> 返回任务ID
   */
  public async sendWorkNotification(
    params: SendWorkNotificationParams
  ): Promise<number> {
    try {
      // 获取accessToken
      const accessToken = await this.getAccessToken();

      const response = await axios.post<SendWorkNotificationResponse>(
        `https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=${accessToken}`,
        params,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const { errcode, errmsg, task_id } = response.data;

      // 检查返回码
      if (errcode !== 0) {
        console.error(`发送工作通知失败: errcode=${errcode}, errmsg=${errmsg}`);
        throw new Error(`发送工作通知失败: ${errmsg}`);
      }

      console.log(`工作通知发送成功, 任务ID: ${task_id}`);
      return task_id;
    } catch (error) {
      console.error("发送工作通知失败:", error);
      if (axios.isAxiosError(error) && error.response?.data) {
        const { errcode, errmsg } = error.response.data;
        throw new Error(`发送工作通知失败: ${errmsg} (错误码: ${errcode})`);
      }
      throw new Error("发送工作通知失败");
    }
  }

  /**
   * 发送成功钉钉通知
   */
  public static async sendSuccessNotification(
    toolName: string,
    dataInfo: CrawlSuccessNotificationInfo
  ) {
    const reportTemplate = DingTalkPushNotificationTemplate.CrawlSuccess(
      toolName,
      dataInfo,
    );
    const { agentId, clientId, clientSecret, phone } = getStore().get(
      "settings"
    ) as Record<string, any>
    const reportResult = await DingTalkService.getClient({
      agentId,
      clientId,
      clientSecret,
      type: "Dingtalk",
    }).sendMarkdownNotification(
      reportTemplate.title,
      reportTemplate.text,
      phone
    )
    return reportResult
  }

  /**
   * 发送失败钉钉通知
   */
  public static async sendFailNotification(toolName: string, errorMessage: string) {
    const reportTemplate = DingTalkPushNotificationTemplate.CrawlError(
      toolName,
      {
        type: "CrawlError",
        errorMessage,
      }
    )
    const { agentId, clientId, clientSecret, phone } = getStore().get(
      "settings"
    ) as Record<string, any>
    const reportResult = await DingTalkService.getClient({
      agentId,
      clientId,
      clientSecret,
      type: "Dingtalk",
    }).sendMarkdownNotification(
      reportTemplate.title,
      reportTemplate.text,
      phone
    )
    return reportResult
  }
}
