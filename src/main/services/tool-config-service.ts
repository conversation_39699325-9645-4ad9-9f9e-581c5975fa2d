import { ToolConfigConverter } from "../converter/tool-config-converter";
import { ToolConfigDao } from "../dao/toolConfigDao";

export class ToolConfigService {
  /**
   * 工具配置查询
   */
  public static async queryAllToolConfig() {
    const toolConfigDao = new ToolConfigDao();
    const toolConfig = await toolConfigDao.getToolConfig();
    const result = ToolConfigConverter.convertToolConfigResponse(toolConfig);
    return result
  }

  public static async queryToolConfigByToolId(toolId: number) {
    const toolConfigDao = new ToolConfigDao();
    const toolConfig = await toolConfigDao.getToolConfigByToolId(toolId);
    const result = ToolConfigConverter.convertSingleToolConfigResponse(toolConfig);
    return result
  }
}