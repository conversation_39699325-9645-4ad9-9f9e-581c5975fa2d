import { CollectionDataType } from "../../renderer/types/collectionData";
import { CollectionDataDao } from "../dao/collectionDataDao";
import { TaskLogDao } from "../dao/taskLogDao";
import { TaskRunHistoryDao } from "../dao/taskRunHistoryDao";
import { getStore } from "../config/store";
import { CosStorage } from "../cos-storage";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { DingTalkService } from "./dingtalk-service";
import { TaskConfigDao } from "../dao/taskConfigDao";
import { TaskStatus } from "../../renderer/types/task";

/**
 * 采集数据服务
 */
export class CollectionService {
  /**
   * 采集完成
   * 1.采集成功失败，均需要记录日志
   * 2.推送钉钉消息
   * 3.将采集数据转换为Excel文件并上传到COS
   * @param params 
   * @param userName 
   */
  public static async completeCollectionHandler(params: CollectionDataType) {
    //@ts-ignore
    const userName = getStore().get('settings')?.userName as string
    // 生成唯一标识符，用于关联采集数据和采集历史
    const collectionId = this.generateCollectionId(params.missionId, params.date, params.time)
    const collectionDataDao = new CollectionDataDao()
    await collectionDataDao.saveCollectionData(params, userName, collectionId)
    const taskLogDao = new TaskLogDao()
    await taskLogDao.saveTaskRecord(params, userName)
    const taskRunHistoryDao = new TaskRunHistoryDao()
    await taskRunHistoryDao.saveCollectionDataHistory(params, userName, collectionId)
    const taskConfigDao = new TaskConfigDao()
    const currentTask = await taskConfigDao.getTaskConfigByTaskId(params.missionId, userName)
    // 如果采集成功，将数据转换为Excel并上传到COS
    if (params.status === TaskStatus.Success) {
      if(params.data && params.data.length > 0) {
        try {
          const excelAccessUrl = await this.convertToExcelAndUpload(params, currentTask.task_name)
          DingTalkService.sendSuccessNotification(currentTask.tool_name, {
            type: 'CrawlSuccess',
            crawlerId: userName,
            excelLink: excelAccessUrl
          })
        } catch (error) {
          console.error('转换Excel并上传失败:', error)
        }
      }
    }
    // 采集失败，推送钉钉消息
    else {
      DingTalkService.sendFailNotification(currentTask.tool_name, params.message)
    }
  }

  /**
   * 将采集数据转换为Excel文件并上传到COS
   * @param params 采集数据
   * @param collectionId 采集数据唯一标识符
   */
  private static async convertToExcelAndUpload(params: CollectionDataType, taskName: string) {
    // 生成Excel文件名
    const fileName = `${taskName}-${params.date}-${params.time.replace(/:/g, '')}.xlsx`
    // 准备Excel数据
    const excelData = this.prepareExcelData(params.data)
    // 创建临时文件路径
    const tempDir = os.tmpdir()
    const tempFilePath = path.join(tempDir, fileName)
    try {
      // 生成Excel文件
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(excelData)
      XLSX.utils.book_append_sheet(workbook, worksheet, "采集数据")
      // 写入临时文件
      XLSX.writeFile(workbook, tempFilePath)
      // 上传到COS
      const cosStorage = new CosStorage()
      const cosKey = `collection-data/${fileName}`
      await cosStorage.uploadFile(tempFilePath, cosKey)
      const result = cosStorage.getStaticWebsiteUrl(cosKey)
      return result
    } finally {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath)
      }
    }
  }

  /**
   * 准备Excel数据
   * @param data 采集数据
   * @returns Excel数据数组
   */
  private static prepareExcelData(data: CollectionDataType['data']): any[][] {
    // 表头
    const headers = [
      '酒店名称',
      '房型',
      '销售价',
      '最低价',
      '早餐',
      '取消规则',
      '确认规则',
      '付款方式',
      '促销信息',
      '房型基本信息'
    ]
    
    // 数据行
    const rows = data.map(item => [
      item.hotelName || '',
      item.roomName || '',
      item.salePrice || '',
      item.lowestPrice || '',
      item.breakfast || '',
      item.cancelRule || '',
      item.confirmRule || '',
      item.payWay || '',
      Array.isArray(item.promotion) ? item.promotion.join('、') : (item.promotion || ''),
      Array.isArray(item.roomBasicInfo) ? item.roomBasicInfo.join('、') : (item.roomBasicInfo || '')
    ])
    
    return [headers, ...rows]
  }

  /**
   * 生成采集数据唯一标识符
   * @param missionId 任务ID
   * @param date 采集日期
   * @param time 采集时间
   * @returns 唯一标识符
   */
  private static generateCollectionId(missionId: number, date: string, time: string): string {
    return `${missionId}_${date}_${time.replace(/:/g, '')}`
  }

  /**
   * 通过collectionId获取采集数据
   * @param collectionId 采集数据唯一标识符
   * @returns 采集数据
   */
  public static async getCollectionDataById(collectionId: string, userName: string) {
    const collectionDataDao = new CollectionDataDao()
    return await collectionDataDao.getCollectionDataById(collectionId, userName)
  }

  public static async getTaskRunHistoryByFileId(fileId: number, userName: string) {
    const taskRunHistoryDao = new TaskRunHistoryDao()
    return await taskRunHistoryDao.getTaskRunHistoryByFileId(fileId, userName)
  }

  public static async getCollectionDataByFileId(fileId: number, userName: string) {
    const taskRunHistoryDao = new TaskRunHistoryDao()
    const currentTask = await taskRunHistoryDao.getTaskRunHistoryByFileId(fileId, userName)
    if (!currentTask) {
      return null
    }
    const collectionDataDao = new CollectionDataDao()
    return await collectionDataDao.getCollectionDataById(currentTask.collection_id, userName)
  }
}