import { db } from './db-connection';
import { TaskRecordHistoryRepositoryType, TaskRecordRepositoryType, TaskRecordUnionRepositoryType } from '../../renderer/types/repository/taskRecord';
import { CollectionDataType } from '../../renderer/types/collectionData';
import { TaskConfigDao } from './taskConfigDao';

export class TaskLogDao {
  /**
   * 查询任务执行记录及其运行历史
   * @param taskId 任务ID
   * @returns 任务记录详情，包含运行历史
   */
  async queryTaskRecord(taskId: number, userName: string): Promise<TaskRecordUnionRepositoryType | null> {
    try {
      // 首先查询任务执行记录
      const recordSql = `
        SELECT *
        FROM task_execute_record
        WHERE task_id = ? AND \`delete\` = 1 AND user_name = ?
      `;

      const taskRecord = await db.query<TaskRecordRepositoryType[]>(recordSql, [taskId, userName]);
      console.log('taskRecord', taskRecord)
      if (!taskRecord || taskRecord.length === 0) {
        console.log('未找到任务执行记录:', taskId);
        return null;
      }
      // 查询对应的运行历史记录
      const historySql = `
        SELECT *
        FROM task_run_history
        WHERE task_id = ? AND user_name = ?
        ORDER BY gmt_create DESC
      `;

      const runHistory = await db.query<TaskRecordHistoryRepositoryType[]>(historySql, [taskId, userName]);
      // 组合结果
      return {
        ...taskRecord[0],
        runHistory: runHistory || []
      };

    } catch (error) {
      console.error('查询任务记录失败:', error);
      throw error;
    }
  }

  /**
   * 采集完成后，执行日志写入
   */
  async saveTaskRecord(params: CollectionDataType, userName: string) {
    const taskConfigDao = new TaskConfigDao()
    const taskPO = await taskConfigDao.getTaskConfigByTaskId(params.missionId, userName)
    const { tool_id, tool_name, target_url, start_time, end_time, time_list, task_name } = taskPO
    const fileName = `${task_name}-${params.date}-${params.time}`
    const sql = `
      INSERT INTO task_execute_record (user_name, tool_id, task_id, tool_name,task_name, target_url, start_date, end_date, time_list, file_name)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    await db.query(sql, [userName, tool_id, params.missionId, tool_name, task_name, target_url, start_time, end_time, time_list, fileName ])
  }
}