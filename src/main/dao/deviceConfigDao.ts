import { db } from './db-connection';
import { DeviceConfigRequestType } from '../../renderer/types/device';
import { DeviceConfigRepositoryType } from '../../renderer/types/repository/device';

export class DeviceConfigDao {
  /**
   * 检查设备配置是否存在
   * @param params 设备配置参数
   * @returns 返回配置ID，如果不存在则返回null
   */
  async checkDeviceConfigExists(params: DeviceConfigRequestType): Promise<number | null> {
    const { userName, host, port } = params;
    const sql = `
      SELECT id FROM device_config 
      WHERE user_name = ? AND host = ? AND port = ?
    `;
    const result = await db.query<any[]>(sql, [userName, host, port]);
    return result.length > 0 ? result[0].id : null;
  }

  /**
   * 保存或更新设备配置
   * @param params 设备配置参数
   * @returns 返回是否成功
   */
  async saveOrUpdateDeviceConfig(params: DeviceConfigRequestType): Promise<boolean> {
    const { userName, bucketName, region, secretId, secretKey, host, port, account, password, appId, appKey, agentId, clientId, clientSecret, phone } = params;
    // 检查是否存在相同配置
    const existingId = await this.checkDeviceConfigExists(params);
    if (existingId) {
      // 更新现有配置
      const updateSql = `
        UPDATE device_config 
        SET bucket_name = ?, 
            region = ?,
            secret_id = ?,
            secret_key = ?,
            account = ?,
            password = ?,
            agent_id = ?,
            client_id = ?,
            client_secret = ?,
            phone = ?,
            gmt_modified = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      await db.query(updateSql, [
        bucketName,
        region,
        secretId,
        secretKey,
        account,
        password,
        agentId,
        clientId,
        clientSecret,
        phone,
        existingId
      ]);
    } else {
      // 插入新配置
      const insertSql = `
        INSERT INTO device_config (
          user_name, bucket_name, region, secret_id, secret_key, 
          host, port, account, password, agent_id, client_id, client_secret, phone
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await db.query(insertSql, [
        userName, bucketName, region, secretId, secretKey,
        host, port, account, password, agentId, clientId, clientSecret, phone
      ]);
    }
    
    return true;
  }

  async getDeviceConfig(userName: string) {
    const sql = `
      SELECT * FROM device_config WHERE user_name = ?
    `
    const result: DeviceConfigRepositoryType[] = await db.query<any[]>(sql, [userName])
    return result[0]
  }
}