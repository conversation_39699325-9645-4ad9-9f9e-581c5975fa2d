import { CollectionDataType } from "../../renderer/types/collectionData";
import { db } from "./db-connection";
import { TaskConfigDao } from "./taskConfigDao";
import { TaskRunHistoryRepositoryType } from "../../renderer/types/repository/taskRunHistory";
import { ExecuteRecordListParams } from "../../renderer/types/file";
import dayjs from "dayjs";

/**
 * 每次采集执行记录
 */
export class TaskRunHistoryDao {

  async saveCollectionDataHistory(params: CollectionDataType, userName: string, collectionId: string) {
    const taskConfigDao = new TaskConfigDao()
    const taskPO = await taskConfigDao.getTaskConfigByTaskId(params.missionId, userName)
    const { start_time, end_time, task_name, tool_name, target_url, tool_id } = taskPO
    const poiNames = params.data.map(item => item.hotelName)
    const collectionTime = dayjs(`${params.date}T${params.time}`).format('YYYY-MM-DD HH:mm:ss')
    const fileName = `${task_name}-${params.date}-${params.time}`
    const errorMessage = params.status === 0 ? '' : `ID为${params.missionId}的任务遇到异常，${params.message}`
    const sql = `INSERT INTO task_run_history (user_name, task_id, run_status, data_count, error_message, start_time, end_time, task_name, tool_name, target_url, file_name, tool_id, poi_names, collection_id, collection_date, collection_time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    await db.query(sql, [userName, params.missionId, params.status, params.data.length, errorMessage, start_time, end_time, task_name, tool_name, target_url, fileName, tool_id, JSON.stringify(poiNames), collectionId, params.date, collectionTime])
  }
  /**
   * 查询最近3条任务执行记录
   */
  async queryRecentTaskHistory(userName: string): Promise<TaskRunHistoryRepositoryType[]> {
    const sql = `SELECT * FROM task_run_history WHERE user_name = ? ORDER BY gmt_create DESC LIMIT 3`
    const result = await db.query(sql, [userName])
    return result as TaskRunHistoryRepositoryType[]
  }

  async queryFailTaskHistoryByLimit(userName: string, startDate: string, endDate: string) {
    const sql = `SELECT * FROM task_run_history WHERE user_name = ? AND run_status = 1 AND gmt_create BETWEEN ? AND ? ORDER BY gmt_create DESC`
    const result = await db.query(sql, [userName, startDate, endDate])
    return result as TaskRunHistoryRepositoryType[]
  }

  /**
   * 查询全部成功的任务执行记录
   * @param userName
   * @param params 查询参数
   * @returns
   */
  async queryAllSuccessTaskHistory(userName: string, params: ExecuteRecordListParams) {
    // 构建基础查询条件
    let conditions = ['user_name = ?', 'run_status = 0', '`delete` = 1'];
    let queryParams: any[] = [userName];
    
    // 文件名模糊查询
    if (params.fileName && params.fileName.trim() !== '') {
      conditions.push('file_name LIKE ?');
      queryParams.push(`%${params.fileName.trim()}%`);
    }
    
    // 工具ID过滤
    if (params.toolId !== -1) {
      conditions.push('tool_id = ?');
      queryParams.push(params.toolId);
    }
    
    // 时间范围查询
    if (params.startDate && params.startDate.trim() !== '') {
      conditions.push('DATE(collection_time) >= ?');
      queryParams.push(params.startDate);
    }
    
    if (params.endDate && params.endDate.trim() !== '') {
      conditions.push('DATE(collection_time) <= ?');
      queryParams.push(params.endDate);
    }
    
    // 构建SQL语句
    const whereClause = conditions.join(' AND ');
    const sql = `SELECT * FROM task_run_history WHERE ${whereClause} ORDER BY collection_time DESC LIMIT ? OFFSET ?`;
    
    // 计算分页参数
    const limit = params.pageSize;
    const offset = (params.pageNum - 1) * params.pageSize;
    queryParams.push(limit, offset);
    
    const result = await db.query(sql, queryParams);
    
    // 计算总数
    const countSql = `SELECT COUNT(*) as total FROM task_run_history WHERE ${whereClause}`;
    const totalResult = await db.query<[{total: number}]>(countSql, queryParams.slice(0, -2));
    const total = totalResult[0].total;
    
    return {
      recordList: result as TaskRunHistoryRepositoryType[],
      total: total
    };
  }



  /**
   * 删除单个任务执行记录
   * @param fileId 文件ID
   * @param userName 用户名
   * @returns 成功返回被删除的ID，失败返回-1
   */
  async deleteSingleTaskHistory(fileId: number, userName: string): Promise<number> {
    try {
      // 先查询记录是否存在且未被删除
      const checkSql = `SELECT id FROM task_run_history WHERE id = ? AND user_name = ? AND \`delete\` = 1`
      const [record] = await db.query<any[]>(checkSql, [fileId, userName])
      if (!record || record.length === 0) {
        console.error('记录不存在或已被删除:', fileId)
        return -1
      }
      // 执行软删除
      const sql = `UPDATE task_run_history SET \`delete\` = 2 WHERE id = ? AND user_name = ?`
      const result = await db.query(sql, [fileId, userName])
      // 检查是否有记录被更新
      const affectedRows = (result as any).affectedRows
      if (affectedRows > 0) {
        return fileId
      } else {
        return -1
      }
    } catch (error) {
      console.error('删除任务执行记录失败:', error)
      return -1
    }
  }

  async getTaskRunHistoryByFileId(fileId: number, userName: string) {
    const sql = `SELECT * FROM task_run_history WHERE id = ? AND user_name = ?`
    const result: TaskRunHistoryRepositoryType[] = await db.query(sql, [fileId, userName])
    return result?.[0] || null
  }

}