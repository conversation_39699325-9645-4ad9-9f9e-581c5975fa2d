import { db } from './db-connection';
import { TaskConfigRepositoryResultType, TaskConfigRepositoryType } from '../../renderer/types/repository/taskConfig';
import { ToolConfigDao } from './toolConfigDao';
import { TaskConfigCreateOrUpdateType, TaskListRequestParams } from '../../renderer/types/task';

export class TaskConfigDao {
  /**
   * 分页获取全部任务列表
   */
  async getTaskConfig(params: TaskListRequestParams, userName: string): Promise<TaskConfigRepositoryResultType> {
    try {
      // 构建基础查询条件
      const conditions: string[] = ['user_name = ?'];
      const values: any[] = [userName];

      // 处理 toolId 条件
      if (params.toolId !== -1) {
        conditions.push('tool_id = ?');
        values.push(params.toolId);
      }

      if(params.taskUrl) {
        conditions.push('target_url LIKE ?');
        values.push(`%${params.taskUrl}%`);
      }

      // 处理 taskName模糊查询
      if (params.taskName) {
        conditions.push('task_name LIKE ?');
        values.push(`%${params.taskName}%`);
      }

      // 构建 WHERE 子句
      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 计算总数
      const countSql = `SELECT COUNT(*) as total FROM task_config ${whereClause}`;
      const totalResult = await db.query<[{total: number}]>(countSql, values);
      const total = totalResult[0].total;

      // 构建分页查询
      const offset = (params.pageNum - 1) * params.pageSize;
      const sql = `
        SELECT * FROM task_config
        ${whereClause}
        ORDER BY gmt_create DESC
        LIMIT ? OFFSET ?
      `;
      // 添加分页参数
      values.push(params.pageSize, offset);
      // 执行查询
      const list = await db.query<TaskConfigRepositoryType[]>(sql, values);

      return {
        total,
        taskList: list
      };
    } catch (error) {
      console.error('查询任务配置失败:', error);
      throw error;
    }
  }

  /**
   * 创建或者更新任务
   * @param params
   * @param userName 用户名
   * @returns 成功返回taskId，失败返回-1
   */
  async createOrUpdateTaskConfig(params: TaskConfigCreateOrUpdateType, userName: string): Promise<number> {
    try {
      const {
        taskId,
        taskName,
        taskTarget,
        toolId,
        startDate,
        endDate,
        collectionDate,
        collectionTime
      } = params;
      const toolConfigDao = new ToolConfigDao();
      const toolConfig = await toolConfigDao.getToolConfigByToolId(toolId);
      const toolName = toolConfig?.tool_name || ''
      const targetUrlJson = JSON.stringify(taskTarget);
      const timeListStr = collectionTime.join(',');
      let sql: string;
      let values: any[];

      if (taskId) {
        // 更新前先验证任务是否属于该用户
        const checkSql = `
          SELECT id FROM task_config
          WHERE id = ? AND user_name = ? AND \`delete\` = 1
        `;
        const [task] = await db.query<any[]>(checkSql, [taskId, userName]);
        if (!task || task.length === 0) {
          console.error('任务不存在或不属于该用户:', taskId);
          return -1;
        }

        sql = `
          UPDATE task_config
          SET
            task_name = ?,
            target_url = ?,
            tool_id = ?,
            start_time = ?,
            end_time = ?,
            date_range = ?,
            time_list = ?,
            tool_name = ?,
            gmt_modified = NOW()
          WHERE id = ? AND user_name = ?
        `;
        values = [
          taskName,
          targetUrlJson,
          toolId,
          startDate,
          endDate,
          collectionDate,
          timeListStr,
          toolName,
          taskId,
          userName
        ];
      } else {
        sql = `
          INSERT INTO task_config (
            task_name,
            target_url,
            tool_id,
            start_time,
            end_time,
            date_range,
            time_list,
            tool_name,
            user_name,
            gmt_create,
            gmt_modified
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        values = [
          taskName,
          targetUrlJson,
          toolId,
          startDate,
          endDate,
          collectionDate,
          timeListStr,
          toolName,
          userName
        ];
      }
      const result = await db.execute(sql, values);
      
      // 如果是新建任务，返回新插入的ID；如果是更新任务，返回原taskId
      if (!taskId) {
        // 新建任务，获取插入的ID
        const insertId = (result as any).insertId;
        return insertId || -1;
      } else {
        // 更新任务，返回原taskId
        return taskId;
      }
    } catch (error) {
      console.error('创建或更新任务失败:', error);
      return -1;
    }
  }

  /**
   * 删除任务（软删除）
   * @param taskId 任务ID
   * @param userName 用户名
   * @returns 成功返回被删除的taskId，失败返回-1
   */
  async deleteTaskConfig(taskId: number, userName: string): Promise<number> {
    try {
      // 先检查任务是否存在且未删除，同时验证是否属于该用户
      const checkSql = `
        SELECT id, \`delete\`
        FROM task_config
        WHERE id = ? AND user_name = ? AND \`delete\` = 1
      `;
      const [task] = await db.query<any[]>(checkSql, [taskId, userName]);
      if (!task || task.length === 0) {
        console.error('任务不存在、已删除或不属于该用户:', taskId);
        return -1;
      }

      // 执行软删除
      const sql = `
        UPDATE task_config
        SET
          \`delete\` = 2,
          gmt_modified = NOW()
        WHERE id = ? AND user_name = ? AND \`delete\` = 1
      `;

      const result = await db.execute(sql, [taskId, userName]);

      // 检查是否有记录被更新
      const affectedRows = (result as any).affectedRows;
      if (affectedRows > 0) {
        return taskId;
      } else {
        return -1;
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      return -1;
    }
  }

  /**
   * 根据所有任务列表查询toolId
   */
  async getAllToolId(userName: string): Promise<number[]> {
    const sql = `
      SELECT DISTINCT tool_id FROM task_config WHERE \`delete\` = 1 AND user_name = ?
    `;
    const result = await db.query<{tool_id: number}[]>(sql, [userName]);
    return result?.map(item => item.tool_id) || [];
  }

  async getTaskConfigByTaskId(taskId: number, userName: string): Promise<TaskConfigRepositoryType> {
    const sql = `
      SELECT * FROM task_config WHERE id = ? AND \`delete\` = 1 AND user_name = ?
    `;
    const result = await db.query<TaskConfigRepositoryType[]>(sql, [taskId, userName]);
    return result[0] as TaskConfigRepositoryType
  }

  async queryAllTaskNames(userName: string): Promise<string[]> {
    const sql = `
      SELECT task_name FROM task_config WHERE \`delete\` = 1 AND user_name = ?
    `;
    const result: TaskConfigRepositoryType[] = await db.query(sql, [userName]);
    return result?.map(item => item.task_name) || [];
  }
}