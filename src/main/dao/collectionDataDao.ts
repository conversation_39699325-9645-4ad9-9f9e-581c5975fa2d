import { db } from './db-connection';
import { CollectionDataType } from '../../renderer/types/collectionData';
import { TaskConfigDao } from './taskConfigDao';
import { CollectionDataRepositoryType } from '../../renderer/types/repository/collectionData';

/**
 * 采集数据存储层
 */
export class CollectionDataDao {
  async saveCollectionData(params: CollectionDataType, userName: string, collectionId: string) {
    const taskDao = new TaskConfigDao()
    const taskPO = await taskDao.getTaskConfigByTaskId(params.missionId, userName)
    const { tool_id, tool_name } = taskPO
    const { data, time, missionId, date } = params
    const message = params.status === 0 ? `采集成功，采集数据行数${data.length}` : `采集失败，${params.message}`
    const sql = `
      INSERT INTO data_info (user_name, tool_id, task_id, task_name, ext_info, collection_date, collection_time, collection_line, collection_status, message, collection_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    const result = await db.query(sql, [userName, tool_id, missionId, tool_name, JSON.stringify(data), date, time, data.length, params.status, message, collectionId])
    return result
  }

  /**
   * 通过采集数据表算出采集核心数据
   * @returns 
   */
  async queryCollectionCoreData() {
    const sql = `SELECT
    SUM(collection_line) AS total_collection_line,
    COUNT(*) AS total_count,
    SUM(CASE WHEN collection_status = 0 THEN 1 ELSE 0 END) / COUNT(*) AS success_rate
FROM
    data_info;`
    const result: any[] = await db.query(sql)
    
    // 将成功率从小数转换为百分比，100%时只显示100，其他情况保留两位小数
    const successRate = result?.[0].success_rate || 0
    const executeSuccRate = successRate === 1 ? 100 : Number((successRate * 100).toFixed(2))
    
    return {
      /** 采集总次数 */
      collectionTimes: result?.[0].total_count || 0,
      /** 采集总行数 */
      collectionLine: result?.[0].total_collection_line || 0,
      /** 采集成功率（百分比，100%时显示100，其他保留两位小数） */
      executeSuccRate: executeSuccRate
    }
  }

  /**
   * 通过collectionId查询采集数据
   * @param collectionId 采集数据唯一标识符
   * @returns 采集数据
   */
  async getCollectionDataById(collectionId: string, userName: string) {
    const sql = `SELECT * FROM data_info WHERE collection_id = ? AND user_name = ?`
    const result: CollectionDataRepositoryType[] = await db.query(sql, [collectionId, userName])
    return result?.[0] || null
  }

}