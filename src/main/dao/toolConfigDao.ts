import { db } from './db-connection';
import { ToolConfigRepositoryType } from '../../renderer/types/repository/toolConfig';

export class ToolConfigDao {
  /**
   * 获取工具配置列表
   * @param userName 用户名
   * @returns 工具配置列表
   */
  async getToolConfig(): Promise<ToolConfigRepositoryType[]> {
    try {
      const sql = 'SELECT * FROM tool_config WHERE \`delete\` = 1'
      const result = await db.query(sql );
      return result as ToolConfigRepositoryType[]
    } catch (error) {
      console.error('获取工具配置失败:', error);
      return [];
    }
  }

  /**
   * 根据工具ID获取工具配置
   * @param toolId 工具ID
   * @param userName 用户名
   * @returns 工具配置
   */
  async getToolConfigByToolId(toolId: number): Promise<ToolConfigRepositoryType | null> {
    try {
      const sql = 'SELECT * FROM tool_config WHERE id = ? AND \`delete\` = 1'
      const result: ToolConfigRepositoryType[] = await db.query(sql, [toolId]);
      return result.length > 0 ? result[0] as ToolConfigRepositoryType : null;
    } catch (error) {
      console.error('根据工具ID获取工具配置失败:', error);
      return null;
    }
  }
}