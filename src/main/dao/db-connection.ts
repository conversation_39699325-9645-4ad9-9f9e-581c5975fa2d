import mysql, { Pool, PoolConnection, PoolOptions } from 'mysql2/promise';

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database?: string;
}

class DbConnection {
  private static instance: DbConnection;
  private pool: Pool | null = null;

  private constructor() {}

  public static getInstance(): DbConnection {
    if (!DbConnection.instance) {
      DbConnection.instance = new DbConnection();
    }
    return DbConnection.instance;
  }

  /**
   * 检查数据库是否已连接
   * @returns 返回是否已连接
   */
  public isConnected(): boolean {
    return this.pool !== null;
  }

  /**
   * 初始化数据库连接池
   * @param config 数据库配置
   * @returns 返回连接是否成功
   */
  public async initializeConnection(config: DatabaseConfig): Promise<boolean> {
    try {
      // 如果已存在连接池，先销毁
      if (this.pool) {
        await this.pool.end();
      }

      // 创建新的连接池
      this.pool = mysql.createPool({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0
      });
      // 测试连接
      await this.testConnection();
      return true;
    } catch (error) {
      console.error('数据库连接初始化失败:', error);
      this.pool = null;
      return false;
    }
  }

  /**
   * 测试数据库连接
   */
  private async testConnection(): Promise<void> {
    if (!this.pool) {
      throw new Error('数据库连接池未初始化');
    }
    const connection = await this.pool.getConnection();
    try {
      await connection.ping();
    } finally {
      connection.release();
    }
  }

  /**
   * 检查连接池是否已初始化
   */
  private checkConnection() {
    if (!this.pool) {
      throw new Error('请先初始化数据库连接');
    }
  }

  public async getConnection(): Promise<PoolConnection> {
    this.checkConnection();
    try {
      const connection = await this.pool!.getConnection();
      return connection;
    } catch (error) {
      console.error('获取数据库连接失败:', error);
      throw error;
    }
  }

  public async query<T>(sql: string, values?: any[]): Promise<T> {
    this.checkConnection();
    const connection = await this.getConnection();
    try {
      const [results] = await connection.query(sql, values);
      return results as T;
    } catch (error) {
      console.error('执行查询失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  public async execute(sql: string, values?: any[]): Promise<any> {
    this.checkConnection();
    const connection = await this.getConnection();
    try {
      const [result] = await connection.execute(sql, values);
      return result;
    } catch (error) {
      console.error('执行SQL失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  public async closeConnection(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }
}

export const db = DbConnection.getInstance();