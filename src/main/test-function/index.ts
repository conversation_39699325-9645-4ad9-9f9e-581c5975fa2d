import { ipcMain } from "electron"
import { createOrUpdateTask, deleteTask, fetchCoreMetric, fetchTaskList, previewExecuteFile, queryAllToolId, queryTaskRecord, saveTencentCosConfig } from "../../renderer/api/common"
import { COS_CONFIG } from "../config/cosconfig"
import { CollectionService } from "../services/collection-service"
import { CosStorage } from "../cos-storage"
import { DingTalkService } from "../services/dingtalk-service"
import { runAgent } from "../../browser-agent"

ipcMain.on('query-task-list', async (event) => {
  const params = {
    toolId: -1,
    pageNum: 1,
    pageSize: 10,
  }
  fetchTaskList(params).then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('query-tool-config', async (event) => {
  queryAllToolId().then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('db-test-connection', async (event) => {
  const params = {
    host: 'bj-cdb-3lmp545o.sql.tencentcdb.com',
    account: 'root',
    password: 'meituanv5!',
    port: 29830,
    userName: 'zhicezhu',
    ...COS_CONFIG,
  }
  // @ts-ignore
  saveTencentCosConfig(params).then((res) => {
    console.log('res', res)
  })
})


ipcMain.on('create-task', async (event) => {
  const params = {
    taskName: '这是第一条测试任务',
    taskTarget: [
      {
        name: '测试酒店1',
        url: 'https://www.baidu.com'
      },
      {
        name: '测试酒店2',
        url: 'https://www.google.com'
      }
    ],
    toolId: 1,
    toolName: '工具1',
    startDate: '2025-07-17',
    endDate: '2025-07-20',
    collectionTime: ['00:00:00', '01:00:00'],
    collectionDate: 1,
    collectionMetric: [1, 2, 3]
  }
  createOrUpdateTask(params).then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('delete-task', async (event) => {
  const taskId = 3
  deleteTask(taskId).then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('query-task-record', async (event) => {
  const taskId = 4
  queryTaskRecord(taskId).then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('cos-test-upload', async (event) => {
  const cosStorage = new CosStorage()
  const testData = {
    'name': '李四',
    'age': 25,
    'city': '北京',
    'gender': '男',
    'phone': '13800138000',
    'email': '<EMAIL>',
    'address': '北京市海淀区',
  }
  const key = 'data/test3.json'
  await cosStorage.uploadJson(testData, key)
  const staticUrl = cosStorage.getStaticWebsiteUrl(key)
  console.log('静态网站URL:', staticUrl)
})


/**
 * 采集成功
 * 1.数据写入对应DB
 * 2.执行日志记录
 */
ipcMain.on('collection-complete', async (event) => {
  const params = {
    "date": "2025-07-12", // 采集日期
    "time": "11:30:16", // 采集时间
    "missionId": 11, // 任务ID（要根据任务 ID 查到任务名称）
    "status": 0, // 0-采集成功；1-采集异常
    "message": "",
    "data": [
        {
            "checkInDate": "2025-07-20", // 入住日期
            "hotelName": "昆泰酒店",
            "roomName": "房间1",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "2分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [
                "促销 1",
                "促销2"
            ],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
            "checkInDate": "2025-07-21",
            "hotelName": "南极洲团购测试",
            "roomName": "房间1",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "1分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
            "checkInDate": "2025-07-22",
            "hotelName": "测试测试mumuxixi",
            "roomName": "房间2",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "2分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
          "checkInDate": "2025-07-22",
          "hotelName": "测试酒店111",
          "roomName": "房间2",
          "salePrice": "￥1000",
          "lowestPrice": "￥100",
          "breakfast": "2分早餐",
          "cancelRule": "可取消",
          "confirmRule": "立即确认",
          "payWay": "在线付",
          "promotion": [],
          "roomBasicInfo": [
              "有窗",
              "43 平"
          ]
      },
      {
        "checkInDate": "2025-07-22",
        "hotelName": "测试酒店222",
        "roomName": "房间2",
        "salePrice": "￥1000",
        "lowestPrice": "￥100",
        "breakfast": "2分早餐",
        "cancelRule": "可取消",
        "confirmRule": "立即确认",
        "payWay": "在线付",
        "promotion": [],
        "roomBasicInfo": [
            "有窗",
            "43 平"
        ]
    },
    {
      "checkInDate": "2025-07-22",
      "hotelName": "测试酒店333",
      "roomName": "房间2",
      "salePrice": "￥1000",
      "lowestPrice": "￥100",
      "breakfast": "2分早餐",
      "cancelRule": "可取消",
      "confirmRule": "立即确认",
      "payWay": "在线付",
      "promotion": [],
      "roomBasicInfo": [
          "有窗",
          "43 平"
      ]
  }
    ]
}
  CollectionService.completeCollectionHandler(params)
  console.log('采集成功模拟并存储对应数据')
})

ipcMain.on('insert-fail-collection', async (event) => {
  const params = {
    "date": "2025-07-14", // 采集日期
    "time": "15:30:16", // 采集时间
    "missionId": 11, // 任务ID（要根据任务 ID 查到任务名称）
    "status": 1, // 0-采集成功；1-采集异常
    "message": "目标网页结构可能已更新，请联系技术支持",
    "data": [
        {
            "checkInDate": "2025-07-20", // 入住日期
            "hotelName": "酒店1",
            "roomName": "房间1",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "2分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [
                "促销 1",
                "促销2"
            ],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
            "checkInDate": "2025-07-21",
            "hotelName": "酒店1",
            "roomName": "房间1",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "1分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
            "checkInDate": "2025-07-22",
            "hotelName": "酒店1",
            "roomName": "房间2",
            "salePrice": "￥1000",
            "lowestPrice": "￥100",
            "breakfast": "2分早餐",
            "cancelRule": "可取消",
            "confirmRule": "立即确认",
            "payWay": "在线付",
            "promotion": [],
            "roomBasicInfo": [
                "有窗",
                "43 平"
            ]
        },
        {
          "checkInDate": "2025-07-22",
          "hotelName": "昆泰酒店",
          "roomName": "房间2",
          "salePrice": "￥1000",
          "lowestPrice": "￥100",
          "breakfast": "2分早餐",
          "cancelRule": "可取消",
          "confirmRule": "立即确认",
          "payWay": "在线付",
          "promotion": [],
          "roomBasicInfo": [
              "有窗",
              "43 平"
          ]
      },
      {
        "checkInDate": "2025-07-22",
        "hotelName": "南极洲",
        "roomName": "房间2",
        "salePrice": "￥1000",
        "lowestPrice": "￥100",
        "breakfast": "2分早餐",
        "cancelRule": "可取消",
        "confirmRule": "立即确认",
        "payWay": "在线付",
        "promotion": [],
        "roomBasicInfo": [
            "有窗",
            "43 平"
        ]
    }
    ]
}
  CollectionService.completeCollectionHandler(params)
  console.log('采集成功模拟并存储对应数据')
})

ipcMain.on('query-core-metric', async (event) => {
  fetchCoreMetric().then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('preview-execute-file', async (event) => {
  const fileId = 21
  previewExecuteFile(fileId).then((res) => {
    console.log('res', res)
  })
})

ipcMain.on('report-ding-talk', async (event) => {
  const result = DingTalkService.sendFailNotification('XC测试工具', '目标网页结构可能已更新，请联系技术支持')

  console.log('result', result)
})


ipcMain.on('execute-bua-task', async(event, taskContent) => {
  const callback = console.log
  const runResult = await runAgent(taskContent, [], callback)
  return runResult
})