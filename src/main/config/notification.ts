// 后续全部需要替换为 getConfig，不能写死在这里

export interface NotificationConfig {
    type: 'SMS' | 'Dingtalk';
}


export interface SMSConfig {
    type: 'SMS';
    sdkAppId: string;
    appKey: string;
}

export interface DingtalkConfig {
    type: 'Dingtalk';
    agentId: string;
    clientId: string;
    clientSecret: string;
}

// const smsConfig
const SMSExampleConfig: SMSConfig = {
    type: 'SMS',
    sdkAppId: '1401014469',
    appKey: 'a82e73688a32fb9050f4838f726e7b0e',
};

export interface CrawlSuccessNotificationInfo {
    type: 'CrawlSuccess';
    crawlerId?: string;
    excelLink?: string;
    dataInfo?: string;
}
interface CrawlErrorNotificationInfo {
    type: 'CrawlError';
    crawlerId?: string;
    errorMessage?: string;
}

export const DingTalkPushNotificationTemplate = {
    CrawlSuccess: (crawlerName: string, dataInfo: CrawlSuccessNotificationInfo) => {
        const title = `${dataInfo.crawlerId ? dataInfo.crawlerId + '上的' : ''}${crawlerName}采集成功！`
        // 如果有 dataInfo，那么拼接上，作为本次采集信息透传
        const contentText = dataInfo.dataInfo ? `采集数据信息：${dataInfo.dataInfo}` : '';

        return {
            title: title,
            text: `${title}${contentText}\n立即查看[本次采集数据](${dataInfo.excelLink})`
        }
    },
    CrawlError: (crawlerName: string, errorInfo: CrawlErrorNotificationInfo) => {
        const title = `${errorInfo.crawlerId ? errorInfo.crawlerId + '上的' : ''}${crawlerName}采集失败！`
        return {
            title: title,
            text: `${title}\n失败原因：${errorInfo.errorMessage ?? '未知'}`
        }
    }
}

const NotificationUsers = {
    SuccessUserMobilePhone: '15521335936',
    FailUserMobilePhone: '15521335936',
}