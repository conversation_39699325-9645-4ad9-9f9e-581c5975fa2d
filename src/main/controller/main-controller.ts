import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import { TaskConfigDao } from '../dao/taskConfigDao';
import { DeviceConfigDao } from '../dao/deviceConfigDao';
import { TaskConfigConverter } from '../converter/task-config-converter';
import { TaskConfigRepositoryResultType } from '../../renderer/types/repository/taskConfig';
import { TaskLogDao } from '../dao/taskLogDao';
import { TaskRecordConverter } from '../converter/task-record-converter';
import { db } from '../dao/db-connection';
import { CollectionDataDao } from '../dao/collectionDataDao';
import { TaskRunHistoryDao } from '../dao/taskRunHistoryDao';
import { TaskRunHistoryConverter } from '../converter/task-run-history-converter';
import { NoticeRecordDao } from '../dao/noticeRecordDao';
import { ipcMain } from 'electron/main';
import { runAgent } from '../../browser-agent';
import { NoticeRecordConverter } from '../converter/notice-record-converter';
import { DeviceConfigConverter } from '../converter/device-config-converter';
import { CollectionService } from '../services/collection-service';
import { CollectionDataConverter } from '../converter/collection-data-converter';
import { ToolConfigService } from '../services/tool-config-service';
console.log('正在初始化 Express 服务器...');

const app = express();

// 添加中间件
app.use(bodyParser.json());


/**
 * 任务列表查询
 */
app.get('/api/clairvoyant/queryTaskList', async (req: Request, res: Response) => {
  try {
    const userName = req.headers['user-name'] as string
    // 获取查询参数并处理默认值
    const params = {
      toolId: Number(req.query.toolId) || -1,
      pageNum: Number(req.query.pageNum) || 1,
      pageSize: Number(req.query.pageSize) || 10,
      taskName: req.query.taskName as string,
      taskUrl: req.query.taskUrl as string,
    };
    const taskConfigDao = new TaskConfigDao();
    const taskConfig: TaskConfigRepositoryResultType = await taskConfigDao.getTaskConfig(params, userName);
    const result = TaskConfigConverter.convertTaskConfigResponse(taskConfig);
    return res.json({
      code: 10000,
      data: result,
      error: null
    });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '获取任务列表失败'
      }
    });
  }
});

/**
 * 创建或更新任务
 */
app.post('/api/clairvoyant/createOrUpdateTask', async (req: Request, res: Response) => {
  try {
    const params = req.body;
    const userName = req.headers['user-name'] as string
    const taskConfigDao = new TaskConfigDao();
    const result = await taskConfigDao.createOrUpdateTaskConfig(params, userName);
    return res.json({
      code: 10000,
      data: result,
      error: null
    });
  } catch (error) {
    console.error('创建或更新任务失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '创建或更新任务失败'
      }
    });
  }
});

app.post('/api/clairvoyant/deleteTask', async (req: Request, res: Response) => {
  try {
    const params = req.body;
    const userName = req.headers['user-name'] as string
    const taskConfigDao = new TaskConfigDao();
    const result = await taskConfigDao.deleteTaskConfig(params.taskId, userName);
    return res.json({
      code: 10000,
      data: result,
      error: null
    });
  } catch(error) {
    console.error('删除任务失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '删除任务失败'
      }
    });
  }
})

/**
 * 工具管理接口
 */
app.get('/api/clairvoyant/queryToolConfig', async (req: Request, res: Response) => {
  try {
    const result = await ToolConfigService.queryAllToolConfig()
    return res.json({
      code: 10000,
      data: result,
      error: null
    });
  } catch (error) {
    console.error('获取工具配置失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '获取工具配置失败'
      }
    });
  }
});

/**
 * 任务执行记录查询
 */
app.get('/api/clairvoyant/queryTaskRecord', async (req: Request, res: Response) => {
  try {
    const userName = req.headers['user-name'] as string
    const { taskId } = req.query;
    const taskLogDao = new TaskLogDao();
    const taskRecord = await taskLogDao.queryTaskRecord(Number(taskId), userName);
    const result = TaskRecordConverter.convertTaskRecordResponse(taskRecord);
    return res.json({
      code: 10000,
      data: result,
      error: null
    });
  } catch (error) {
    console.error('查询任务执行记录失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '查询任务执行记录失败'
      }
    });
  }
})

/** 根据当前任务查询全部toolsId */
app.get('/api/clairvoyant/queryAllToolId', async (req: Request, res: Response) => {
  try {
    const userName = req.headers['user-name'] as string
    const taskConfigDao = new TaskConfigDao();
    const toolIds = await taskConfigDao.getAllToolId(userName);
    return res.json({
      code: 10000,
      data: toolIds,
      error: null
    });
  } catch (error) {
    console.error('查询所有工具id失败:', error);
    return res.status(500).json({
      code: 50000,
      data: [],
      error: {
        msg: '查询所有工具id失败'
      }
    });
  }
})

/**
 * 保存腾讯云配置
 * 目前先进行数据库连接测试
 */
app.post('/api/clairvoyant/saveTencentCosConfig', async (req: Request, res: Response) => {
  try {
    const params = req.body
    const { host, port, account, password } = params
    const connected = await db.initializeConnection({
      host,port, user: account, password, database: 'hl_db'
    })
    if(!connected) {
      return res.json({
        code: 10001,
        data: null,
        error: {
          msg: '数据库连接失败'
        }
      })
    }
    const deviceConfigDao = new DeviceConfigDao();
    await deviceConfigDao.saveOrUpdateDeviceConfig(params);
    return res.json({
      code: 10000,
      data: true,
      error: null
    })
  } catch(error) {
    console.error('保存腾讯云配置失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '保存腾讯云配置失败'
      }
    });
  }
})

/**
 * 首页仪表盘接口
 */
app.get('/api/clairvoyant/queryCoreMetric', async (req: Request, res: Response) => {
  try {
    const userName = req.headers['user-name'] as string
    const collectionDao = new CollectionDataDao()
    const collectionData = await collectionDao.queryCollectionCoreData()
    const taskRunHistoryDao = new TaskRunHistoryDao()
    /** 最近任务执行记录 */
    const taskRunHistory = await taskRunHistoryDao.queryRecentTaskHistory(userName)
    const recentTaskStatus = TaskRunHistoryConverter.convertRecentTask(taskRunHistory)
    const noticeDao = new NoticeRecordDao()
    const noticeList = await noticeDao.queryNoticeList()
    /** 系统公告 */
    return res.json({
      code: 10000,
      data: {
        ...collectionData,
        recentTaskStatus,
        noticeList: NoticeRecordConverter.convertNoticeList(noticeList)
      },
      error: null
    })
  } catch(error) {
    console.error('查询采集核心数据失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '查询采集核心数据失败'
      }
    });
  }
})

/**
 * 获取全部成功的执行记录
 */
app.get('/api/clairvoyant/queryAllExecuteRecord', async (req: Request, res: Response) => {
  try {
    const userName = req.headers['user-name'] as string
    const params = {
      toolId: Number(req.query.toolId) || -1,
      pageNum: Number(req.query.pageNum) || 1,
      pageSize: Number(req.query.pageSize) || 10,
      fileName: req.query.fileName as string || '',
      startDate: req.query.startDate as string || '',
      endDate: req.query.endDate as string || '',
    };
    const runHistoryDao = new TaskRunHistoryDao()
    const result = await runHistoryDao.queryAllSuccessTaskHistory(userName, params)
    return res.json({
      code: 10000,
      data: {
        recordList: TaskRunHistoryConverter.convertToExecuteRecordList(result.recordList),
        total: result.total,
      },
      error: null
    })
  } catch(error) {
    console.error('查询执行记录失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '查询执行记录失败'
      }
    });
  }
})

/**
 * 删除单条执行记录
 */
app.post('/api/clairvoyant/deleteExecuteRecord', async (req: Request, res: Response) => {
  try {
    const params = req.body
    const userName = req.headers['user-name'] as string
    const { fileId } = params
    const runHistoryDao = new TaskRunHistoryDao()
    const result = await runHistoryDao.deleteSingleTaskHistory(fileId, userName)
    return res.json({
      code: 10000,
      data: result,
      error: null
    })
  } catch(error) {
    console.error('删除执行记录失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '删除执行记录失败'
      }
    });
  }
})

/**
 * 智能操作并解析
 */

app.post('/api/ai/bua/submitAutoCrawlTask', async (req: Request, res: Response) => {
  // 任务信息
  const { taskContent } = req.body
  // callback, 使用 postMessage
  // const callback = (status: 'success' | 'error' | 'finish', msg: string) => {
  //   ipcMain.emit('agent-process', status, msg);
  // }
  const callback = console.log

  try {
    const runResult = runAgent(taskContent, [], callback)
    return res.json({
      code: 10000,
      data: {
        message: '执行成功',
        // 后面需要返回真实的任务 ID，可以根据这个任务 ID 查询结果
        taskId: -1
      },
      error: null
    })
  } catch(error) {
    console.error('创建智能流程失败:', error);
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '创建智能流程失败'
      }
    });
  }
})

/**
 * 获取腾讯云配置
 */
app.get('/api/clairvoyant/getTencentCosConfig', async (req: Request, res: Response) => {
  const userName = req.headers['user-name'] as string
  const deviceConfigDao = new DeviceConfigDao()
  try {
    const deviceConfig = await deviceConfigDao.getDeviceConfig(userName)
    const result = DeviceConfigConverter.convertToDeviceConfig(deviceConfig)
    return res.json({
      code: 10000,
      data: result,
      error: null
    })
  } catch (error) {
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '获取腾讯云配置失败'
      }
    });
  }
})

/**
 * 预览采集数据
 */
app.get('/api/clairvoyant/previewExecuteFile', async (req: Request, res: Response) => {
  const params = req.query
  const userName = req.headers['user-name'] as string
  const fileId = Number(params.fileId)
  const limit = Number(params.limit) || 0
  const runHistoryDao = new TaskRunHistoryDao()
  try {
    const runHistory = await runHistoryDao.getTaskRunHistoryByFileId(fileId, userName)
    const collectionData = await CollectionService.getCollectionDataByFileId(fileId, userName)
    const result = CollectionDataConverter.convertCollectionDataResponse(collectionData, runHistory, limit)
    return res.json({
      code: 10000,
      data: result,
      error: null
    })
  } catch (error) {
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '预览执行文件失败'
      }
    });
  }
})

/**
 * 查询消息提醒
 */
app.get('/api/clairvoyant/getNotification', async (req: Request, res: Response) => {
  const userName = req.headers['user-name'] as string
  const startDate = req.query.startDate as string
  const endDate = req.query.endDate as string
  const runHistoryDao = new TaskRunHistoryDao()
  try {
    const failTaskHistory = await runHistoryDao.queryFailTaskHistoryByLimit(userName,  startDate, endDate)
    const messageNotice = await TaskRunHistoryConverter.convertFailTaskHistory(failTaskHistory)
    return res.json({
      code: 10000,
      data: messageNotice,
      error: null
    })
  } catch (error) {
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '查询消息通知失败'
      }
    });
  }
})

/**
 * 查询全部任务名称
 */
app.get('/api/clairvoyant/queryAllTaskNames', async (req: Request, res: Response) => {
  const userName = req.headers['user-name'] as string
  const taskConfigDao = new TaskConfigDao()
  try {
    const taskNames = await taskConfigDao.queryAllTaskNames(userName)
    return res.json({
      code: 10000,
      data: taskNames,
      error: null
  })
  } catch (error) {
    return res.status(500).json({
      code: 50000,
      data: null,
      error: {
        msg: '查询任务名称失败'
      }
    })
  }
})



const PORT = 3001;
app.listen(PORT, () => {
  console.log(`✅ API 服务器已成功启动，监听端口 ${PORT}`);
  console.log(`📍 测试链接: http://localhost:${PORT}/api/users`);
});