import { app, BrowserWindow, dialog, ipcMain } from 'electron'
import * as http from 'http'
import * as path from 'path'
import './controller/main-controller'
import './test-function'
import { db } from './dao/db-connection'
import { getStore, initStore } from './config/store'
import { getToolConfigEmitter, TOOL_ID_CONFIG } from '../workflow/basic/tools'
import { ToolConfigService } from './services/tool-config-service'
import { createHttpServer } from './init/http-server'

let mainWindow: BrowserWindow | null = null
let httpServer: http.Server | null = null

/** 创建浏览器窗口 */
const createWindow = () => {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    icon: path.join(__dirname, '../../assets/icon.svg'),
    show: false, // 先不显示，等加载完毕后显示
    title: 'Electron-Automa 通信桥',
  })

  // 加载应用的 index.html
  mainWindow.loadFile(path.join(__dirname, '../index.html'))

  // 当窗口准备好时显示
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
    console.log('初始化：Electron 应用已启动')
  })

  // 窗口关闭时的处理
  mainWindow.on('closed', () => {
    console.log('Electron 窗口被关闭，清空 mainWindow，并关闭 httpServer')
    mainWindow = null
    if (httpServer) {
      httpServer.close()
    }
  })
}

/** 保障页面初始化 */
const initMainWindow = () => {
  // 初始化 httpServer
  if (!httpServer || !httpServer.listening) {
    console.log('初始化：创建新的 httpServer')
    httpServer = createHttpServer(mainWindow)
  }

  // 初始化 BrowserWindow
  if (BrowserWindow.getAllWindows().length === 0) {
    console.log('创建新的窗口')
    createWindow()
  }
}

async function initDBConnection() {
  const config = (getStore()?.get('settings') as Record<string, any>) ?? {}
  const { host, port, account, password } = config
  // 数据库连接
  if (host && port && account && password) {
    try {
      await db.initializeConnection({
        host,
        port,
        user: account,
        password,
        database: 'hl_db',
      })
      console.log('数据库连接成功')
    } catch (error) {
      console.error('数据库连接失败:', error)
      dialog.showErrorBox('数据库连接失败', '请检查数据库配置')
      return
    }
  }
}

// 应用就绪时的处理
app
  .whenReady()
  .then(async () => {
    initStore()
    // 从 store 中获取数据库配置
    //@ts-ignore
    await initDBConnection()
    initMainWindow()
  })
  .catch((error: any) => {
    console.error('初始化失败:', error)
    dialog.showErrorBox('初始化失败', '请检查配置文件')
  })

// 所有窗口关闭时退出应用 (macOS 除外)
app.on('window-all-closed', async () => {
  if (db.isConnected()) {
    await db.closeConnection()
  }

  if (process.platform !== 'darwin') {
    if (httpServer) {
      httpServer.close()
    }
    // 确保所有窗口都已关闭
    app.quit()
  }
})

// macOS 激活处理
app.on('activate', () => {
  console.log('Electron 应用被激活')
  initMainWindow()
})

// IPC 消息处理
ipcMain.on('get-server-status', (event) => {
  event.reply('server-status', {
    running: httpServer?.listening || false,
    port: 3000,
  })
})

// 手动测试通知
ipcMain.on('test-notification', async (event) => {
  if (mainWindow) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '测试通知',
      message: '这是一个测试通知',
      detail: '用于验证通知功能是否正常工作',
    })
  }
})

// 处理打开 Chrome 扩展的 IPC 消息
ipcMain.on('open-chrome-extension', async (event, payload) => {
  const { toolId, customConfig } = payload

  const toolConfig = await ToolConfigService.queryToolConfigByToolId(toolId)

  if (!toolConfig || !toolConfig.workflowTemplate) {
    console.error('采集工具工作流配置获取失败')
    if (mainWindow) {
      dialog.showErrorBox(
        '采集工具执行失败',
        '采集工具工作流配置获取失败，请联系系统工作人员'
      )
    }
    return
  }

  const toolEmitter = getToolConfigEmitter(toolId)
  if (!toolEmitter) {
    console.error('采集工具触发器匹配失败')
    if (mainWindow) {
      dialog.showErrorBox(
        '采集工具执行失败',
        '采集工具触发器匹配失败，请联系系统工作人员'
      )
    }
    return
  }

  const result = await toolEmitter(
    toolConfig.workflowTemplate as any,
    customConfig
  )
  if (result.status !== 200) {
    console.error('采集工具执行失败', result.message)
    if (mainWindow) {
      dialog.showErrorBox('采集工具执行失败', result.message)
    }
    return
  }

  console.log('采集工具执行成功')
})

ipcMain.on('save-settings', async (event) => {
  const settings = {
    name: '张三',
    age: 25,
    city: '北京',
  }
  getStore()?.set('settings', settings)
})

ipcMain.on('get-settings', async (event) => {
  const result = getStore()?.get('settings')
  console.log('result', result)
})

// 用于存储消息的本地缓存
const messageCache = {
  messages: [],
  addMessage(message: { type: 'success' | 'error'; content: string }) {
    //@ts-ignore
    this.messages.push({
      ...message,
      timestamp: Date.now(),
      id: Date.now().toString(),
    })
    // 只保留最近的10条消息
    if (this.messages.length > 10) {
      this.messages.shift()
    }
  },
  getMessages() {
    const msgs = [...this.messages]
    this.messages = [] // 获取后清空
    return msgs
  },
}

// 添加IPC处理器
ipcMain.handle('get-cached-messages', () => {
  return messageCache.getMessages()
})

// 设置相关的IPC处理器
ipcMain.handle('save-settings', async (event, settings) => {
  getStore().set('settings', settings)
  await initDBConnection()
})

ipcMain.handle('get-settings', async (event) => {
  return getStore().get('settings')
})

// 添加清空设置的IPC处理器
ipcMain.handle('clear-settings', async (event) => {
  return getStore().delete('settings')
})
