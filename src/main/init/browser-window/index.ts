import { BrowserWindow } from 'electron'
import * as path from 'path'

export const createWindow = () => {
  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    icon: path.join(__dirname, '../../assets/icon.svg'),
    show: false, // 先不显示，等加载完毕后显示
    title: 'Electron-Automa 通信桥',
  })

  // 加载应用的 index.html
  mainWindow.loadFile(path.join(__dirname, '../index.html'))

  // 当窗口准备好时显示
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
    console.log('Electron 应用已启动')
  })

  // 窗口关闭时的处理
  mainWindow.on('closed', () => {
    console.log('Electron 窗口被关闭，清空 mainWindow，并关闭 httpServer')
    // mainWindow = null
    // if (httpServer) {
    //   httpServer.close()
    // }
  })

  return mainWindow
}
