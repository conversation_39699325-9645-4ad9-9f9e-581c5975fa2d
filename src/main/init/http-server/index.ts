import type { BrowserWindow } from 'electron'
import type { Server } from 'http'

import http from 'http'
import { dialog } from 'electron'

// 创建 HTTP 服务器监听 Automa 通知
export const createHttpServer = (mainWindow: BrowserWindow | null): Server => {
  const httpServer = http.createServer((req, res) => {
    // 设置 CORS 头，允许跨域请求
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

    if (req.method === 'OPTIONS') {
      res.writeHead(200)
      res.end()
      return
    }

    if (req.method === 'POST' && req.url === '/automa-complete') {
      let body = ''
      req.on('data', (chunk) => {
        body += chunk.toString()
      })

      req.on('end', () => {
        try {
          const data = JSON.parse(body || '{}')
          console.log('收到 Automa 工作流完成通知:', data)

          // 显示桌面通知弹窗
          if (mainWindow) {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Automa 工作流完成',
              message: '🎉 Automa 工作流已成功完成！',
              detail: `消息: ${data.message || '无附加信息'}\n时间: ${new Date(
                data.time || Date.now()
              ).toLocaleString()}`,
              buttons: ['确定', '查看详情'],
              defaultId: 0,
              cancelId: 0,
            })
          }

          // const resultData = {
          //   ...data,
          //   data: JSON.parse(data.data),
          // }
          // // 采集数据入库
          // CollectionService.completeCollectionHandler(resultData)

          // 向渲染进程发送通知
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('automa-notification', data)
          }

          res.writeHead(200, { 'Content-Type': 'application/json' })
          res.end(
            JSON.stringify({
              status: 'success',
              message: 'Notification received',
              timestamp: new Date().toISOString(),
            })
          )
        } catch (error) {
          console.error('处理 Automa 通知时出错:', error)
          res.writeHead(400, { 'Content-Type': 'application/json' })
          res.end(
            JSON.stringify({
              status: 'error',
              message: 'Invalid JSON data',
            })
          )
        }
      })
    } else if (req.method === 'GET' && req.url === '/status') {
      // 状态检查端点
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(
        JSON.stringify({
          status: 'running',
          app: 'Electron-Automa-Bridge',
          timestamp: new Date().toISOString(),
        })
      )
    } else {
      res.writeHead(404, { 'Content-Type': 'application/json' })
      res.end(
        JSON.stringify({
          status: 'error',
          message: 'Endpoint not found',
        })
      )
    }
  })

  const PORT = 3000
  httpServer.listen(PORT, 'localhost', () => {
    console.log(`🚀 HTTP 服务器已启动，监听端口 ${PORT}`)
    console.log(`📡 Automa 通知端点: http://localhost:${PORT}/automa-complete`)
    console.log(`📊 状态检查端点: http://localhost:${PORT}/status`)
  })

  httpServer.on('error', (error) => {
    console.error('HTTP 服务器错误:', error)
    if (mainWindow) {
      dialog.showErrorBox(
        '服务器启动失败',
        `无法启动 HTTP 服务器: ${error.message}`
      )
    }
  })

  return httpServer
}
