import { DeviceConfigRepositoryType } from "../../renderer/types/repository/device";

export class DeviceConfigConverter {
  public static convertToDeviceConfig(deviceConfig: DeviceConfigRepositoryType) {
    return {
      ...deviceConfig,
      userName: deviceConfig.user_name,
      bucketName: deviceConfig.bucket_name,
      secretId: deviceConfig.secret_id,
      secretKey: deviceConfig.secret_key,
      appId: deviceConfig.app_id,
      appKey: deviceConfig.app_key,
      agentId: deviceConfig.agent_id,
      clientId: deviceConfig.client_id,
      clientSecret: deviceConfig.client_secret,
      phone: deviceConfig.phone,
    }
  }
}