import dayjs from "dayjs";
import { TaskRecordUnionRepositoryType } from "../../renderer/types/repository/taskRecord";
import { TaskStatus } from "../../renderer/types/task";
import { TaskTargetType } from "./task-config-converter";

export class TaskRecordConverter {
  public static convertTaskRecordResponse(taskRecord: TaskRecordUnionRepositoryType | null) {
    if(!taskRecord) {
      return null
    }
    const { task_id, task_name, target_url, start_date, end_date, time_list, runHistory, tool_id } = taskRecord
    let taskTarget: TaskTargetType[] = [];
        try {
          taskTarget = JSON.parse(target_url);
        } catch (error) {
          console.error('解析 target_url 失败:', error);
          taskTarget = [];
        }
    return {
      taskId: task_id,
      taskName: task_name,
      taskTarget,
      toolId: tool_id,
      startDate: dayjs(start_date).format('YYYY-MM-DD'),
      endDate: dayjs(end_date).format('YYYY-MM-DD'),
      collectionTime: time_list.split(','),
      taskRecord: runHistory.map((item) => {
        const runStatus = Number(item.run_status)
        const taskSummary = runStatus === TaskStatus.Fail ? item.error_message : runStatus === TaskStatus.Success ? `任务执行成功，采集到${item.data_count}条数据` : ''
        return {
          taskStatus: Number(item.run_status),
          taskStartDate: item.collection_time,
          taskSummary: taskSummary,
          fileName: item.file_name
        }
      })
    }
  }
}