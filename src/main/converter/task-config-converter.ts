import dayjs from "dayjs";
import { TaskConfigRepositoryResultType } from "../../renderer/types/repository/taskConfig";

export interface TaskTargetType {
  name: string
  url: string
}

export class TaskConfigConverter {
  public static convertTaskConfigResponse(taskConfig: TaskConfigRepositoryResultType) {
    const { total, taskList } = taskConfig;
    return {
      total,
      taskList: taskList.map((item) => {
        let targetList: TaskTargetType[] = [];
        try {
          targetList = JSON.parse(item.target_url);
        } catch (error) {
          console.error('解析 target_url 失败:', error);
          targetList = []; // 解析失败时设置为空数组
        }

        return {
          taskId: item.id,
          taskName: item.task_name,
          taskTarget: targetList,
          toolId: item.tool_id,
          toolName: item.tool_name,
          startDate: dayjs(item.start_time).format('YYYY-MM-DD'),
          // 采集范围
          collectionDate: item.date_range,
          endDate: dayjs(item.end_time).format('YYYY-MM-DD'),
          collectionTime: item.time_list.split(','),
          /** 1 -- 运行中 2 -- 已删除 */
          taskStatus: item.delete
        }
      })
    }
  }
}