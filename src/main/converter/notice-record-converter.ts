import dayjs from "dayjs"
import { NoticeListRepositoryType } from "../../renderer/types/repository/noticeList"

export class NoticeRecordConverter {
  public static convertNoticeList(noticeList: NoticeListRepositoryType[]) {
    return noticeList.map((item) => {
      return {
        id: item.id,
        title: item.title,
        content: item.content,
        updateTime: dayjs(item.update_time).format('YYYY-MM-DD')
      }
    })
  }
}