import dayjs from "dayjs"
import { ToolConfigRepositoryType } from "../../renderer/types/repository/toolConfig"
import { ToolMetricItemType, UpdateRecordItemType } from "../../renderer/types/tools"

export class ToolConfigConverter {
  public static convertToolConfigResponse(toolConfig: ToolConfigRepositoryType[]) {
    return toolConfig.map((item) => {
      let metricList = [] as ToolMetricItemType[]
      let updateRecord = [] as UpdateRecordItemType[]
      let workflowTemplate = ''
      try {
        metricList = JSON.parse(item.metric_list)
      } catch (error) {
        metricList = []
      }
      try {
        updateRecord = JSON.parse(item.update_record)
      } catch (error) {
        updateRecord = []
      }
      try {
        workflowTemplate = JSON.parse(item.workflow_template)
      } catch (error) {
        workflowTemplate = ''
      }
      return {
        toolId: item.id,
        toolName: item.tool_name,
        toolAvatar: item.tool_avatar,
        toolDesc: item.tool_desc,
        toolVersion: item.version,
        lastUpdateDate: dayjs(item.gmt_modified).format('YYYY-MM-DD'),
        toolMetric: metricList,
        urlRegex: item.url_regex,
        workflowTemplate: workflowTemplate,
        updateRecord: updateRecord.map(item => {
          return {
            ...item,
            updateDate: dayjs(item.updateDate).format('YYYY-MM-DD')
          }
        })
      }
    })
  }

  public static convertSingleToolConfigResponse(toolConfig: ToolConfigRepositoryType | null) {
    if (!toolConfig) {
      return null
    }
    let metricList = [] as ToolMetricItemType[]
    let updateRecord = [] as UpdateRecordItemType[]
    let workflowTemplate = ''
    try {
      metricList = JSON.parse(toolConfig.metric_list)
    } catch (error) {
      metricList = []
    }
    try {
      updateRecord = JSON.parse(toolConfig.update_record)
    } catch (error) {
      updateRecord = []
    }
    try {
      workflowTemplate = JSON.parse(toolConfig.workflow_template)
    } catch (error) {
      workflowTemplate = ''
    }
    return {
      toolId: toolConfig.id,
      toolName: toolConfig.tool_name,
      toolAvatar: toolConfig.tool_avatar,
      toolDesc: toolConfig.tool_desc,
      toolVersion: toolConfig.version,
      lastUpdateDate: dayjs(toolConfig.gmt_modified).format('YYYY-MM-DD'),
      toolMetric: metricList,
      urlRegex: toolConfig.url_regex,
      workflowTemplate: workflowTemplate,
      updateRecord: updateRecord.map(item => {
        return {
          ...item,
          updateDate: dayjs(item.updateDate).format('YYYY-MM-DD')
        }
      })
    }
  }
}