import { CollectionDataItemType, CollectionDataType } from "../../renderer/types/collectionData";
import { CollectionDataRepositoryType } from "../../renderer/types/repository/collectionData";
import { TaskRunHistoryRepositoryType } from "../../renderer/types/repository/taskRunHistory";
import { COLLECTION_TABLE_COLUMNS } from "../constants";

export class CollectionDataConverter {
  public static convertCollectionDataResponse(collectionData: CollectionDataRepositoryType | null, runHistory: TaskRunHistoryRepositoryType, limit?: number) {
    if(!collectionData) {
      return null
    }
    let taskTarget = []
    try {
      taskTarget = JSON.parse(runHistory.target_url)
    } catch (error) {
      taskTarget = []
    }
    let originData = [] as CollectionDataItemType[]
    try {
      originData = JSON.parse(collectionData.ext_info)
    } catch (error) {
      originData = [] as CollectionDataItemType[]
    }

    // 处理前limit条数据
    if (limit && limit > 0 && Array.isArray(originData)) {
      originData = originData.slice(0, limit)
    }

    return {
      fileId: runHistory.id,
      fileName: runHistory.file_name,
      toolId: runHistory.tool_id,
      toolName: runHistory.tool_name,
      taskTarget,
      /** 采集时间 */
      collectionTime: `${collectionData.collection_date} ${collectionData.collection_time}`,
      /** 采集行数 */
      collectionLine: Number(collectionData.collection_line),
      tableData: CollectionDataConverter._convertToTableData(originData)
    }
  }

  private static _convertToTableData(originData: CollectionDataItemType[]) {
    const columns = COLLECTION_TABLE_COLUMNS
    return {
      columns,
      data: originData
    }
  }
}