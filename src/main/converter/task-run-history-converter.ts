import { TaskRunHistoryRepositoryType } from "../../renderer/types/repository/taskRunHistory"
import { ExecuteRecordItemType } from "../../renderer/types/file"
import dayjs from "dayjs"

export class TaskRunHistoryConverter {
  public static convertRecentTask(taskRunHistory: TaskRunHistoryRepositoryType[]) {
    return taskRunHistory.map((item) => {
      let poiNames: string[] = []
      try {
        poiNames = JSON.parse(item.poi_names)
      } catch (error) {
        poiNames = []
      }
      return {
        taskId: item.task_id,
        taskStartDate: dayjs(item.start_time).format('YYYY-MM-DD'),
        taskEndDate: dayjs(item.end_time).format('YYYY-MM-DD'),
        taskStatus: Number(item.run_status),
        toolName: item.tool_name,
        taskName: item.task_name,
        poiNames
      }
    })
  }

  /**
   * 转换为执行记录列表项
   * @param taskRunHistory 任务运行历史记录
   * @returns 执行记录列表项
   */
  public static convertToExecuteRecordList(taskRunHistory: TaskRunHistoryRepositoryType[]): ExecuteRecordItemType[] {
    return taskRunHistory.map((item) => {
      return {
        fileId: item.id,
        fileName: item.file_name,
        toolId: item.tool_id,
        toolName: item.tool_name,
        fileSize: 0,
        fileLine: item.data_count,
        createTime: item.collection_time
      }
    })
  }

  public static convertFailTaskHistory(taskRunHistory: TaskRunHistoryRepositoryType[]) {
    return taskRunHistory.map((item) => {
      return {
        taskId: item.id,
        content: item.error_message,
        updateTime: dayjs(item.gmt_create).valueOf()
      }
    })
  }
}