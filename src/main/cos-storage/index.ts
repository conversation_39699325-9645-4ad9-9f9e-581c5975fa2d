import Cos from 'cos-nodejs-sdk-v5'
import { COS_CONFIG } from '../config/cosconfig'

export class CosStorage {
  private cos: Cos
  private bucketName: string
  private region: string

  constructor() {
    this.bucketName = COS_CONFIG.bucketName
    this.region = COS_CONFIG.region
    // 初始化COS实例
    this.cos = new Cos({
      // 必选参数
      SecretId: COS_CONFIG.secretId,
      SecretKey: COS_CONFIG.secretKey,
      // 可选参数
      FileParallelLimit: 3, // 控制文件上传并发数
      ChunkParallelLimit: 8, // 控制单个文件下分片上传并发数，在同园区上传可以设置较大的并发数
      ChunkSize: 1024 * 1024 * 8, // 控制分片大小，单位 B，在同园区上传可以设置较大的分片大小
      Proxy: '',
      Protocol: 'https:',
      Timeout: 60000,
    })
  }

  /**
   * 上传文件到COS
   * @param filePath 本地文件路径
   * @param key 对象键名
   * @returns Promise<any>
   */
  public async uploadFile(filePath: string, key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.putObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
          Body: require('fs').createReadStream(filePath),
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 上传Buffer数据到COS
   * @param buffer 数据buffer
   * @param key 对象键名
   * @param contentType 内容类型，默认为application/octet-stream
   * @returns Promise<any>
   */
  public async uploadBuffer(
    buffer: Buffer,
    key: string,
    contentType: string = 'application/octet-stream'
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.putObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
          Body: buffer,
          ContentType: contentType,
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 上传JSON数据到COS
   * Automa的工作流模版文件，可直接通过浏览器访问打开
   * @param jsonData JSON数据对象
   * @param key 对象键名
   * @returns Promise<any>
   */
  public async uploadJson(jsonData: any, key: string): Promise<any> {
    const jsonString = JSON.stringify(jsonData, null, 2)
    const buffer = Buffer.from(jsonString, 'utf-8')
    return new Promise((resolve, reject) => {
      this.cos.putObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
          Body: buffer,
          ContentType: 'application/json; charset=utf-8',
          ContentEncoding: 'utf-8',
          CacheControl: 'no-cache',
          ContentDisposition: 'inline',
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 下载文件
   * @param key 对象键名
   * @param localPath 本地保存路径
   * @returns Promise<any>
   */
  public async downloadFile(key: string, localPath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.getObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
          Output: require('fs').createWriteStream(localPath),
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 获取文件访问URL
   * @param key 对象键名
   * @param expires 签名有效期，单位秒，默认3600秒
   * @returns Promise<string>
   */
  public async getSignedUrl(
    key: string,
    expires: number = 3600
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      this.cos.getObjectUrl(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
          Sign: true,
          Expires: expires,
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data.Url)
          }
        }
      )
    })
  }

  /**
   * 获取静态网站访问URL（推荐用于JSON文件）
   * @param key 对象键名
   * @returns string
   */
  public getStaticWebsiteUrl(key: string): string {
    // 静态网站域名格式：bucket-website.region.myqcloud.com
    const staticWebsiteDomain = `${COS_CONFIG.bucketName}.cos.${COS_CONFIG.region}.myqcloud.com`
    return `https://${staticWebsiteDomain}/${key}`
  }

  /**
   * 删除文件
   * @param key 对象键名
   * @returns Promise<any>
   */
  public async deleteFile(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.deleteObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 列出文件
   * @param prefix 前缀
   * @param maxKeys 最大返回数量
   * @returns Promise<any>
   */
  public async listFiles(
    prefix: string = '',
    maxKeys: number = 100
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.getBucket(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Prefix: prefix,
          MaxKeys: maxKeys,
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 检查文件是否存在
   * @param key 对象键名
   * @returns Promise<boolean>
   */
  public async fileExists(key: string): Promise<boolean> {
    try {
      await this.cos.headObject({
        Bucket: this.bucketName,
        Region: this.region,
        Key: key,
      })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取文件信息
   * @param key 对象键名
   * @returns Promise<any>
   */
  public async getFileInfo(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cos.headObject(
        {
          Bucket: this.bucketName,
          Region: this.region,
          Key: key,
        },
        (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        }
      )
    })
  }

  /**
   * 验证文件Content-Type
   * @param key 对象键名
   * @returns Promise<boolean>
   */
  public async verifyContentType(key: string): Promise<boolean> {
    try {
      const fileInfo = await this.getFileInfo(key)
      console.log('文件元数据:', fileInfo)
      const contentType = fileInfo.headers['content-type']
      console.log('Content-Type:', contentType)
      return contentType && contentType.includes('application/json')
    } catch (error) {
      console.error('验证Content-Type失败:', error)
      return false
    }
  }
}
