{"extVersion": "1.29.10", "name": "携程黄龙采集工作流0721", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "dXoy_a-flGA-XdFumMEsQ", "type": "BlockBasic", "initialized": false, "position": {"x": 385.2651320472992, "y": 1.2865364032326028}, "data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "triggers": [{"data": {"isUrlRegex": true, "supportSPA": false, "url": "github\\.com/AutomaApp/automa"}, "id": "rTa_4", "type": "visit-web"}], "type": "manual", "url": ""}, "label": "trigger"}, {"id": "omDH_l2oRzHjbHsBzvA82", "type": "BlockBasic", "initialized": false, "position": {"x": 2413.352819415057, "y": 276.0884118481668}, "data": {"disableBlock": false, "description": "打开酒店详情页", "url": "{{ <EMAIL> }}", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false, "onError": {"retry": true, "enable": true, "retryTimes": 2, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_DetailTabOpen", "type": "variable", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}, "settings": {"blockTimeout": 3000, "debugMode": false}}, "label": "new-tab"}, {"id": "mkkjt2u", "type": "BlockBasic", "initialized": false, "position": {"x": 2566.3980112839995, "y": 710.0782924472443}, "data": {"disableBlock": false, "description": "发送数据", "timeout": 5000, "context": "website", "code": "/**\n * 将原始数据对象转换为按日期聚合的格式\n * @param {Object} rawData - 原始数据对象，包含形如 \"YYYY-MM-dd_酒店名称\" 的属性\n * @param {string} missionId - 任务ID\n * @param {Object} options - 可选配置\n * @returns {Array} 转换后的数据数组\n */\nfunction formatDataByDate(rawData, missionId, options = {}) {\n  try {\n    // 参数验证\n    if (!rawData || typeof rawData !== 'object') {\n      throw new Error('rawData 必须是一个对象');\n    }\n\n    if (!missionId) {\n      throw new Error('missionId 不能为空');\n    }\n\n    const {\n      defaultStatus = 0,\n      defaultMessage = '数据采集成功',\n    } = options;\n\n    // 正则表达式匹配 \"YYYY-MM-dd_酒店名称\" 格式\n    const dateKeyPattern = /^(\\d{4}-\\d{2}-\\d{2})_(.+)$/;\n\n    // 按日期分组数据\n    const groupedByDate = {};\n\n    // 遍历原始数据对象的所有属性\n    Object.keys(rawData).forEach(key => {\n      try {\n        const match = key.match(dateKeyPattern);\n        if (match) {\n          const [, date, hotelName] = match;\n          const data = rawData[key];\n\n          // 验证数据结构\n          if (!data) {\n            console.warn(`键 ${key} 对应的数据为空`);\n            return;\n          }\n\n          // 如果该日期还没有记录，初始化\n          if (!groupedByDate[date]) {\n            groupedByDate[date] = {\n              hotels: [],\n              totalData: [],\n            };\n          }\n\n          // 添加酒店数据\n          groupedByDate[date].hotels.push({\n            hotelName,\n            originalKey: key,\n            data: data?.data || data,\n          });\n\n          // 合并所有数据到总数据中\n          if (data?.data && Array.isArray(data.data)) {\n            // 验证数组中的每个元素\n            const validData = data.data.filter(item => {\n              if (!item || typeof item !== 'object') {\n                console.warn('发现无效数据项:', item);\n                return false;\n              }\n              return true;\n            });\n            groupedByDate[date].totalData.push(...validData);\n          } else if (Array.isArray(data)) {\n            const validData = data.filter(item => {\n              if (!item || typeof item !== 'object') {\n                console.warn('发现无效数据项:', item);\n                return false;\n              }\n              return true;\n            });\n            groupedByDate[date].totalData.push(...validData);\n          } else {\n            console.warn(`键 ${key} 的数据格式不正确，期望包含 data 数组`);\n          }\n        }\n      } catch (itemError) {\n        console.error(`处理键 ${key} 时出错:`, itemError);\n        // 继续处理其他项目，不中断整个流程\n      }\n    });\n\n    // 检查是否找到了任何有效数据\n    if (Object.keys(groupedByDate).length === 0) {\n      console.warn('未找到任何符合日期格式的数据');\n      return JSON.stringify([{\n        date: new Date().toISOString().split('T')[0],\n        time: formatTime(new Date()),\n        missionId: missionId.toString(),\n        status: 1,\n        message: '未找到符合格式的数据',\n        data: [],\n      }]);\n    }\n\n    // 转换为最终格式\n    const result = Object.keys(groupedByDate).map(date => {\n      try {\n        const dateData = groupedByDate[date];\n        const now = new Date();\n\n        // 确定状态和消息\n        let status = defaultStatus;\n        let message = defaultMessage;\n\n        // 检查数据完整性\n        if (!dateData.totalData || dateData.totalData.length === 0) {\n          status = 1;\n          message = '未采集到有效数据';\n        }\n\n        return {\n          date,\n          time: formatTime(now),\n          missionId: missionId.toString(),\n          status,\n          message,\n          data: (dateData.totalData || []),\n          // 额外信息（可选）\n          meta: {\n            hotelCount: dateData.hotels ? dateData.hotels.length : 0,\n            hotelNames: dateData.hotels ? dateData.hotels.map(h => h.hotelName || '未知酒店') : [],\n            dataCount: dateData.totalData ? dateData.totalData.length : 0,\n          },\n        };\n      } catch (dateError) {\n        console.error(`处理日期 ${date} 的数据时出错:`, dateError);\n        return {\n          date,\n          time: formatTime(new Date()),\n          missionId: missionId.toString(),\n          status: 1,\n          message: `处理日期数据时出错: ${dateError.message}`,\n          data: [],\n        };\n      }\n    });\n\n    // 按日期排序\n    result.sort((a, b) => {\n      try {\n        return a.date.localeCompare(b.date);\n      } catch (sortError) {\n        console.error('排序时出错:', sortError);\n        return 0;\n      }\n    });\n\n    return JSON.stringify(result);\n\n  } catch (error) {\n    console.error('数据格式化时出错:', error);\n    console.error('错误堆栈:', error.stack);\n\n    // 返回错误格式的数据\n    return JSON.stringify([{\n      date: new Date().toISOString().split('T')[0],\n      time: formatTime(new Date()),\n      missionId: missionId ? missionId.toString() : 'unknown',\n      status: 1,\n      message: `数据格式化失败: ${error.message}`,\n      data: [],\n      error: {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n      },\n    }]);\n  }\n}\n\n/**\n * 将单个日期的数据对象转换为标准格式\n * @param {Object} rawData - 原始数据对象，包含形如 \"YYYY-MM-dd_酒店名称\" 的属性\n * @param {string} missionId - 任务ID\n * @returns {Object} 转换后的单个数据对象\n */\nfunction formatSingleDateData(rawData, missionId) {\n  // 正则表达式匹配 \"YYYY-MM-dd_酒店名称\" 格式\n  const dateKeyPattern = /^(\\d{4}-\\d{2}-\\d{2})_(.+)$/;\n\n  let targetDate = null;\n  let originalData = null;\n\n  // 查找日期格式的键\n  for (const key of Object.keys(rawData)) {\n    const match = key.match(dateKeyPattern);\n    if (match) {\n      targetDate = match[1]; // 提取日期\n      originalData = rawData[key].data || []; // 保持原有的data结构\n      break; // 只处理第一个匹配的键\n    }\n  }\n\n  // 如果没有找到日期，使用当前日期\n  if (!targetDate) {\n    targetDate = new Date().toISOString().split('T')[0];\n  }\n\n  // 确定状态\n  const status = (originalData && originalData.length > 0) ? 0 : 1;\n  const message = status === 0 ? '采集成功' : '未采集到有效数据';\n\n  return JSON.stringify({\n    date: targetDate,\n    time: formatTime(new Date()),\n    missionId: missionId.toString(),\n    status,\n    message,\n    data: originalData || [],\n  });\n}\n\n/**\n * 格式化时间为 HH:mm:ss 格式\n * @param {Date} date - 日期对象\n * @returns {string} 格式化后的时间字符串\n */\nfunction formatTime(date) {\n  try {\n    return date.toTimeString().split(' ')[0]; // 获取 HH:mm:ss 部分\n  } catch (error) {\n    console.error('时间格式化出错:', error);\n    return '00:00:00';\n  }\n}\n\nconst allVariables = automaRefData('variables')\nconst currentMissionId = allVariables.missionId\nconst currentTaskTime = allVariables.taskTime\nconst formatRes = formatDataByDate(allVariables, currentMissionId)\n\ntry {\n  fetch(\n    'http://localhost:3000/automa-complete', \n    { \n        method: 'POST', \n        headers: { 'Content-Type': 'application/json' }, \n        body: JSON.stringify({ \n            data: formatRes,\n            date: \"YYYY-MM-DD\", // 采集日期\n            time: currentTaskTime, // 采集时间\n            missionId: currentMissionId, // 任务ID（要根据任务 ID 查到任务名称）\n            status: 0, // 0-采集成功；1-采集异常\n            message: \"采集成功\",\n        }) \n    })\n    .then(response => {\n        console.log('采集请求成功:', response.status);\n        return response.json();\n    })\n    .then(data => {\n        console.log('📨 采集服务器响应:', data);\n    })\n    .catch(error => {\n        console.error('❌ 采集请求失败:', error);\n    });\n} catch(error) {\n  console.error('❌ 采集请求失败:', error);\n}\n\n", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false, "onError": {"retry": true, "enable": true, "retryTimes": 3, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_sendMessage", "type": "variable", "value": "{{variables.missionId}}"}]}, "settings": {"blockTimeout": 5000, "debugMode": false}}, "label": "javascript-code"}, {"id": "5sibwws", "type": "BlockBasic", "initialized": false, "position": {"x": 713.0588722339771, "y": 212.0368468948363}, "data": {"disableBlock": false, "loopId": "8hEMl2", "maxLoop": "0", "toNumber": 10, "fromNumber": 1, "startIndex": 0, "loopData": "[\n    {\n        \"name\": \"喆啡酒店\",\n        \"date\": \"2025-07-18\",\n        \"url\": \"https://hotels.ctrip.com/hotels/69428752.html?cityid=17&checkIn=2025-07-18&checkOut=2025-07-19&crn=1&adult=2&children=0\"\n    },\n    {\n        \"name\": \"北岸商银酒店\",\n        \"date\": \"2025-07-19\",\n        \"url\": \"https://hotels.ctrip.com/hotels/detail/?cityId=1&checkIn=2025-07-19&checkOut=2025-07-20&hotelId=2920983&adult=1&crn=1&children=0&highprice=-1&lowprice=0&listfilter=1\"\n    },\n    {\n        \"name\": \"还有不知道什么酒店\",\n        \"date\": \"2025-07-24\",\n        \"url\": \"https://hotels.ctrip.com/hotels/detail/?cityId=1&checkIn=2025-07-19&checkOut=2025-07-20&hotelId=122154965&adult=1&crn=1&children=0&highprice=-1&lowprice=0&listfilter=1\"\n    }\n]", "description": "hotelConfig", "variableName": "toLoopData", "referenceKey": "", "reverseLoop": false, "elementSelector": "", "waitForSelector": false, "waitSelectorTimeout": 5000, "resumeLastWorkflow": false, "loopThrough": "custom-data", "onError": {"dataToInsert": [], "enable": true, "errorMessage": "", "insertData": true, "retry": false, "retryInterval": 2, "retryTimes": 1, "toDo": "continue"}, "settings": {"blockTimeout": 0, "debugMode": false}}, "label": "loop-data"}, {"id": "0m8kd56", "type": "BlockLoopBreakpoint", "initialized": false, "position": {"x": 1909.7609945658855, "y": 668.8333333333333}, "data": {"clearLoop": false, "disableBlock": false, "loopId": "8hEMl2", "onError": {"dataToInsert": [], "enable": false, "errorMessage": "", "insertData": false, "retry": false, "retryInterval": 2, "retryTimes": 1, "toDo": "error"}, "settings": {"blockTimeout": 3600000, "debugMode": false}}, "label": "loop-breakpoint"}, {"id": "ega25p0", "type": "BlockBasic", "initialized": false, "position": {"x": 2228.5747597228437, "y": 707.9632253774275}, "data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://www.ctrip.com", "userAgent": "", "waitTabLoaded": false, "settings": {"blockTimeout": 0, "debugMode": false}, "onError": {"retry": true, "enable": true, "retryTimes": 5, "retryInterval": 2, "toDo": "continue", "errorMessage": "数据发送前Tab打开失败", "insertData": true, "dataToInsert": [{"type": "variable", "name": "WorkFlowError_FinalTabOpen", "value": "{{variables.missionId}}"}]}}, "label": "new-tab"}, {"id": "krhygwi", "type": "BlockBasic", "initialized": false, "position": {"x": 2855.5537616053657, "y": 717.7397083967529}, "data": {"activeTab": true, "allWindows": false, "closeType": "tab", "description": "", "disableBlock": false, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "Tab提前关闭", "insertData": true, "dataToInsert": [{"type": "variable", "name": "WorkFlowError_FinalTabCloase", "value": "{{variables.missionId}}"}]}, "settings": {"blockTimeout": 0, "debugMode": false}, "url": ""}, "label": "close-tab"}, {"id": "o1325xd", "type": "BlockBasic", "initialized": false, "position": {"x": 747.062872104987, "y": 704.0676153070865}, "data": {"code": "// CSS类名常量定义\nconst CSS_SELECTORS = {\n  // 酒店信息\n  HOTEL_NAME: 'h1.detail-headline_name',\n  HOTEL_ADDRESS: 'span.detail-headline_position_text',\n  HOTEL_HEAD_PIC: '[class*=\"detail-headalbum_bigpicImg\"]',\n  HOTEL_START_PRICE: '[class*=\"detail-head-price_price ctripPrice\"]',\n\n  // 房间卡片\n  ROOM_CARD_SHOWING: '[class*=\"commonRoomCard__\"]',\n  ROOM_CARD_HIDDEN: '[class*=\"commonRoomCardHidden__\"]',\n  ROOM_CARD_TITLE: '[class*=\"commonRoomCard-title__\"]',\n\n  // 房间基本信息\n  ROOM_BEDS_INFO: '[class*=\"baseRoom-bedsInfo_title__\"]',\n  ROOM_FACILITY_TITLE: '[class*=\"baseRoom-facility_title__\"]',\n\n  // 预订信息\n  RESERVATION_NOTES_INFO: '[class*=\"saleRoomItemBox-reservationNotesInfo_basicSaleRoomBox__\"]',\n  PRICE_AND_PROMOTION: '[class*=\"saleRoomItemBox-priceAndPromotionForC__\"]',\n};\n\n// 文本匹配关键词常量\nconst TEXT_KEYWORDS = {\n  SMOKING: '烟',\n  AREA: '平方米',\n  WIFI: 'Wi-Fi',\n\n  // 内容分类关键词\n  BREAKFAST: '早餐',\n  CANCEL_RULE: '取消',\n  CONFIRM_RULE: '确认',\n  PAY_WAY: '付',\n  PRICE: '¥',\n};\n\nfunction parseGroupedSaleRoomNodes() {\n  // 查找房间卡片节点\n  const showingRoomCardNodes = document.querySelectorAll(CSS_SELECTORS.ROOM_CARD_SHOWING);\n  const hiddenRoomCardNodes = document.querySelectorAll(CSS_SELECTORS.ROOM_CARD_HIDDEN);\n  const roomCardNodes = [...showingRoomCardNodes, ...hiddenRoomCardNodes];\n\n  // 获取酒店基本信息（使用兜底策略）\n  const hotelName = safeGetTextContent(document.querySelector(CSS_SELECTORS.HOTEL_NAME));\n  const checkInDate = getCurrentCheckInDate(); // 获取当前入住日期\n  const hotelHeadPic = safeGetTextContent(document.querySelector(CSS_SELECTORS.HOTEL_HEAD_PIC));\n  const hotelStartPrice = safeGetTextContent(document.querySelector(CSS_SELECTORS.HOTEL_START_PRICE));\n\n  const data = [];\n\n  if (roomCardNodes.length === 0) return { data: [] };\n\n  roomCardNodes.forEach((roomCard, roomIndex) => {\n    // 获取房型名称（使用兜底策略）\n    const roomName = safeGetTextContent(roomCard.querySelector(CSS_SELECTORS.ROOM_CARD_TITLE));\n\n    // 提取房间基本信息\n    const roomBasicInfo = extractRoomBasicInfo(roomCard);\n\n    // 处理预订节点\n    const roomDetails = roomCard.querySelectorAll(CSS_SELECTORS.RESERVATION_NOTES_INFO);\n\n    roomDetails.forEach((reservationNode, roomDetailIndex) => {\n      // 收集所有文本内容\n      const allTexts = [];\n\n      // 获取DIV叶子节点文本\n      getLeafNodes(reservationNode, 'div').forEach((div) => {\n        const text = safeGetTextContent(div);\n        if (text && isVisible(div)) {\n          allTexts.push(text);\n        }\n      });\n\n      // 查找对应的价格信息\n      let currentElement = reservationNode;\n      while (currentElement && currentElement !== roomCard) {\n        const parent = currentElement.parentElement;\n        if (parent) {\n          const priceNode = parent.querySelector(CSS_SELECTORS.PRICE_AND_PROMOTION);\n          if (priceNode) {\n            getLeafNodes(priceNode, 'span').forEach((span) => {\n              const text = safeGetTextContent(span);\n              if (text && isVisible(span)) {\n                allTexts.push(text);\n              }\n            });\n            break;\n          }\n        }\n        currentElement = parent;\n      }\n\n      // 分类文本内容（使用兜底策略）\n      let breakfast = null;\n      let cancelRule = null;\n      let confirmRule = null;\n      let payWay = null;\n      let salePrice = null;\n      const promotionTexts = [];\n\n      allTexts.forEach((text) => {\n        if (text.includes(TEXT_KEYWORDS.BREAKFAST)) {\n          breakfast = text;\n        } else if (text.includes(TEXT_KEYWORDS.CANCEL_RULE)) {\n          cancelRule = text;\n        } else if (text.includes(TEXT_KEYWORDS.CONFIRM_RULE)) {\n          confirmRule = text;\n        } else if (text.includes(TEXT_KEYWORDS.PAY_WAY)) {\n          payWay = text;\n        } else if (text.includes(TEXT_KEYWORDS.PRICE)) {\n          salePrice = text;\n        } else if (text && !isCommonWord(text)) {\n          // 其他内容作为促销信息\n          promotionTexts.push(text);\n        }\n      });\n\n      // 创建单个房间详情数据项（使用兜底策略）\n      const roomDetailData = {\n        checkInDate,\n        hotelName,\n        hotelHeadPic,\n        roomName,\n        salePrice,\n        lowestPrice: hotelStartPrice,\n        breakfast,\n        cancelRule,\n        confirmRule,\n        payWay,\n        promotion: promotionTexts.length > 0 ? promotionTexts : null,\n        roomBasicInfo: roomBasicInfo.length > 0 ? roomBasicInfo : null,\n      };\n\n      data.push(roomDetailData);\n    });\n  });\n\n  return { data };\n}\n\n// 辅助函数\nfunction safeGetTextContent(element) {\n  try {\n    if (!element) return null;\n    const text = element.textContent?.trim();\n    return text && text.length > 0 ? text : null;\n  } catch (error) {\n    console.error('获取元素文本内容时出错:', error);\n    return null;\n  }\n}\n\nfunction getCurrentCheckInDate() {\n  // 从URL参数或页面元素中获取入住日期\n  try {\n    const urlParams = new URLSearchParams(window.location.search);\n    const checkIn = urlParams.get('checkIn');\n    if (checkIn) {\n      return checkIn;\n    }\n    return new Date().toISOString().split('T')[0];\n  } catch (error) {\n    console.error('获取入住日期时出错:', error);\n    return new Date().toISOString().split('T')[0];\n  }\n}\n\nfunction extractRoomBasicInfo(roomCard) {\n  try {\n    const basicInfoArray = [];\n\n    // 提取床型信息（使用兜底策略）\n    const bedsInfo = safeGetTextContent(roomCard.querySelector(CSS_SELECTORS.ROOM_BEDS_INFO));\n    if (bedsInfo) {\n      basicInfoArray.push(bedsInfo);\n    }\n\n    // 提取设施信息（使用兜底策略）\n    const facilityNodes = roomCard.querySelectorAll(CSS_SELECTORS.ROOM_FACILITY_TITLE);\n    facilityNodes.forEach((node) => {\n      const text = safeGetTextContent(node);\n      if (text) {\n        basicInfoArray.push(text);\n      }\n    });\n\n    return basicInfoArray.length > 0 ? basicInfoArray : null;\n  } catch (error) {\n    console.error('提取房间基本信息时出错:', error);\n    return null;\n  }\n}\n\nfunction isCommonWord(text) {\n  const commonWords = [\n    '查看详情', '立即预订', '更多信息', '房型详情',\n    '预订', '查看', '详情', '更多', '信息',\n    '点击', '选择', '确定', '取消', '关闭',\n  ];\n  return commonWords.some(word => text.includes(word)) || text.length < 2;\n}\n\nfunction getLeafNodes(element, tagName) {\n  const leafNodes = [];\n  function traverse(el) {\n    const children = Array.from(el.children);\n    if (children.length === 0 && el.tagName.toLowerCase() === tagName) {\n      leafNodes.push(el);\n    } else {\n      children.forEach(traverse);\n    }\n  }\n  traverse(element);\n  return leafNodes;\n}\n\nfunction isVisible(element) {\n  const style = window.getComputedStyle(element);\n  return style.display !== 'none' &&\n         style.visibility !== 'hidden' &&\n         parseFloat(style.opacity) > 0;\n}\n\n// 执行解析\nconst result = parseGroupedSaleRoomNodes();\n\nconst currentLoopData = automaRefData('loopData', '8hEMl2');\n\nconst currentLoopDataKey = currentLoopData.data.date + '_' + currentLoopData.data.name\n\nautomaSetVariable(currentLoopDataKey, result)\n\nautomaNextBlock();\n", "context": "website", "description": "CSS解析房型", "disableBlock": false, "everyNewTab": false, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_DomAnalysis", "type": "variable", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 2000, "debugMode": false}, "timeout": 20000}, "label": "javascript-code"}, {"id": "zuaq870", "type": "BlockBasic", "initialized": false, "position": {"x": 1590.2111613312372, "y": 709.3956100703408}, "data": {"activeTab": true, "allWindows": false, "closeType": "tab", "description": "", "disableBlock": false, "url": "", "settings": {"blockTimeout": 0, "debugMode": false}, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"type": "variable", "name": "WorkFlowError_DetailTabClose", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}}, "label": "close-tab"}, {"id": "vgxdhoe", "type": "BlockDelay", "initialized": false, "position": {"x": 2708.7285209072106, "y": 245.09571912881347}, "data": {"disableBlock": false, "time": "2000"}, "label": "delay"}, {"id": "7rh9sol", "type": "BlockBasic", "initialized": false, "position": {"x": 2563.6838884162426, "y": 917.1107860879214}, "data": {"disableBlock": false, "description": "发送未登录数据", "timeout": 2000, "context": "background", "code": "const currentTaskTime = automaRefData('variables', 'taskTime')\nconst currentMissionId = automaRefData('variables', 'missionId')\ntry {\n  fetch(\n    'http://localhost:3000/automa-complete', \n    { \n        method: 'POST', \n        headers: { 'Content-Type': 'application/json' }, \n        body: JSON.stringify({ \n            data: null,\n            date: \"YYYY-MM-DD\", // 采集日期\n            time: currentTaskTime, // 采集时间\n            missionId: currentMissionId, // 任务ID（要根据任务 ID 查到任务名称）\n            status: 1, // 0-采集成功；1-采集异常\n            message: \"用户未登录，采集失败\",\n        }) \n    })\n    .then(response => {\n        console.log('采集请求成功:', response.status);\n        return response.json();\n    })\n    .then(data => {\n        console.log('📨 采集服务器响应:', data);\n    })\n    .catch(error => {\n        console.error('❌ 采集请求失败:', error);\n    });\n} catch(error) {\n  console.error('❌ 采集请求失败:', error);\n}\n\nalert('用户未登录，工作流结束，上报错误')\nautomaNextBlock()", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false, "onError": {"retry": false, "enable": true, "retryTimes": 2, "retryInterval": 1, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_sendUnlogin", "type": "variable", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}, "settings": {"blockTimeout": 3000, "debugMode": false}}, "label": "javascript-code"}, {"id": "3px784x", "type": "BlockConditions", "initialized": false, "position": {"x": 381.17937010572314, "y": 621.1403907903023}, "data": {"description": "登录判断", "disableBlock": false, "conditions": [{"conditions": [{"id": "svMnmlAXcP1kBBGaOFyP4", "conditions": [{"id": "69CPJZEt6n3outNu-LI3K", "items": [{"type": "code", "category": "value", "data": {"code": "const isUserLogin = automaRefData('variables', 'isUserLogin')\nreturn isUserLogin;", "context": "background"}, "id": "fxcW8hPy39b_pyUE8JRmg"}]}]}], "id": "Pk9Tx7M55QUwtwmmuHyBC", "name": "Path 1"}], "retryConditions": false, "retryCount": 10, "retryTimeout": 1000}, "label": "conditions"}, {"id": "p98zz0r", "type": "BlockLoopBreakpoint", "initialized": false, "position": {"x": 754.9744978855654, "y": 871.8594308157483}, "data": {"clearLoop": true, "disableBlock": false, "loopId": "8hEMl2"}, "label": "loop-breakpoint"}, {"id": "wfbdjis", "type": "BlockBasic", "initialized": false, "position": {"x": 2991.154280871187, "y": 269.88987330429615}, "data": {"disableBlock": false, "description": "获取登录状态", "timeout": 20000, "context": "website", "code": "function isLoginByCss() {\n  const isDocumentExist = document\n  if(!isDocumentExist) {\n    automaSetVariable('isUserLogin', false);\n    return false\n  }\n  const loginCssClass = '[class*=\"tl_nfes_home_header_login_title_\"]';\n  const isLoginDoms = document.querySelectorAll(loginCssClass) || [];\n  const isLogin = isLoginDoms.length > 0 && isLoginDoms[0].innerText !== '登录';\n  return isLogin;\n}\nautomaSetVariable('isUserLogin', isLoginByCss());\n\n\nautomaNextBlock()", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false, "onError": {"retry": false, "enable": true, "retryTimes": 3, "retryInterval": 2, "toDo": "continue", "errorMessage": "登录状态获取失败", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_LoginDomAnalysis", "type": "variable", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}, "settings": {"blockTimeout": 2000, "debugMode": false}}, "label": "javascript-code"}, {"id": "r4nynp3", "type": "BlockBasic", "initialized": false, "position": {"x": 1031.833928701313, "y": 705.5937127895014}, "data": {"disableBlock": false, "description": "滚动模拟扫描", "timeout": 20000, "context": "website", "code": "function smartHumanScroll() {\n  // 防止重复执行，避免内存泄漏\n  if (window.scrolling) {\n    return;\n  }\n  window.scrolling = true;\n\n  const currentPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n  // 兼容性更好的页面高度计算\n  const pageHeight = Math.max ? Math.max(\n    document?.body?.scrollHeight || 0,\n    document?.body?.offsetHeight || 0,\n    document?.documentElement?.clientHeight || 0,\n    document?.documentElement?.scrollHeight || 0,\n    document?.documentElement?.offsetHeight || 0,\n  ) : (document?.documentElement?.scrollHeight || document?.body?.scrollHeight || 0);\n  const viewportHeight = window?.innerHeight || document?.documentElement?.clientHeight || document?.body?.clientHeight || 600;\n  const maxScrollPosition = pageHeight - viewportHeight;\n\n  // 存储定时器ID，便于清理\n  const timers = [];\n\n  // 随机选择目标位置（在页面范围内）\n  const scrollRange = Math.random();\n  let targetPosition;\n\n  if (scrollRange < 0.3) {\n    // 30% 概率滚动到页面顶部区域 (0-20%)\n    targetPosition = Math.random() * (maxScrollPosition * 0.2);\n  } else if (scrollRange < 0.6) {\n    // 30% 概率滚动到页面中部区域 (30-70%)\n    targetPosition = maxScrollPosition * 0.3 + Math.random() * (maxScrollPosition * 0.4);\n  } else if (scrollRange < 0.9) {\n    // 30% 概率滚动到页面底部区域 (70-100%)\n    targetPosition = maxScrollPosition * 0.7 + Math.random() * (maxScrollPosition * 0.3);\n  } else {\n    // 10% 概率完全随机位置\n    targetPosition = Math.random() * maxScrollPosition;\n  }\n\n  const distance = Math.abs(targetPosition - currentPosition);\n\n  // 随机化滚动次数（2-3次）\n  const randomFactor = Math.random();\n  let scrollSteps;\n\n  if (randomFactor < 0.6) {\n    scrollSteps = 2; // 60% 概率分2次滚动\n  } else {\n    scrollSteps = 3; // 40% 概率分3次滚动\n  }\n\n  // 生成滚动步骤，确保每次滚动在200px-500px之间\n  const steps = [];\n  const minStepDistance = Math.max(300, distance * 0.2); // 最小步骤距离为200px或总距离的20%，取较大值\n  const maxStepDistance = 500; // 最大步骤距离为500px\n\n  // 如果单次滚动距离会超过最大限制，增加滚动次数\n  if (distance / scrollSteps > maxStepDistance) {\n    scrollSteps = Math.ceil ? Math.ceil(distance / maxStepDistance) : Math.floor(distance / maxStepDistance) + 1;\n  }\n\n  // 检查总距离是否足够支持最小滚动要求\n  if (distance < minStepDistance * scrollSteps) {\n    // 如果总距离不够，减少滚动次数\n    scrollSteps = Math.max(1, Math.floor(distance / minStepDistance));\n  }\n\n  for (let i = 1; i < scrollSteps; i++) {\n    // 基础进度，确保每步至少满足最小距离要求\n    const minProgressForStep = (minStepDistance * i) / distance;\n    const baseProgress = Math.max(minProgressForStep, i / scrollSteps);\n\n    // 添加随机偏移，但确保不违反最小距离规则\n    const maxOffset = Math.min(0.15, (1 - baseProgress) / 2);\n    const randomOffset = (Math.random() - 0.5) * maxOffset * 2;\n    let progress = baseProgress + randomOffset;\n\n    // 确保进度在合理范围内，且满足最小距离要求\n    progress = Math.max(minProgressForStep, Math.min(0.95, progress));\n\n    // 如果是最后一步之前，确保留足够空间给最后的滚动（至少300px）\n    if (i === scrollSteps - 1) {\n      const maxProgressForLastStep = 1 - (minStepDistance / distance);\n      progress = Math.min(progress, maxProgressForLastStep);\n    }\n\n    // 计算滚动位置\n    const stepPosition = currentPosition + distance * progress * (targetPosition > currentPosition ? 1 : -1);\n\n    // 验证步骤距离是否满足最小要求\n    const lastPosition = steps.length > 0 ? steps[steps.length - 1] : currentPosition;\n    const stepDistance = Math.abs(stepPosition - lastPosition);\n\n    if (stepDistance >= minStepDistance || i === scrollSteps - 1) {\n      steps.push(stepPosition);\n    } else {\n      // 如果距离不够，调整到最小距离\n      const adjustedPosition = lastPosition + minStepDistance * (targetPosition > currentPosition ? 1 : -1);\n      steps.push(adjustedPosition);\n    }\n  }\n\n  // 清理定时器的函数\n  function clearAllTimers() {\n    timers.forEach((timerId) => {\n      if (timerId) {\n        clearTimeout(timerId);\n      }\n    });\n    timers.length = 0;\n    window.scrolling = false;\n  }\n\n  // 执行分段滚动\n  function executeStep(stepIndex) {\n    if (stepIndex >= steps.length) {\n      // 最后滚动到精确位置，添加随机延迟\n      const finalDelay = 300 + Math.random() * 200; // 增加200ms基础延迟\n      const finalTimer = setTimeout(() => {\n        // 滚动到目标位置（带兼容性检查）\n        if (window.scrollTo && typeof window.scrollTo === 'function') {\n          try {\n            window.scrollTo({\n              top: targetPosition,\n              behavior: 'smooth',\n            });\n          } catch (e) {\n            // 兜底方案：使用传统的scrollTo\n            window.scrollTo(0, targetPosition);\n          }\n        } else {\n          // 兜底方案：使用document.documentElement.scrollTop\n          document.documentElement.scrollTop = targetPosition;\n          document.body.scrollTop = targetPosition;\n        }\n\n        // 滚动完成，清理状态\n        setTimeout(() => {\n          clearAllTimers();\n        }, 500); // 等待滚动动画完成\n      }, finalDelay);\n      timers.push(finalTimer);\n      return;\n    }\n\n    // 使用平滑滚动（带兼容性检查）\n    if (window.scrollTo && typeof window.scrollTo === 'function') {\n      try {\n        window.scrollTo({\n          top: steps[stepIndex],\n          behavior: 'smooth',\n        });\n      } catch (e) {\n        // 兜底方案：使用传统的scrollTo\n        window.scrollTo(0, steps[stepIndex]);\n      }\n    } else {\n      // 兜底方案：使用document.documentElement.scrollTop\n      document.documentElement.scrollTop = steps[stepIndex];\n      document.body.scrollTop = steps[stepIndex];\n    }\n\n    // 计算合理的延迟时间，控制总时间在2-3秒，每次滚动至少200ms\n    const totalBudget = 2000; // 总时间预算2000ms\n    const leastPauseTime = 800;\n    const avgDelay = totalBudget / scrollSteps;\n\n    // 根据步骤位置分配时间，确保每次至少200ms\n    let baseDelay;\n    if (stepIndex === 0) {\n      baseDelay = Math.max(leastPauseTime, avgDelay * 0.8 + Math.random() * (avgDelay * 0.4)); // 第一次，至少800ms\n    } else if (stepIndex === steps.length - 1) {\n      baseDelay = Math.max(leastPauseTime, avgDelay * 0.7 + Math.random() * (avgDelay * 0.3)); // 最后一次，至少800ms\n    } else {\n      baseDelay = Math.max(leastPauseTime, avgDelay * 0.75 + Math.random() * (avgDelay * 0.5)); // 中间，至少800ms\n    }\n\n    // 偶尔有短暂停顿（25%概率）\n    if (Math.random() < 0.25) {\n      baseDelay += 400;\n    }\n\n    // 偶尔在滚动后稍微向上滚动一点（15%概率）\n    if (Math.random() < 0.15 && stepIndex < steps.length - 1) {\n      const backScrollTimer = setTimeout(() => {\n        const currentPos = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n        const backScrollDistance = 250 + Math.random() * 100; // 向上滚动250-350px\n        const backScrollPosition = Math.max(0, currentPos - backScrollDistance);\n\n        // 向上滚动\n        if (window.scrollTo && typeof window.scrollTo === 'function') {\n          try {\n            window.scrollTo({\n              top: backScrollPosition,\n              behavior: 'smooth',\n            });\n          } catch (e) {\n            window.scrollTo(0, backScrollPosition);\n          }\n        } else {\n          document.documentElement.scrollTop = backScrollPosition;\n          document.body.scrollTop = backScrollPosition;\n        }\n\n        // 向上滚动后，稍作停顿再继续\n        const backScrollDelay = leastPauseTime;\n        const continueTimer = setTimeout(() => {\n          executeStep(stepIndex + 1);\n        }, backScrollDelay);\n        timers.push(continueTimer);\n      }, baseDelay * 0.7); // 在原延迟的70%时执行向上滚动\n      timers.push(backScrollTimer);\n    } else {\n      const nextStepTimer = setTimeout(() => {\n        executeStep(stepIndex + 1);\n      }, baseDelay);\n      timers.push(nextStepTimer);\n    }\n  }\n\n  // 添加初始随机延迟\n  const initialDelay = Math.random() * 300;\n  const initialTimer = setTimeout(() => {\n    executeStep(0);\n  }, initialDelay);\n  timers.push(initialTimer);\n}\n\n// 使用\nsmartHumanScroll();\n\n\n\nautomaNextBlock()", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"name": "WorkFlowError_Scroll", "type": "variable", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}, "settings": {"blockTimeout": 10000, "debugMode": false}}, "label": "javascript-code"}, {"id": "k7hs07x", "type": "BlockBasic", "initialized": false, "position": {"x": 396.2647180584943, "y": 207.90448786558915}, "data": {"disableBlock": false, "description": "mock当前missionid", "dataList": [{"action": "default", "filePath": "", "isFile": false, "name": "missionId", "type": "variable", "value": "mockMissionId"}, {"action": "default", "filePath": "", "isFile": false, "name": "taskTime", "type": "variable", "value": "mockTaskExecuteTime"}]}, "label": "insert-data"}, {"id": "sw2ri69", "type": "BlockDelay", "initialized": false, "position": {"x": 1302.6423552881915, "y": 685.5937127895013}, "data": {"disableBlock": false, "time": "2000"}, "label": "delay"}, {"id": "umlky27", "type": "BlockBasic", "initialized": false, "position": {"x": 1325.9338204853766, "y": 136.09763567579446}, "data": {"disableBlock": false, "description": "搜索模拟", "url": "https://www.ctrip.com/", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false, "settings": {"blockTimeout": 3000, "debugMode": false}, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"type": "variable", "name": "WorkFlowError_SearchError", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}}, "label": "new-tab"}, {"id": "p670ajs", "type": "BlockConditions", "initialized": false, "position": {"x": 1004, "y": 156.25}, "data": {"description": "随机模拟搜索", "disableBlock": false, "conditions": [{"id": "SkJaHYCZ7kEhEbYI7rGrt", "name": "Path 1", "conditions": [{"id": "bp4bmxa2LXijWWx0Ep62l", "conditions": [{"id": "JmCtqul3NQ43yoaH47uX5", "items": [{"type": "code", "category": "value", "data": {"code": "return Math.random() < 0.4;", "context": "background"}, "id": "HlpyPV_Fbd7qUF5QFZFQ-"}]}]}]}], "retryConditions": false, "retryCount": 10, "retryTimeout": 1000}, "label": "conditions"}, {"id": "xvhonmm", "type": "BlockBasic", "initialized": false, "position": {"x": 1593.375285321028, "y": 138.04600568485077}, "data": {"disableBlock": false, "description": "模拟搜索输入", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": "input#_allSearchKeyword", "markEl": false, "multiple": false, "selected": true, "clearValue": true, "getValue": false, "saveData": false, "dataColumn": "", "selectOptionBy": "value", "optionPosition": "1", "assignVariable": false, "variableName": "", "type": "text-field", "value": "{{loopData.8hEMl2.name}}", "delay": 0, "events": [], "settings": {"blockTimeout": 3000, "debugMode": false}, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": false, "dataToInsert": []}}, "label": "forms"}, {"id": "8lcqy5l", "type": "BlockBasic", "initialized": false, "position": {"x": 2152.2277026288434, "y": 139.99437569390705}, "data": {"disableBlock": false, "url": "", "description": "", "activeTab": true, "closeType": "tab", "allWindows": false, "settings": {"blockTimeout": 2000, "debugMode": false}, "onError": {"retry": false, "enable": true, "retryTimes": 1, "retryInterval": 2, "toDo": "continue", "errorMessage": "", "insertData": true, "dataToInsert": [{"type": "variable", "name": "WorkFlowError_SearchError", "value": "{{variables.missionId}}_{{<EMAIL>}}_{{<EMAIL>}}"}]}}, "label": "close-tab"}, {"id": "15h8xak", "type": "BlockDelay", "initialized": false, "position": {"x": 1870.3229811908263, "y": 115.31803102399152}, "data": {"disableBlock": false, "time": "1000", "settings": {"blockTimeout": 0, "debugMode": false}}, "label": "delay"}], "edges": [{"id": "vueflow__edge-ega25p0ega25p0-output-1-mkkjt2umkkjt2u-input-1", "type": "custom", "source": "ega25p0", "target": "mkkjt2u", "sourceHandle": "ega25p0-output-1", "targetHandle": "mkkjt2u-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2444.564872196838, "sourceY": 743.9515833799126, "targetX": 2542.403830025331, "targetY": 746.0665375784279}, {"id": "vueflow__edge-mkkjt2umkkjt2u-output-1-krhygwikrhygwi-input-1", "type": "custom", "source": "mkkjt2u", "target": "krhygwi", "sourceHandle": "mkkjt2u-output-1", "targetHandle": "krhygwi-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2782.388123757994, "sourceY": 746.0665375784279, "targetX": 2831.559580346697, "targetY": 753.728066399238, "class": "connected-edges"}, {"id": "vueflow__edge-dXoy_a-flGA-XdFumMEsQdXoy_a-flGA-XdFumMEsQ-output-1-5sibwws5sibwws-input-1", "type": "custom", "source": "dXoy_a-flGA-XdFumMEsQ", "target": "k7hs07x", "sourceHandle": "dXoy_a-flGA-XdFumMEsQ-output-1", "targetHandle": "k7hs07x-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 601.2554702638965, "sourceY": 37.27483091561052, "targetX": 372.2705367998258, "targetY": 243.8927894324234}, {"id": "vueflow__edge-zuaq870zuaq870-output-1-0m8kd560m8kd56-input-1", "type": "custom", "source": "zuaq870", "target": "0m8kd56", "sourceHandle": "zuaq870-output-1", "targetHandle": "0m8kd56-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1806.2012738052317, "sourceY": 745.3839680728258, "targetX": 1885.766813307217, "targetY": 744.4017062528894}, {"id": "vueflow__edge-0m8kd560m8kd56-output-1-ega25p0ega25p0-input-1", "type": "custom", "source": "0m8kd56", "target": "ega25p0", "sourceHandle": "0m8kd56-output-1", "targetHandle": "ega25p0-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2125.75110703988, "sourceY": 744.4017062528894, "targetX": 2204.5805784641752, "targetY": 743.9515833799126}, {"id": "vueflow__edge-3px784x3px784x-output-fallback-p98zz0rp98zz0r-input-1", "type": "custom", "source": "3px784x", "target": "p98zz0r", "sourceHandle": "3px784x-output-fallback", "targetHandle": "p98zz0r-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 661.173492671315, "sourceY": 780.2831337074024, "targetX": 730.9803166268969, "targetY": 947.4279166066059}, {"id": "vueflow__edge-wfbdjiswfbdjis-output-1-3px784x3px784x-input-1", "type": "custom", "source": "wfbdjis", "target": "3px784x", "sourceHandle": "wfbdjis-output-1", "targetHandle": "3px784x-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 3207.14507057299, "sourceY": 305.8781748711304, "targetX": 357.1853017183561, "targetY": 713.7107935873435}, {"id": "vueflow__edge-vgxdhoevgxdhoe-output-1-wfbdjiswfbdjis-input-1", "type": "custom", "source": "vgxdhoe", "target": "wfbdjis", "sourceHandle": "vgxdhoe-output-1", "targetHandle": "wfbdjis-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2924.718633381205, "sourceY": 303.6719101919347, "targetX": 2967.1605510977242, "targetY": 305.8781748711304}, {"id": "vueflow__edge-r4nynp3r4nynp3-output-1-sw2ri69sw2ri69-input-1", "type": "custom", "source": "r4nynp3", "target": "sw2ri69", "sourceHandle": "r4nynp3-output-1", "targetHandle": "sw2ri69-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1247.8242669179103, "sourceY": 741.581957920685, "targetX": 1278.648174029523, "targetY": 744.1699320704479}, {"id": "vueflow__edge-3px784x3px784x-output-Pk9Tx7M55QUwtwmmuHyBC-o1325xdo1325xd-input-1", "type": "custom", "source": "3px784x", "target": "o1325xd", "sourceHandle": "3px784x-output-Pk9Tx7M55QUwtwmmuHyBC", "targetHandle": "o1325xd-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 662.179514580989, "sourceY": 738.2908897717516, "targetX": 723.0686908463185, "targetY": 740.0558604382701, "class": "connected-edges"}, {"id": "vueflow__edge-o1325xdo1325xd-output-1-r4nynp3r4nynp3-input-1", "type": "custom", "source": "o1325xd", "target": "r4nynp3", "sourceHandle": "o1325xd-output-1", "targetHandle": "r4nynp3-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 963.0532103215844, "sourceY": 740.0558604382701, "targetX": 1007.8397474426445, "targetY": 741.581957920685, "class": "connected-edges"}, {"id": "vueflow__edge-sw2ri69sw2ri69-output-1-zuaq870zuaq870-input-1", "type": "custom", "source": "sw2ri69", "target": "zuaq870", "sourceHandle": "sw2ri69-output-1", "targetHandle": "zuaq870-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1518.632693504789, "sourceY": 744.1699320704479, "targetX": 1566.2167543299659, "targetY": 745.3839680728258}, {"id": "vueflow__edge-k7hs07xk7hs07x-output-1-5sibwws5sibwws-input-1", "type": "custom", "source": "k7hs07x", "target": "5sibwws", "sourceHandle": "k7hs07x-output-1", "targetHandle": "5sibwws-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 612.2550562750916, "sourceY": 243.8927894324234, "targetX": 689.0646909753086, "targetY": 250.0173551497918}, {"id": "vueflow__edge-5sibwws5sibwws-output-1-p670ajsp670ajs-input-1", "type": "custom", "source": "5sibwws", "target": "p670ajs", "sourceHandle": "5sibwws-output-1", "targetHandle": "p670ajs-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 929.0492104505745, "sourceY": 250.0173551497918, "targetX": 980.0058187413315, "targetY": 248.82038868812847}, {"id": "vueflow__edge-p670ajsp670ajs-output-SkJaHYCZ7kEhEbYI7rGrt-umlky27umlky27-input-1", "type": "custom", "source": "p670ajs", "target": "umlky27", "sourceHandle": "p670ajs-output-SkJaHYCZ7kEhEbYI7rGrt", "targetHandle": "umlky27-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1285.0000316039645, "sourceY": 273.400428436886, "targetX": 1301.939639226708, "targetY": 172.08593724262872}, {"id": "vueflow__edge-umlky27umlky27-output-1-xvhonmmxvhonmm-input-1", "type": "custom", "source": "umlky27", "target": "xvhonmm", "sourceHandle": "umlky27-output-1", "targetHandle": "xvhonmm-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1541.924158701974, "sourceY": 172.08593724262872, "targetX": 1569.3813298049624, "targetY": 174.03427903385966}, {"id": "vueflow__edge-xvhonmmxvhonmm-output-1-15h8xak15h8xak-input-1", "type": "custom", "source": "xvhonmm", "target": "15h8xak", "sourceHandle": "xvhonmm-output-1", "targetHandle": "15h8xak-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1809.3658492802283, "sourceY": 174.03427903385966, "targetX": 1846.3290256747607, "targetY": 173.89423619602545}, {"id": "vueflow__edge-15h8xak15h8xak-output-1-8lcqy5l8lcqy5l-input-1", "type": "custom", "source": "15h8xak", "target": "8lcqy5l", "sourceHandle": "15h8xak-output-1", "targetHandle": "8lcqy5l-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2086.3135451500266, "sourceY": 173.89423619602545, "targetX": 2128.233521370175, "targetY": 175.9826772607413}, {"id": "vueflow__edge-8lcqy5l8lcqy5l-output-1-omDH_l2oRzHjbHsBzvA82omDH_l2oRzHjbHsBzvA82-input-1", "type": "custom", "source": "8lcqy5l", "target": "omDH_l2oRzHjbHsBzvA82", "sourceHandle": "8lcqy5l-output-1", "targetHandle": "omDH_l2oRzHjbHsBzvA82-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2368.2182665880437, "sourceY": 175.9826772607413, "targetX": 2389.3586381563887, "targetY": 312.0766851971757}, {"id": "vueflow__edge-omDH_l2oRzHjbHsBzvA82omDH_l2oRzHjbHsBzvA82-output-1-vgxdhoevgxdhoe-input-1", "type": "custom", "source": "omDH_l2oRzHjbHsBzvA82", "target": "vgxdhoe", "sourceHandle": "omDH_l2oRzHjbHsBzvA82-output-1", "targetHandle": "vgxdhoe-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 2629.3429318890517, "sourceY": 312.0766851971757, "targetX": 2684.734339648542, "targetY": 303.6719101919347}, {"id": "vueflow__edge-p670ajsp670ajs-output-fallback-omDH_l2oRzHjbHsBzvA82omDH_l2oRzHjbHsBzvA82-input-1", "type": "custom", "source": "p670ajs", "target": "omDH_l2oRzHjbHsBzvA82", "sourceHandle": "p670ajs-output-fallback", "targetHandle": "omDH_l2oRzHjbHsBzvA82-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1283.9943483081947, "sourceY": 315.3926441547113, "targetX": 2389.3586381563887, "targetY": 312.0766851971757}, {"id": "vueflow__edge-p98zz0rp98zz0r-output-1-7rh9sol7rh9sol-input-1", "type": "custom", "source": "p98zz0r", "target": "7rh9sol", "sourceHandle": "p98zz0r-output-1", "targetHandle": "7rh9sol-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 970.9647232308613, "sourceY": 947.4279166066059, "targetX": 2539.689707157574, "targetY": 953.099031219105}, {"id": "vueflow__edge-7rh9sol7rh9sol-output-1-krhygwikrhygwi-input-1", "type": "custom", "source": "7rh9sol", "target": "krhygwi", "sourceHandle": "7rh9sol-output-1", "targetHandle": "krhygwi-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "connected-edges", "sourceX": 2779.6742266328397, "sourceY": 953.099031219105, "targetX": 2831.559580346697, "targetY": 753.728066399238}], "position": [26, 209], "zoom": 0.5407502728190379, "viewport": {"x": 26, "y": 209, "zoom": 0.5407502728190379}}, "settings": {"publicId": "huanglongXcWorkflow", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": true, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {}}