import { useState, useEffect, useCallback } from 'react';
import { getNotification } from '../api/common';
import { NotificationItem } from '../types/notification';
import { getCurrentDate, getDateBeforeDays, formatTime } from '../utils/date';

interface Notification {
  id: number;
  message: string;
  time: string;
  read: boolean;
}

interface ReadNotificationRecord {
  date: string; // YYYY-MM-DD format
  id: number[];
}

const NOTIFICATION_READ_KEY = "notification_read_status";
const NOTIFICATION_STORAGE_EVENT = "notification_status_changed";

// 清理超过3天的记录
const cleanupExpiredRecords = (records: ReadNotificationRecord[]): ReadNotificationRecord[] => {
  const threeDaysAgo = getDateBeforeDays(3);
  return records.filter(record => record.date >= threeDaysAgo);
};

const getReadNotifications = (): number[] => {
  try {
    const stored = localStorage.getItem(NOTIFICATION_READ_KEY);
    if (!stored) return [];
    
    const records: ReadNotificationRecord[] = JSON.parse(stored);
    const cleanedRecords = cleanupExpiredRecords(records);
    
    // 如果清理后有变化，更新localStorage
    if (cleanedRecords.length !== records.length) {
      localStorage.setItem(NOTIFICATION_READ_KEY, JSON.stringify(cleanedRecords));
    }
    
    // 将所有日期的ID合并成一个数组
    return cleanedRecords.flatMap(record => record.id);
  } catch (error) {
    console.error("Failed to parse read notifications from localStorage:", error);
    return [];
  }
};

// 添加单个已读记录
const addReadNotification = (id: number): void => {
  try {
    const stored = localStorage.getItem(NOTIFICATION_READ_KEY);
    const existingRecords: ReadNotificationRecord[] = stored ? JSON.parse(stored) : [];
    
    // 清理过期记录
    const cleanedRecords = cleanupExpiredRecords(existingRecords);
    
    const currentDate = getCurrentDate();
    
    // 查找当前日期的记录
    const currentDateRecord = cleanedRecords.find(record => record.date === currentDate);
    
    if (currentDateRecord) {
      // 如果当前日期记录存在，检查ID是否已存在
      if (!currentDateRecord.id.includes(id)) {
        currentDateRecord.id.push(id);
      }
    } else {
      // 如果当前日期记录不存在，创建新记录
      cleanedRecords.push({ date: currentDate, id: [id] });
    }
    
    localStorage.setItem(NOTIFICATION_READ_KEY, JSON.stringify(cleanedRecords));
  } catch (error) {
    console.error("Failed to add read notification to localStorage:", error);
  }
};

// 将 NotificationItem[] 转换为 Notification[]
const convertNotificationItems = (items: NotificationItem[]): Notification[] => {
  const readIds = getReadNotifications();
  return items.map((item, index) => ({
    id: index + 1, // 使用索引作为ID，或者可以根据实际需求调整
    message: item.content,
    time: formatTime(item.updateTime),
    read: readIds.includes(index + 1),
  }));
};

export const useNotifications = () => {
  const [notificationList, setNotificationList] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取通知数据
  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    
    try {
      const startDate = getDateBeforeDays(2) + ' 00:00:00';
      const endDate = getCurrentDate() + ' 23:59:59';
      const response = await getNotification({ startDate, endDate });
      const notificationItems: NotificationItem[] = response || [];
      const convertedNotifications = convertNotificationItems(notificationItems);

      setNotificationList(convertedNotifications);
    } catch (err) {
      console.error("Failed to fetch notifications:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 监听localStorage变化事件，仅用于跨组件同步
  useEffect(() => {
    const handleStorageChange = () => {
      // 只重新计算已读状态，不重新请求数据
      setNotificationList(prev => 
        prev.map(notification => ({
          ...notification,
          read: getReadNotifications().includes(notification.id)
        }))
      );
    };

    // 监听自定义事件
    window.addEventListener(NOTIFICATION_STORAGE_EVENT, handleStorageChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener(NOTIFICATION_STORAGE_EVENT, handleStorageChange);
    };
  }, []);

  // 初始加载数据
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const markAsRead = useCallback((id: number) => {
    const readIds = getReadNotifications();
    if (!readIds.includes(id)) {
      addReadNotification(id);
      // 更新本地状态
      setNotificationList(prev => 
        prev.map(notification => 
          notification.id === id ? { ...notification, read: true } : notification
        )
      );
      // 触发跨组件同步事件
      window.dispatchEvent(new Event(NOTIFICATION_STORAGE_EVENT));
    }
  }, []);

  const unreadCount = notificationList.filter(n => !n.read).length;

  return {
    notificationList,
    markAsRead,
    unreadCount,
    loading,
    refetch: fetchNotifications,
  };
}; 