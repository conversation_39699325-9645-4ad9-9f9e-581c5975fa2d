/**
 * localStorage 工具函数
 * 提供统一的 localStorage 操作，并自动触发更新事件
 */

/**
 * 设置 localStorage 值并触发更新事件
 * @param key localStorage 键名
 * @param value 要存储的值
 */
export const setStorageItem = (key: string, value: any) => {
  const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
  localStorage.setItem(key, stringValue);
  
  // 触发自定义事件，通知其他组件 localStorage 已更新
  window.dispatchEvent(new CustomEvent('localStorageChange', {
    detail: { key, value }
  }));
};

/**
 * 获取 localStorage 值（自动解析JSON）
 * @param key localStorage 键名
 * @param defaultValue 默认值
 * @returns 解析后的值或默认值
 */
export const getStorageItem = (key: string, defaultValue: any) => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    // 尝试解析JSON，如果失败则返回原始字符串
    try {
      return JSON.parse(item);
    } catch {
      return item;
    }
  } catch (error) {
    console.error(`Error getting ${key} from localStorage:`, error);
    return defaultValue;
  }
};

/**
 * 获取 localStorage 值（返回原始字符串）
 * @param key localStorage 键名
 * @param defaultValue 默认值
 * @returns 原始字符串或默认值
 */
export const getStorageItemAsString = (key: string, defaultValue: string) => {
  try {
    const item = localStorage.getItem(key);
    return item !== null ? item : defaultValue;
  } catch (error) {
    console.error(`Error getting ${key} from localStorage:`, error);
    return defaultValue;
  }
};

/**
 * 移除 localStorage 项并触发更新事件
 * @param key localStorage 键名
 */
export const removeStorageItem = (key: string) => {
  localStorage.removeItem(key);
  
  // 触发自定义事件，通知其他组件 localStorage 已更新
  window.dispatchEvent(new CustomEvent('localStorageChange', {
    detail: { key, value: null }
  }));
}; 