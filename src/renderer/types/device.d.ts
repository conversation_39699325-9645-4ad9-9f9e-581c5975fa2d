export interface DeviceConfigRequestType {
  userName: string;      // 用户名
  bucketName: string;    // 桶名称
  region: string;        // 区域
  secretId: string;      // 腾讯云账号的SecretID
  secretKey: string;     // 腾讯云账号的SecretKey
  host: string;          // 数据库域名
  port: number;          // 数据库端口
  account: string;       // 数据库账号
  password: string;      // 数据库密码
  appId: string;         // 腾讯短信appId
  appKey: string;        // 腾讯短信appkey
  agentId: string;       // 钉钉agentId
  clientId: string;      // 钉钉clientId
  clientSecret: string;  // 钉钉clientSecret
  phone: string;         // 钉钉手机号
}