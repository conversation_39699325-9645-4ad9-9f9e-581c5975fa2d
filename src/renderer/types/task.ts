// 任务运行状态
export enum TaskStatus {
  Success = 0, // 成功
  Fail = 1,    // 失败
  Running = 2, // 运行中
}
// 任务状态
export enum TaskItemStatus {
  Normal = 1, // 正常
  Deleted = 2, // 已删除
}
export interface TaskTargetItemType {
  url: string;
  name: string;
}
// 单个任务类型
export interface TaskItemType {
  taskId: number; // 任务id
  taskName: string; // 任务名称
  taskTarget: TaskTargetItemType[]; // 目标酒店URL及名称
  toolId: number; // 工具id
  toolName: string; // 工具名称
  startDate: string; // 开始时间 YYYY-MM-DD
  endDate: string; // 结束时间 YYYY-MM-DD
  collectionTime: string[]; // 采集时间
  taskStatus?: number; // 1-运行中 2-已删除
  collectionDate: number; // 采集日期范围 1-今天 7-近7天 14-近14天 30-近30天
  collectionMetric: number[]; // 采集指标
}

// 出参类型
export interface TaskListResponse {
  taskList: TaskItemType[]; // 任务列表
  total: number; // 总数
}
export interface TaskEditItem extends Omit<TaskItemType, "taskId" | "startDate" | "endDate"> {
  taskId?: number | null; // 任务id（创建时为空）
  startDate: string | null; // 开始时间 YYYY-MM-DD
  endDate: string | null; // 结束时间 YYYY-MM-DD
}
export interface TaskListRequestParams {
  taskName?: string | null; // 任务名称（非必传）
  taskUrl?: string | null; // 目标URL（非必传）
  toolId: number; // 工具id，全部的话传-1
  pageNum: number; // 当前页数
  pageSize: number; // 页条数
}
// 单条运行历史记录
export interface TaskRecordItemType {
  taskStatus: TaskStatus; // 任务状态 1-运行中 2-成功 3-失败
  taskStartDate: string; // 任务开始时间
  taskSummary: string; // 状态运行成功时会出现的信息摘要
  fileName: string; // 对应文件名
}

// 任务日志响应
export interface TaskLogResponse {
  taskId: number; // 工具id
  taskName: string; // 任务名称
  taskTarget: TaskTargetItemType[]; // 目标url
  toolId: number; // 工具id
  toolName: string; // 工具名称
  startDate: string; // 开始时间
  endDate: string; // 结束时间
  collectionTime: string[]; // 采集时间
  taskRecord: TaskRecordItemType[]; // 运行历史记录
}

export interface TaskTargetType {
  name: string
  url: string
}
export interface TaskConfigCreateOrUpdateType extends Omit<TaskItemType, "taskId"> {
  /** 任务id */
  taskId?: number
}