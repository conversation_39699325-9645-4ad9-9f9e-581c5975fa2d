// 设置相关的类型定义
export interface SettingsModel {
  /** 用户名 */
  userName: string;
  /** 桶名称 */
  bucketName: string;
  /** 区域 */
  region: string;
  /** 腾讯云密钥SecretID */
  secretId: string;
  /** 腾讯云密钥SecretKey */
  secretKey: string;
  /** 数据库主机 */
  host: string;
  /** 数据库端口 */
  port: number;
  /** 数据库账号 */
  account: string;
  /** 数据库密码 */
  password: string;
  /** DingTalk - 微应用标识 */
  agentId: string;
  /** DingTalk- 唯一身份标识 */
  clientId: string;
  /** DingTalk - 系统密钥 */
  clientSecret: string;
  /** 推送手机号 */
  phone: string;
}