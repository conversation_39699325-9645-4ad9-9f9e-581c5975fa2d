export interface CollectionDataType {
  /** 采集日期 YYYY-MM-DD */
  date: string
  /** 采集时间 HH:MM:SS */
  time: string
  /** 任务id */
  missionId: number
  data: CollectionDataItemType[]
  /** 采集状态 0 -- 采集成功； 1 -- 采集失败 */
  status: number
  /** 采集失败原因 */
  message: string
}

export interface CollectionDataItemType {
  /** 酒店名称 */
  hotelName: string
  /** 房型 */
  roomName: string
  /** 销售价 */
  salePrice: string
  /** 最低价 */
  lowestPrice: string
  /** 早餐 */
  breakfast: string
  /** 取消规则 */
  cancelRule: string
  /** 确认规则 */
  confirmRule: string
  /** 付款方式 */
  payWay: string
  /** 促销信息 */
  promotion: string[]
  /** 房型基本信息 */
  roomBasicInfo: string[]
}