
export interface CollectionDataRepositoryType {
  id: number
  user_name: string
  tool_id: number
  task_id: number
  /** 任务名称 */
  task_name: string
  /** 原始数据 */
  ext_info: string
  gmt_create: Date
  gmt_modified: Date
  /** 采集日期 */
  collection_date: string
  /** 采集时间 */
  collection_time: string
  /** 采集行数 */
  collection_line: number
  /** 采集状态 */
  collection_status: number
  /** 采集成功/失败原因 */
  message: string
}