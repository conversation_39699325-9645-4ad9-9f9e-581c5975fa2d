
export interface TaskRecordRepositoryType {
  tool_id: number
  task_id: number
  task_name: string
  record_name: string
  target_url: string
  start_date: Date
  end_date: Date
  time_list: string
  delete: number
}

export interface TaskRecordHistoryRepositoryType {
  task_id: number
  /** 1 -- 运行中 2 -- 成功 3 -- 失败 */
  run_status: number
  data_count: number
  error_message: string
  start_time: Date
  end_time: Date
  file_name: string
  collection_date: string
  collection_time: string
}

export interface TaskRecordUnionRepositoryType extends TaskRecordRepositoryType {
  runHistory: TaskRecordHistoryRepositoryType[]
}