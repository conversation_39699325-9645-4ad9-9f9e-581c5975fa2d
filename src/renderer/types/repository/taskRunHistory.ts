export interface TaskRunHistoryRepositoryType {
  id: number
  user_name: string
  task_id: number
  run_status: number
  data_count: number
  error_message: string
  start_time: string
  end_time: string
  task_name: string
  tool_name: string
  target_url: string
  file_name: string
  tool_id: number
  gmt_create: string
  poi_names: string
  collection_id: string
  collection_date: string
  collection_time: string
}