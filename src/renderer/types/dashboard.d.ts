// 最近任务状态项类型
export interface RecentTaskStatusItemType {
  taskName: string;        // 任务名称
  taskId: number;          // 任务唯一标识
  taskStartDate: string;   // 任务开始时间
  taskEndDate: string;     // 任务结束时间
  taskStatus: TaskStatus;  // 任务状态
  poiNames: string[];          // 目标酒店名
}

// 仪表板主要数据类型
export interface DashboardDataModel {
  collectionTimes: number;                        // 采集数据次数
  collectionLine: number;                          // 采集数据行数
  executeSuccRate: number;                         // 任务执行成功率
  recentTaskStatus: RecentTaskStatusItemType[];   // 最近任务状态
  noticeList: AnnouncementItemType[];           // 系统公告
}

export interface AnnouncementItemType {
  id: number;
  title: string; // 标题
  content: string; // 对应公告内容
  updateTime: string; // YYYY-MM-DD
}
