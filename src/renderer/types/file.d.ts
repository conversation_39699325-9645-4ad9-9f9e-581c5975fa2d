import { TaskTargetType } from "./task";
// 执行记录查询参数类型
export interface ExecuteRecordListParams {
  fileName?: string | null; // 文件名（非必传）
  toolId: number; // 工具id，全部传-1
  startDate?: string | null; // 创建时间 YYYY-MM-DD（非必传）
  endDate?: string | null; // 结束时间 YYYY-MM-DD（非必传）
  pageNum: number; // 第几页
  pageSize: number; // 页条目数
}
export interface ExecuteRecordListResponse {
  recordList: ExecuteRecordItemType[]; // 执行记录列表
  total: number; // 总数
}

// 单条执行记录
export interface ExecuteRecordItemType {
  fileId: number; // 文件id
  fileName: string; // 文件名
  toolId: number; // 工具id
  toolName: string; // 工具名称
  fileLine: number; // 数据行数
  createTime: string; // 创建时间
}
// 预览文件内容接口返回
export interface PreviewExecuteFileResponse {
  fileId: number; // 文件id
  fileName: string; // 文件名
  taskTarget: TaskTargetType[]; // 全部酒店名称
  collectionTime: string; // 采集时间 YYYY-MM-DD HH:mm:ss
  collectionLine: number; // 采集数据行数
  toolId: number; // 工具id
  toolName: string; // 工具名称
  tableData: PreviewTableDataType; // 预览表格数据（动态表格）
}

// 动态表格数据类型
export interface PreviewTableDataType {
  columns: PreviewTableColumn[]; // 表头定义
  data: Record<string, string | string[]>[]; // 表格数据，每行为一个对象，key为列key
}

// 表头定义
export interface PreviewTableColumn {
  key: string; // 字段名
  title: string; // 列显示名
}
