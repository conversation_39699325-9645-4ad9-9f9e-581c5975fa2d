// 工具项
export interface ToolConfigItem {
  toolId: number; // 工具id
  toolAvatar: string; // 工具头像
  toolName: string; // 工具名称
  toolDesc: string; // 工具简介
  toolVersion: string; // 版本号
  lastUpdateDate: string; // 最后更新时间 YYYY-MM-DD
  toolMetric: ToolMetricItemType[]; // 采集指标
  updateRecord: UpdateRecordItemType[]; // 更新日志
  urlRegex: string; // 目标URL正则表达式
}

// 采集指标
export interface ToolMetricItemType {
  label: string; // 采集指标名称（酒店名称、地址、入住日期等）
  value: number; // 对应key的value值
}

// 更新日志
export interface UpdateRecordItemType {
  updateVersion: string; // 更新版本号
  updateDate: string; // 更新日期
  updateDesc: string[]; // 更新描述
}
