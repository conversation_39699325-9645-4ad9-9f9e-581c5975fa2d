import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider } from 'antd';
import { saveTencentCosConfig, getTencentCosConfig } from '../../api/common';
import { SaveOutlined } from '@ant-design/icons';
import type { SettingsModel } from '../../types/settings';
import { setStorageItem, getStorageItem, getStorageItemAsString } from '../../utils/storage';
import { useNavigate } from 'react-router-dom';
import './index.scss';
import { settingsAPI } from '../../api/settings';

const { Title, Paragraph } = Typography;

const Settings: React.FC = () => {
  const [form] = Form.useForm<SettingsModel>();
  const navigate = useNavigate();
  const [isUserNameSubmitted, setIsUserNameSubmitted] = useState(false);

  // 初始化时加载当前的设置
  useEffect(() => {
    const loadSettings = async () => {
      const currentUserName = getStorageItemAsString('userName', '');
      
      // 检查用户名是否已经提交过
      if (currentUserName) {
        setIsUserNameSubmitted(true);
      }
      
      // 设置默认值
      form.setFieldsValue({
        userName: currentUserName,
        bucketName: '',
        region: '',
        secretId: '',
        secretKey: '',
        host: '',
        port: undefined,
        account: '',
        password: '',
        agentId: '',
        clientId: '',
        clientSecret: '',
        phone: '',
      });

      // 数据回显
      if (currentUserName) {
        const configData = await getTencentCosConfig();
        if (configData) {
          // 将接口返回的数据填充到表单中
          form.setFieldsValue({
            ...form.getFieldsValue(),
            ...configData,
          });
        }
      }
    };

    loadSettings();
  }, [form]);

  const handleFinish = async (values: SettingsModel) => {
    try {  
      const currentUserName = getStorageItemAsString('userName', '');

      // 确保port是数字类型
      const settingsToSave = {
        ...values,
        port: Number(values.port)
      };

      // 写入localStorage
      setStorageItem('userName', values.userName);
      setStorageItem('settings', settingsToSave);

      // 写入内存
      await settingsAPI.saveSettings(settingsToSave);
      // 标记用户名已提交
      setIsUserNameSubmitted(true);
      // 首次设置，数据库未连接
      if(!currentUserName) {
         // 设置完成后自动跳转到dashboard页面
        setTimeout(() => {
          navigate('/collection/dashboard');
        }, 1000);
      } else {
        const res = await saveTencentCosConfig(settingsToSave);
        if(res) {
          message.success('保存成功');
        } else {
          message.error('保存失败');
        }
      }
    } catch (error: any) {
      message.error(error?.message || '保存失败，请重试');
    }
  };

  return (
    <div className="settings">
      <Title level={2} className="settings__title">系统设置</Title>
      
      <Card className="settings__card" bordered>

        <Form
          form={form}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          className="settings__form"
          onFinish={handleFinish}
          initialValues={{
            userName: '',
            bucketName: '',
            region: '',
            secretId: '',
            secretKey: '',
            host: '',
            port: null,
            account: '',
            password: '',
            agentId: '',
            clientId: '',
            clientSecret: '',
            phone: '',
          }}
        >
          <Form.Item 
            label="用户名" 
            name="userName"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input 
              placeholder="请输入用户名" 
              disabled={isUserNameSubmitted}
            />
          </Form.Item>

          <Divider orientation="left" orientationMargin={0}>数据库信息</Divider>
          
          <Form.Item 
            label="数据库域名" 
            name="host"
            rules={[{ required: true, message: '请输入数据库域名' }]}
          >
            <Input placeholder="例如：localhost" />
          </Form.Item>
          
          <Form.Item 
            label="数据库端口" 
            name="port"
            rules={[{ required: true, message: '请输入数据库端口号' }]}
          >
            <Input type="number" placeholder="例如：3306" />
          </Form.Item>
          
          <Form.Item 
            label="数据库账号" 
            name="account"
            rules={[{ required: true, message: '请输入数据库账号' }]}
          >
            <Input placeholder="数据库账号" />
          </Form.Item>
          
          <Form.Item 
            label="数据库密码" 
            name="password"
            rules={[{ required: true, message: '请输入数据库密码' }]}
          >
            <Input.Password placeholder="数据库密码" />
          </Form.Item>

          <Divider orientation="left" orientationMargin={0}>对象存储信息</Divider>
          
          <Form.Item 
            label="桶名称" 
            name="bucketName"
            rules={[{ required: true, message: '请输入桶名称' }]}
          >
            <Input placeholder={`例如：hotel-data-collector`} />
          </Form.Item>
          
          <Form.Item 
            label="区域 (Region)" 
            name="region"
            rules={[{ required: true, message: '请输入区域' }]}
          >
            <Input placeholder='例如：ap-beijing' />
          </Form.Item> 
         
          <Form.Item 
            label="Secret ID" 
            name="secretId"
            rules={[{ required: true, message: '请输入腾讯云账号的SecretID' }]}
          >
            <Input placeholder="腾讯云账号的 Secret ID" />
          </Form.Item>
          <Form.Item 
            label="Secret Key" 
            name="secretKey"
            rules={[{ required: true, message: '请输入腾讯云账号的SecretKey' }]}
          >
            <Input.Password placeholder="腾讯云账号的 Secret Key" />
          </Form.Item>

          <Divider orientation="left" orientationMargin={0}>钉钉配置信息</Divider>
          
          <Form.Item 
            label="钉钉 微应用标识" 
            name="agentId"
            rules={[{ required: true, message: '请输入钉钉 微应用标识' }]}
          >
            <Input placeholder="微应用标识" />
          </Form.Item>

          <Form.Item 
            label="钉钉 唯一身份标识" 
            name="clientId"
            rules={[{ required: true, message: '请输入钉钉 唯一身份标识' }]}
          >
            <Input placeholder="唯一身份标识" />
          </Form.Item>

          <Form.Item 
            label="钉钉 系统密钥" 
            name="clientSecret"
            rules={[{ required: true, message: '请输入钉钉 系统密钥' }]}
          >
            <Input.Password placeholder="系统密钥" />
          </Form.Item>
          
          <Form.Item 
            label="钉钉 推送手机号" 
            name="phone"
            rules={[{ required: true, message: '请输入推送手机号' }]}
          >
            <Input placeholder="推送手机号" />
          </Form.Item>
          
          <Form.Item style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Settings;