import React, { useState, useEffect } from 'react';
import { Card, Button, Tag, Typography, Space, Alert, Avatar, message } from 'antd';
import { 
  CheckCircleOutlined, 
  InfoCircleOutlined, 
  ClockCircleOutlined, 
  DownOutlined, 
  UpOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import './index.scss';
import { fetchToolConfig } from '../../api/common';
import { ToolConfigItem } from '../../types/tools';

const { Title, Paragraph } = Typography;
const ToolManagement: React.FC = () => {
  const [expandedTool, setExpandedTool] = useState<number| null>();
  const [tools, setTools] = useState<ToolConfigItem[]>([]);
  const [loading, setLoading] = useState(false);

  const toggleExpand = (id: number) => {
    setExpandedTool(expandedTool === id ? null : id);
  };
  useEffect(() => {
    setLoading(true);
    fetchToolConfig().then(res => {
      setTools(res || []);
    }).catch(err => {
      message.error(err?.message || '获取工具配置失败');
    }).finally(() => {
      setLoading(false);
    });
  }, []);

  return (
    <div className="tool-management">
      <Title level={2} className="tool-management__title">工具管理</Title>
      
      <Space direction="vertical" style={{ width: '100%' }} size={24}>
        {loading && <div>加载中...</div>}
        {!loading && tools.map(tool => (
          <Card 
            key={tool.toolId} 
            className="tool-management__card" 
            bordered
          >
            <div className="tool-management__card-content">
              <Avatar 
                src={tool.toolAvatar} 
                size={64} 
                shape="square"
                className="tool-management__card-avatar"
              />
              <div className="tool-management__card-info">
                <div className="tool-management__card-header">
                  <div>
                    <Title level={4} className="tool-management__card-name">{tool.toolName}</Title>
                    <Paragraph className="tool-management__card-desc">{tool.toolDesc}</Paragraph>
                  </div>
                  <Tag color="green" className="tool-management__status-tag">
                    <CheckCircleOutlined className="tool-management__meta-icon" />
                    活跃
                  </Tag>
                </div>
                
                <div className="tool-management__card-meta">
                  <span className="tool-management__meta-item">
                    <InfoCircleOutlined className="tool-management__meta-icon" />
                    版本: {tool.toolVersion}
                  </span>
                  <span className="tool-management__meta-item">
                    <ClockCircleOutlined className="tool-management__meta-icon" />
                    最后更新: {tool.lastUpdateDate}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="tool-management__toggle-container">
              <Button
                type="link"
                onClick={() => toggleExpand(tool.toolId)}
                icon={expandedTool === tool.toolId ? <UpOutlined /> : <DownOutlined />}
                className="tool-management__toggle-button"
              >
                {expandedTool === tool.toolId ? '收起更新日志' : '查看更新日志'}
              </Button>
            </div>
            
            {expandedTool === tool.toolId && (
              <div className="tool-management__expanded-content">
                <Title level={5} className="tool-management__changelog-title">更新日志</Title>
                <div className="tool-management__timeline">
                  {tool?.updateRecord?.map((log, index) => (
                    <div 
                      key={index} 
                      className={`tool-management__timeline-item ${index === tool.updateRecord.length - 1 ? 'tool-management__timeline-item--last' : ''}`}
                    >
                      {index !== tool.updateRecord.length - 1 && (
                        <div className="tool-management__timeline-line"></div>
                      )}
                      <div className="tool-management__timeline-dot"></div>
                      <div>
                        <div className="tool-management__version-header">
                          <span className="tool-management__version-title">版本 {log.updateVersion}</span>
                          <Tag className="tool-management__version-tag">{log.updateDate}</Tag>
                        </div>
                        <ul className="tool-management__update-list">
                          {log.updateDesc.map((change, changeIndex) => (
                            <li key={changeIndex} className="tool-management__update-item">
                              <span className="tool-management__update-bullet">•</span>
                              <span>{change}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>
        ))}
      </Space>
      
      <Alert
        type="warning"
        showIcon
        icon={<ExclamationCircleOutlined />}
        message="注意事项"
        description="工具由系统预设，目前不支持自定义添加新工具。如有特殊需求，请联系技术支持。"
        className="tool-management__notice"
      />
    </div>
  );
};

export default ToolManagement;