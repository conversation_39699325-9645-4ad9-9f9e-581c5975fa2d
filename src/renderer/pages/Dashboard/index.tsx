import React, { useState, useEffect } from "react";
import { Card, Row, Col, List, Tag, Space, Typography, message, Spin } from "antd";
import NotificationContent from "../../components/NotificationContent";
import {
  BellOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import "./index.scss";
import { fetchCoreMetric } from "../../api/common";
import {
  DashboardDataModel,
  RecentTaskStatusItemType,
} from "../../types/dashboard.d";
import { TaskStatus } from "../../types/task";

const { Title, Text, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const [coreMetric, setCoreMetric] = useState<DashboardDataModel | null>(null);
  const [loading, setLoading] = useState(false);
  const [readAnnouncements, setReadAnnouncements] = useState<Set<number>>(new Set());

  // 从localStorage加载已读公告状态
  useEffect(() => {
    const storedReadAnnouncements = localStorage.getItem('readAnnouncements');
    if (storedReadAnnouncements) {
      try {
        const readIds = JSON.parse(storedReadAnnouncements);
        setReadAnnouncements(new Set(readIds));
      } catch (error) {
        console.error('解析已读公告数据失败:', error);
      }
    }
  }, []);

  // 处理公告点击，标记为已读
  const handleAnnouncementClick = (announcementId: number) => {
    const newReadAnnouncements = new Set(readAnnouncements);
    newReadAnnouncements.add(announcementId);
    setReadAnnouncements(newReadAnnouncements);
    
    // 保存到localStorage
    localStorage.setItem('readAnnouncements', JSON.stringify(Array.from(newReadAnnouncements)));
  };

  const stats = [
    {
      title: "采集数据次数",
      value: coreMetric?.collectionTimes || "-",
      icon: (
        <FileTextOutlined className="dashboard__stat-icon dashboard__stat-icon--blue" />
      ),
    },
    {
      title: "采集数据行数",
      value: coreMetric?.collectionLine || "-",
      icon: (
        <DatabaseOutlined className="dashboard__stat-icon dashboard__stat-icon--green" />
      ),
    },
    {
      title: "任务执行成功率",
      value: coreMetric?.executeSuccRate + "%" || "-",
      icon: (
        <CheckCircleOutlined className="dashboard__stat-icon dashboard__stat-icon--purple" />
      ),
    },
  ];

  useEffect(() => {
    setLoading(true);
    fetchCoreMetric()
      .then((res) => {
        setCoreMetric(res as DashboardDataModel);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <Spin spinning={loading} tip="加载中...">
      <div className="dashboard">
        <Title level={2} className="dashboard__title">
          仪表盘
        </Title>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} className="dashboard__stats-row">
          {stats.map((stat, idx) => (
            <Col xs={24} sm={8} key={stat.title}>
              <Card bordered className="dashboard__stat-card">
                <div className="dashboard__stat-content">
                  <div>
                    <Text type="secondary" className="dashboard__stat-text">
                      {stat.title}
                    </Text>
                    <Title level={3} className="dashboard__stat-value">
                      {stat.value}
                    </Title>
                  </div>
                  <div className="dashboard__stat-icon-container">
                    {stat.icon}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 消息提醒和系统公告 */}
        <Row gutter={[24, 24]} className="dashboard__info-row">
          <Col xs={24} md={12}>
            <Card
              title={
                <span>
                  <BellOutlined className="dashboard__info-title" />
                  消息提醒
                </span>
              }
              bordered
              className="dashboard__info-card"
            >
             <NotificationContent />
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card title="系统公告" bordered className="dashboard__info-card">
              <div className="dashboard__announcements">
                {coreMetric?.noticeList && coreMetric.noticeList.length > 0 ? (
                  coreMetric.noticeList.map((item, index) => {
                    const isRead = readAnnouncements.has(item.id);
                    return (
                      <div
                        key={item.id}
                        className="dashboard__announcement-item"
                        style={{
                          borderLeft: `4px solid ${isRead ? '#fbbf24' : '#3b82f6'}`,
                          cursor: 'pointer',
                        }}
                        onClick={() => handleAnnouncementClick(item.id)}
                      >
                        <Text strong className="dashboard__announcement-title">
                          {item.title}
                        </Text>
                        <Text className="dashboard__announcement-desc">
                          {item.content}
                        </Text>
                        <Text
                          type="secondary"
                          className="dashboard__announcement-date"
                        >
                          {item.updateTime}
                        </Text>
                      </div>
                    );
                  })
                ) : (
                  <div className="dashboard__no-announcement">
                    <Text type="secondary">暂无系统公告</Text>
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>

        {/* 最近任务状态 */}
        <Card title="最近任务状态" bordered className="dashboard__recent-card">
          <div className="dashboard__recent-tasks">
            {coreMetric?.recentTaskStatus?.map(
              (item: RecentTaskStatusItemType, index: number) => (
                <div key={index} className="dashboard__task-item">
                  <div>
                    <Text strong className="dashboard__task-name">
                      {item.taskName}-{item.taskId}
                    </Text>
                    <Text type="secondary" className="dashboard__task-target">
                      目标: {item.poiNames?.join('，')}
                    </Text>
                  </div>
                  <Tag
                    color={
                      item.taskStatus === TaskStatus.Success
                        ? "green"
                        : item.taskStatus === TaskStatus.Fail
                        ? "red"
                        : "blue"
                    }
                    className="dashboard__task-status"
                  >
                    {item.taskStatus === TaskStatus.Success
                      ? "成功"
                      : item.taskStatus === TaskStatus.Fail
                      ? "失败"
                      : "进行中"}
                  </Tag>
                </div>
              )
            )}
          </div>
        </Card>
      </div>
    </Spin>
  );
};

export default Dashboard;
