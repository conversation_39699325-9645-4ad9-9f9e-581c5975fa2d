// MainPage 专用的 SCSS 变量
$primary-color: #4facfe;
$secondary-color: #3d8bfe;
$success-color: #28a745;
$error-color: #dc3545;

$white: #ffffff;
$light-gray: #f8f9fa;
$gray: #e9ecef;
$dark-gray: #495057;
$dark: #2d3748;
$light-text: #e2e8f0;

$border-radius: 10px;
$border-radius-small: 6px;
$border-radius-large: 15px;

$font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
$font-size-base: 14px;
$font-size-lg: 2rem;
$font-size-md: 1.5rem;

$padding-base: 20px;
$padding-lg: 30px;
$padding-sm: 15px;
$padding-xs: 10px;

$breakpoint-mobile: 768px;

// SCSS 混合器
@mixin gradient-background($color1, $color2) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
}

@mixin box-shadow($shadow) {
  box-shadow: $shadow;
}

@mixin button-hover-effect {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

@mixin status-indicator($color) {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  background: $color;
}

// 主要布局
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: $white;
  border-radius: $border-radius-large;
  @include box-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

// 头部样式
.header {
  @include gradient-background($primary-color, #00f2fe);
  color: $white;
  padding: $padding-lg;
  text-align: center;

  h1 {
    margin: 0 0 $padding-xs 0;
    font-size: $font-size-lg;
  }

  p {
    margin: 0;
    opacity: 0.9;
  }
}

// 内容区域
.content {
  padding: $padding-lg;
  flex: 1;
  overflow-y: auto;
}

// 状态卡片
.status-card {
  background: $light-gray;
  border: 1px solid $gray;
  border-radius: $border-radius;
  padding: $padding-base;
  margin-bottom: $padding-base;

  h3 {
    margin: 0 0 $padding-sm 0;
    color: $dark-gray;
  }
}

// 状态指示器
.status-indicator {
  @include status-indicator($primary-color);
}

.status-running {
  @include status-indicator($success-color);
  animation: pulse 2s infinite;
}

.status-stopped {
  @include status-indicator($error-color);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 按钮样式
.btn {
  background: $primary-color;
  color: $white;
  border: none;
  padding: 12px 24px;
  border-radius: $border-radius-small;
  cursor: pointer;
  font-size: $font-size-base;
  margin: 5px;
  @include button-hover-effect;

  &:hover {
    background: $secondary-color;
  }
}

// 通知日志
.notification-log {
  background: $dark;
  color: $light-text;
  border-radius: $border-radius-small;
  padding: $padding-sm;
  font-family: monospace;
  height: 200px;
  overflow-y: auto;
  margin-top: $padding-base;
}

.log-entry {
  margin: 5px 0;
  padding: 5px;
  border-left: 3px solid $primary-color;
  padding-left: $padding-xs;
  font-size: $font-size-base;
}

// 端点信息
.endpoint-info {
  background: #e7f3ff;
  border: 1px solid #b8daff;
  border-radius: $border-radius-small;
  padding: $padding-sm;
  margin: $padding-sm 0;

  h4 {
    margin: 0 0 $padding-xs 0;
    color: #004085;
  }
}

.code-snippet {
  background: $light-gray;
  border: 1px solid $gray;
  border-radius: $border-radius-small;
  padding: $padding-sm;
  font-family: monospace;
  overflow-x: auto;
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 1.4;
  margin-top: $padding-xs;
}

// 响应式设计
@media (max-width: $breakpoint-mobile) {
  .container {
    margin: 0;
    border-radius: $border-radius;
  }

  .header {
    padding: $padding-base;

    h1 {
      font-size: $font-size-md;
    }
  }

  .content {
    padding: $padding-base;
  }

  .btn {
    padding: $padding-xs $padding-base;
    font-size: 13px;
    margin: 3px;
  }
}
