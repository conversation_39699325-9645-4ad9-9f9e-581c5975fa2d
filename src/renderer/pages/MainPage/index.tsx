import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ipc<PERSON>ender<PERSON> } from 'electron'
import { getStorageItemAsString } from '../../utils/storage'
import './index.scss'

// 定义 Electron IPC 类型
declare global {
  interface Window {
    electronAPI: {
      ipcRenderer: {
        send: (channel: string, ...args: any[]) => void
        on: (channel: string, listener: (...args: any[]) => void) => void
        invoke: (channel: string, ...args: any[]) => Promise<any>
        removeAllListeners: (channel: string) => void
      }
    }
  }
}

interface LogEntry {
  id: number
  timestamp: string
  message: string
  type: 'info' | 'success' | 'error' | 'test'
}

interface ServerStatus {
  running: boolean
  port: number
}

const MainPage: React.FC = () => {
  const navigate = useNavigate()
  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: 1,
      timestamp: new Date().toLocaleTimeString(),
      message: '[系统] 等待 Automa 工作流通知...',
      type: 'info',
    },
  ])
  const [serverStatus, setServerStatus] = useState<ServerStatus>({
    running: true,
    port: 3000,
  })
  const [logCounter, setLogCounter] = useState(2)

  // 获取 Electron IPC 渲染器
  const ipcRenderer = window.require('electron').ipcRenderer

  // 添加日志条目
  const addLogEntry = (message: string, type: LogEntry['type'] = 'info') => {
    const newEntry: LogEntry = {
      id: logCounter,
      timestamp: new Date().toLocaleTimeString(),
      message,
      type,
    }
    setLogs((prev) => [...prev, newEntry])
    setLogCounter((prev) => prev + 1)
  }

  // 测试通知
  const testNotification = () => {
    ipcRenderer.send('test-notification')
    addLogEntry('🔔 发送测试通知', 'test')
  }

  // 检查服务器状态
  const checkServerStatus = () => {
    ipcRenderer.send('get-server-status')
    addLogEntry('📊 检查服务器状态', 'info')
  }

  const createTask = () => {
    ipcRenderer.send('create-task')
    addLogEntry('🚀 创建任务', 'info')
  }

  const queryTaskList = () => {
    ipcRenderer.send('query-task-list')
    addLogEntry('🚀 查询任务列表', 'info')
  }

  const queryToolConfig = () => {
    ipcRenderer.send('query-tool-config')
    addLogEntry('🚀 查询工具配置', 'info')
  }

  const deleteTask = () => {
    ipcRenderer.send('delete-task')
    addLogEntry('🗑️ 删除任务', 'info')
  }

  const queryTaskRecord = () => {
    ipcRenderer.send('query-task-record')
    addLogEntry('🚀 查询任务执行记录', 'info')
  }

  const dbTestConnection = () => {
    ipcRenderer.send('db-test-connection')
    addLogEntry('🚀 测试数据库连接', 'info')
  }

  const cosTestUpload = () => {
    ipcRenderer.send('cos-test-upload')
    addLogEntry('🚀 测试COS上传', 'info')
  }

  const saveSettings = () => {
    ipcRenderer.send('save-settings')
    addLogEntry('🚀 保存设置', 'info')
  }

  const getSettings = () => {
    ipcRenderer.send('get-settings')
    addLogEntry('🚀 获取设置', 'info')
  }

  const collectionComplete = () => {
    ipcRenderer.send('collection-complete')
    addLogEntry('🚀 采集成功模拟并存储对应数据', 'info')
  }

  const queryCoreMetric = () => {
    ipcRenderer.send('query-core-metric')
    addLogEntry('🚀 查询大盘数据', 'info')
  }

  const previewExecuteFile = () => {
    ipcRenderer.send('preview-execute-file')
    addLogEntry('🚀 预览执行文件', 'info')
  }

  const insertFailCollection = () => {
    ipcRenderer.send('insert-fail-collection')
    addLogEntry('🚀 插入失败采集数据', 'info')
  }

  const reportDingTalk = () => {
    ipcRenderer.send('report-ding-talk')
    addLogEntry('🚀 推送钉钉', 'info')
  }

  // 清空日志
  const clearLog = () => {
    setLogs([
      {
        id: logCounter,
        timestamp: new Date().toLocaleTimeString(),
        message: '[系统] 日志已清空',
        type: 'info',
      },
    ])
    setLogCounter((prev) => prev + 1)
  }

  // 打开采集页面
  const openCollectionPage = () => {
    const isSettings = getStorageItemAsString('userName', '')
    if (isSettings) {
      navigate('/collection/dashboard')
      addLogEntry('🚀 导航到主页面', 'info')
    } else {
      navigate('/settings')
      addLogEntry('🚀 导航到设置页面', 'info')
    }
  }

  // 打开 Chrome 扩展
  const openChromeExtension = () => {
    ipcRenderer.send('open-chrome-extension', {
      toolId: 1,
      customConfig: {
        insertData: [
          {
            name: '喆啡酒店',
            date: '2025-07-18',
            url: 'https://hotels.ctrip.com/hotels/69428752.html?cityid=17&checkIn=2025-07-18&checkOut=2025-07-19&crn=1&adult=2&children=0',
          },
          {
            name: '北岸商银酒店',
            date: '2025-07-19',
            url: 'https://hotels.ctrip.com/hotels/detail/?cityId=1&checkIn=2025-07-19&checkOut=2025-07-20&hotelId=2920983&adult=1&crn=1&children=0&highprice=-1&lowprice=0&listfilter=1',
          },
          {
            name: '还有不知道什么酒店',
            date: '2025-07-24',
            url: 'https://hotels.ctrip.com/hotels/detail/?cityId=1&checkIn=2025-07-19&checkOut=2025-07-20&hotelId=122154965&adult=1&crn=1&children=0&highprice=-1&lowprice=0&listfilter=1',
          },
        ],
        days: 3,
      },
    })
    addLogEntry('🌐 正在打开 Chrome 扩展...', 'info')
  }

  // 设置 IPC 监听器
  useEffect(() => {
    // 监听 Automa 通知
    ipcRenderer.on('automa-notification', (event: any, data: any) => {
      addLogEntry(`🎉 收到 Automa 通知: ${data.message || '无消息'}`, 'success')
      if (data.workflowName) {
        addLogEntry(`📋 工作流名称: ${data.workflowName}`, 'info')
      }
    })

    // 监听服务器状态
    ipcRenderer.on('server-status', (event: any, status: ServerStatus) => {
      setServerStatus(status)
      if (status.running) {
        addLogEntry('✅ 服务器状态: 运行中', 'success')
      } else {
        addLogEntry('❌ 服务器状态: 已停止', 'error')
      }
    })

    // 监听 Chrome 扩展打开结果
    ipcRenderer.on(
      'chrome-extension-opened',
      (event: any, result: { success: boolean; error?: string }) => {
        if (result.success) {
          addLogEntry('✅ Chrome 扩展已成功打开', 'success')
        } else {
          addLogEntry(`❌ Chrome 扩展打开失败: ${result.error}`, 'error')
        }
      }
    )

    // 页面加载完成后检查状态
    addLogEntry('🚀 应用启动完成', 'info')
    setTimeout(() => {
      checkServerStatus()
    }, 1000)

    // 清理监听器
    return () => {
      ipcRenderer.removeAllListeners('automa-notification')
      ipcRenderer.removeAllListeners('server-status')
      ipcRenderer.removeAllListeners('chrome-extension-opened')
    }
  }, [])

  return (
    <div className="app">
      <div className="container">
        <div className="header">
          <h1>🚀 Electron-Automa 通信桥</h1>
          <p>监听 Automa 工作流完成通知</p>
        </div>

        <div className="content">
          <div className="status-card">
            <h3>服务器状态</h3>
            <p>
              <span
                className={`status-indicator ${
                  serverStatus.running ? 'status-running' : 'status-stopped'
                }`}
              ></span>
              <span>
                {serverStatus.running
                  ? `HTTP 服务器运行中 (端口 ${serverStatus.port})`
                  : 'HTTP 服务器已停止'}
              </span>
            </p>
            <p>
              <strong>监听端口:</strong> 3000
            </p>
            <p>
              <strong>通知端点:</strong> http://localhost:3000/automa-complete
            </p>
          </div>

          <div className="status-card">
            <h3>控制面板</h3>
            <button className="btn" onClick={testNotification}>
              🔔 测试通知
            </button>
            <button className="btn" onClick={checkServerStatus}>
              📊 检查状态
            </button>
            <button className="btn" onClick={clearLog}>
              🗑️ 清空日志
            </button>
            <button className="btn" onClick={openCollectionPage}>
              🚀 打开 采集页面
            </button>
            <button className="btn" onClick={openChromeExtension}>
              🌐 打开 Chrome 扩展
            </button>
            <button className="btn" onClick={createTask}>
              🚀 创建任务
            </button>
            <button className="btn" onClick={queryTaskList}>
              🚀 查询任务列表
            </button>
            <button className="btn" onClick={queryToolConfig}>
              🚀 查询工具配置
            </button>
            <button className="btn" onClick={deleteTask}>
              🗑️ 删除任务
            </button>
            <button className="btn" onClick={queryTaskRecord}>
              🚀 查询任务执行记录
            </button>
            <button className="btn" onClick={dbTestConnection}>
              🚀 测试数据库连接
            </button>
            <button className="btn" onClick={cosTestUpload}>
              🚀 测试COS上传
            </button>
            <button className="btn" onClick={saveSettings}>
              🚀 保存设置
            </button>
            <button className="btn" onClick={getSettings}>
              🚀 获取设置
            </button>
            <button className="btn" onClick={collectionComplete}>
              🚀 采集成功模拟并存储对应数据
            </button>
            <button className="btn" onClick={insertFailCollection}>
              🚀 插入失败采集数据
            </button>
            <button className="btn" onClick={queryCoreMetric}>
              🚀 查询大盘数据
            </button>
            <button className="btn" onClick={previewExecuteFile}>
              🚀 预览执行文件
            </button>
            <button className="btn" onClick={reportDingTalk}>
              🚀 推送钉钉
            </button>
          </div>

          <div className="endpoint-info">
            <h4>📡 Automa 集成说明</h4>
            <p>
              在您的 Automa 工作流最后添加"运行 JavaScript"动作，使用以下代码：
            </p>
            <div className="code-snippet">
              {`fetch('http://localhost:3000/automa-complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        message: 'Workflow finished successfully',
        time: Date.now(),
        workflowName: '{{ workflow.name }}' // Automa 变量
    })
});`}
            </div>
          </div>

                    <div className="endpoint-info">
            <h4>📡 Automa 集成说明</h4>
            <p>
              在您的 Automa 工作流最后添加"运行 JavaScript"动作，使用以下代码：
            </p>
            <div className="code-snippet">
              {`fetch('http://localhost:3000/automa-complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        message: 'Workflow finished successfully',
        time: Date.now(),
        workflowName: '{{ workflow.name }}' // Automa 变量
    })
});`}
            </div>
          </div>

          <div className="status-card">
            <h3>📋 通知日志</h3>
            <div className="notification-log">
              {logs.map((log) => (
                <div key={log.id} className="log-entry">
                  [{log.timestamp}] {log.message}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MainPage
