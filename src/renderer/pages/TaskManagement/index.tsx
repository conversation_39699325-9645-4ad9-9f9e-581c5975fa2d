import React, { useCallback, useRef, useState } from "react";
import { Table, Button, Input, Select, Modal, Space, Tag, message } from "antd";
import {
  PlusOutlined,
  FormOutlined,
  DeleteOutlined,
  FilterOutlined,
  SearchOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import "./index.scss";
import {
  fetchTaskList,
  deleteTask,
  fetchToolConfig,
  createOrUpdateTask,
} from "../../api/common";
import {
  TaskEditItem,
  TaskItemStatus,
  TaskItemType,
  TaskListRequestParams,
  TaskTargetItemType,
} from "../../types/task";
import { Popover } from "antd";
import TaskForm from "../../components/TaskForm/index";
import RunLogModal from "../../components/RunLogModal";
import { Typography } from "antd";
import { ToolConfigItem } from "../../types/tools";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { taskAPI } from "../../api/task-ls";
import { debounce } from "lodash";
const { Title } = Typography;

const TaskManagement: React.FC = () => {
  const allToolId = -1;
  const defaultPageSize = 10;
  const [tasks, setTasks] = useState<TaskItemType[]>([]);
  const [search, setSearch] = useState("");
  const [toolId, setToolId] = useState(allToolId);
  const [editingTask, setEditingTask] = useState<TaskItemType | null>(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [tools, setTools] = useState<ToolConfigItem[]>([]);
  const [formVisible, setFormVisible] = useState(false);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const queryParams = useRef({
    toolId: allToolId,
    pageNum: 1,
    pageSize: defaultPageSize,
  });

  const columns = [
    { title: "ID", dataIndex: "taskId", key: "taskId", width: 50 },
    { title: "任务名称", dataIndex: "taskName", key: "taskName" },
    {
      title: "目标URL",
      dataIndex: "taskTarget",
      key: "taskTarget",
      ellipsis: true,
      render: (targets: TaskTargetItemType[]) =>
        targets && targets.length > 0 ? (
          <Popover
            content={
              <div style={{ maxWidth: 300 }}>
                {targets.map((target, idx) => (
                  <div key={idx} style={{ wordBreak: "break-all" }}>
                    {target.url}
                  </div>
                ))}
              </div>
            }
          >
            <span>{targets.map((t) => t.url).join(", ")}</span>
          </Popover>
        ) : (
          "-"
        ),
    },
    { title: "工具", dataIndex: "toolName", key: "toolName" },
    {
      title: "日期范围",
      dataIndex: "startDate",
      key: "dateRange",
      width: 130,
      render: (_: any, record: any) =>
        `${record.startDate} 至 ${record.endDate}`,
    },
    {
      title: "采集时间",
      dataIndex: "collectionTime",
      key: "collectionTime",
      width: 120,
      render: (times: string[]) => (
        <div className="task-management__collection-time">
          <ClockCircleOutlined className="task-management__collection-time-icon" />
          <div className="task-management__collection-time-list">
            {times?.map((time, index) => (
              <span key={index}>
                {time}{index < times.length - 1 ? ',' : ''}
              </span>
            )) || ""}
          </div>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "taskStatus",
      key: "taskStatus",
      width: 90,
      render: (taskStatus: number) =>
        taskStatus === TaskItemStatus.Normal ? (
          <Tag color="green">正常</Tag>
        ) : (
          <Tag color="red">已删除</Tag>
        ),
    },
    {
      title: "操作",
      key: "action",
      width: 130,
      render: (_: any, record: TaskItemType) => (
        <Space wrap>
          {record.taskStatus === TaskItemStatus.Normal && <Button
            icon={<FormOutlined />}
            type="text"
            size="small"
            onClick={() => handleEdit(record)}
          />}
          <Button
            icon={<FileTextOutlined />}
            type="text"
            size="small"
            onClick={() => handleShowLog(record)}
          />
          {record.taskStatus === TaskItemStatus.Normal && (
            <Button
              icon={<DeleteOutlined />}
              type="text"
              size="small"
              onClick={() => handleDelete(record.taskId as number)}
            />
          )}
        </Space>
      ),
    },
  ];

  function handleEdit(task: TaskItemType) {
    setEditingTask(task);
    setFormVisible(true);
  }

  function handleDelete(taskId: number) {
    Modal.confirm({
      title: "确定要删除此任务吗？",
      okText: "确定",
      cancelText: "取消",
      icon: <ExclamationCircleOutlined style={{ color: "#ff4d4f" }} />,
      okButtonProps: { loading: submitLoading, danger: true },
      centered: true,
      onOk: async () => {
        try {
          setSubmitLoading(true);
          const data = await deleteTask(taskId);
          if (data !== -1) {
            message.success("任务已删除");
            debouncedFetchTasks();
          } else {
            message.error("任务删除失败");
          }
        } finally {
          setSubmitLoading(false);
        }
      },
    });
  }

  function handleAdd() {
    setEditingTask(null);
    setFormVisible(true);
  }

  function handleShowLog(task: TaskItemType) {
    setCurrentTaskId(task.taskId)
    setLogModalVisible(true);
  }

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const res = await fetchTaskList(queryParams.current);
      const { taskList = [], total = 0 } = res;
      setTasks(taskList);
      setTotal(total);
    } finally {
      setLoading(false);
    }
  };
  const debouncedFetchTasks = useCallback(debounce(fetchTasks, 500), []);

  useEffect(() => {
    debouncedFetchTasks();
  }, [pageNum, pageSize]);

  useEffect(() => {
    if (pageNum !== 1) {
      setPageNum(1);
    } else {
      debouncedFetchTasks();
    }
  }, [search, toolId]);

  useEffect(() => {
    const params: TaskListRequestParams = {
      toolId: allToolId,
      pageNum,
      pageSize,
    };
    if (search) {
      if (search.includes("http")) {
        params.taskUrl = search;
      } else {
        params.taskName = search;
      }
    }
    if (toolId !== allToolId) {
      params.toolId = toolId;
    }
    queryParams.current = params;
  }, [search, toolId, pageSize, pageNum]);

  useEffect(() => {
    fetchToolConfig().then((res) => {
      setTools(res || []);
    });
  }, []);

  // 新建/编辑任务
  const handleModalOk = async (formValues: TaskEditItem) => {
    setSubmitLoading(true);
    try {
      const data = await createOrUpdateTask(formValues);
      if (data !== -1) {
        message.success(editingTask ? "任务更新成功" : "任务创建成功");
        taskAPI.saveTask(editingTask ? formValues : { ...formValues, taskId: data });
        setFormVisible(false);
        debouncedFetchTasks();
      } else {
        message.error(editingTask ? "任务更新失败" : "任务创建失败");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const navigate = useNavigate();

  const handleViewData = (fileName: string) => {
    navigate("/collection/data", { state: { fileName: fileName } });
  };

  return (
    <div className="task-management">
      <div className="task-management__header">
        <Title level={2} className="task-management__title">
          任务管理
        </Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          新增任务
        </Button>
      </div>
      <div className="task-management__filters" style={{ marginBottom: 16 }}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="搜索任务名称或目标URL"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          allowClear
        />
        <Select
          prefix={<FilterOutlined />}
          value={toolId}
          onChange={setToolId}
        >
          <Select.Option value={allToolId}>所有工具</Select.Option>
          {tools.map((t) => (
            <Select.Option key={t.toolId} value={t.toolId}>
              {t.toolName}
            </Select.Option>
          ))}
        </Select>
      </div>
      <Table
        dataSource={tasks}
        columns={columns}
        rowKey="key"
        className="task-management__table"
        pagination={{
          pageSize,
          current: pageNum,
          total,
          onChange: (page, size) => {
            setPageNum(page);
            setPageSize(size || defaultPageSize);
          },
        }}
        loading={loading}
      />
      {formVisible && (
        <TaskForm
          visible={formVisible}
          onCancel={() => setFormVisible(false)}
          onSubmit={handleModalOk}
          initialValues={editingTask}
          toolList={tools}
          isEdit={!!editingTask}
          submitLoading={submitLoading}
        />
      )}
      {logModalVisible && (
        <RunLogModal
          taskId={currentTaskId as number}
          visible={logModalVisible}
          onClose={() => setLogModalVisible(false)}
          onViewData={handleViewData}
        />
      )}
    </div>
  );
};

export default TaskManagement;
