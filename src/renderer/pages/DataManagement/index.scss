.data-management {
  &__header { margin-bottom: 16px; }
  &__title { font-weight: bold; }
  &__filters { margin-bottom: 16px; flex-wrap: wrap; row-gap: 8px; }
  &__file-info-row {
    display: flex;
    justify-content: flex-start;
    gap: 80px;
    margin-top: 24px;
    margin-bottom: 24px;
  }
  &__file-info-col {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
  }
  &__file-info-item {
    display: flex;
    flex-direction: column;
  }
  &__file-info-label {
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 4px;
  }
  &__file-info-value {
    color: #1d2129;
    font-weight: 500;
    word-break: break-all;
  }
  &__table-tip {
    color: #8c8c8c;
    font-size: 14px;
    margin-top: 12px;
  }

  &__table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 12px;

    .ant-table-wrapper {
      min-width: 100%;
    }

    .ant-table {
      width: 100%;
    }
  }

  &__preview-table {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      white-space: nowrap;
      min-width: 100px;
    }
  }
}