import { useCallback, useEffect, useRef, useState } from "react";
import {
  Table,
  Button,
  Input,
  Select,
  Modal,
  Space,
  message,
  DatePicker,
} from "antd";
import {
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  FilterOutlined,
  SearchOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import "./index.scss";
import {
  fetchExecuteRecordList,
  deleteExecuteRecord,
  previewExecuteFile,
  fetchToolConfig,
} from "../../api/common";
import { ToolConfigItem } from "../../types/tools";
import {
  ExecuteRecordItemType,
  ExecuteRecordListParams,
  PreviewExecuteFileResponse,
} from "../../types/file";
import { Dayjs } from "dayjs";
import { useLocation } from "react-router-dom";
import * as XLSX from "xlsx";
import { debounce } from "lodash";

const DataManagement: React.FC = () => {
  const defaultPageSize = 10;
  const allToolId = -1;
  const location = useLocation();
  const [files, setFiles] = useState<ExecuteRecordItemType[]>([]);
  const [fileName, setFileName] = useState(location.state?.fileName || "");
  const [toolId, setToolId] = useState(allToolId);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);
  const [previewFile, setPreviewFile] =
    useState<PreviewExecuteFileResponse | null>(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [tools, setTools] = useState<ToolConfigItem[]>([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewModalLoading, setPreviewModalLoading] = useState(false);
  const queryParams = useRef({
    toolId: allToolId,
    pageNum: 1,
    pageSize: defaultPageSize,
  });

  const convertTableData = (
    data: Record<string, string | string[]>[]
  ): Record<string, string>[] => {
    return data?.map((row) => {
      const newRow: Record<string, string> = {};
      Object.entries(row).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          newRow[key] = value.join("、");
        } else {
          newRow[key] = value;
        }
      });
      return newRow;
    });
  };
  const columns = [
    {
      title: "文件名",
      dataIndex: "fileName",
      key: "",
      render: (text: string) => (
        <span>
          <FileTextOutlined style={{ fontSize: 14, color: "#1890ff" }} /> {text}
        </span>
      ),
    },
    { title: "工具", dataIndex: "toolName", key: "toolName" },
    { title: "数据行数", dataIndex: "fileLine", key: "fileLine" },
    { title: "创建时间", dataIndex: "createTime", key: "createTime" },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: ExecuteRecordItemType) => (
        <Space wrap>
          <Button
            icon={<EyeOutlined />}
            type="text"
            size="small"
            onClick={() => handlePreview(Number(record.fileId))}
          />
          <Button
            icon={<DownloadOutlined />}
            type="text"
            size="small"
            loading={downloadLoading}
            onClick={() => {
              downloadS3File(record.fileId, record.fileName);
            }}
          />
          <Button
            icon={<DeleteOutlined />}
            type="text"
            size="small"
            onClick={() => handleDelete(Number(record.fileId))}
          />
        </Space>
      ),
    },
  ];

  const fetchFiles = async () => {
    setLoading(true);
    try {
      const res = await fetchExecuteRecordList(queryParams.current);
      setFiles(res?.recordList || []);
      setTotal(res?.total || 0);
    } finally {
      setLoading(false);
    }
  };
  const debouncedFetchFiles = useCallback(debounce(fetchFiles, 500), []);
  const downloadS3File = async (fileId: number, fileName: string) => {
    setDownloadLoading(true);
    try {
      const res = await previewExecuteFile(fileId);
      const tableData = res?.tableData;
      const columns = tableData?.columns;
      const data = convertTableData(tableData?.data);
      if (!columns || !data) {
        message.error("无法获取表格数据");
        return;
      }
      // 生成表头
      const header = columns.map((col: any) => col.title || col.key);
      const keys = columns.map((col: any) => col.key);
      // 生成数据
      const excelData = [header, ...data.map((row: any) => keys.map((k: string) => row[k]))];
      // 创建worksheet和workbook
      const ws = XLSX.utils.aoa_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 导出文件
      const xlsxFileName = `${fileName}.xlsx`;
      XLSX.writeFile(wb, xlsxFileName);
    } catch (error: any) {
      message.error(error?.message || "下载失败");
    } finally {
      setDownloadLoading(false);
    }
  };

  useEffect(() => {
    debouncedFetchFiles();
  }, [pageNum, pageSize]);

  useEffect(() => {
    if (pageNum !== 1) {
      setPageNum(1);
    } else {
      debouncedFetchFiles();
    }
  }, [fileName, toolId, dateRange]);

  useEffect(() => {
    const params: ExecuteRecordListParams = {
      toolId: -1,
      pageNum,
      pageSize,
    };
    if (fileName) {
      params.fileName = fileName;
    }
    if (toolId && toolId !== allToolId) {
      params.toolId = toolId;
    } else {
      params.toolId = allToolId;
    }
    if (dateRange?.[0]) {
      params.startDate = dateRange?.[0]?.format("YYYY-MM-DD");
    }
    if (dateRange?.[1]) {
      params.endDate = dateRange?.[1]?.format("YYYY-MM-DD");
    }
    queryParams.current = params;
  }, [fileName, toolId, dateRange, pageSize, pageNum]);

  useEffect(() => {
    fetchToolConfig().then((res) => {
      setTools(res || []);
    });
  }, []);

  // 删除文件
  function handleDelete(fileId: number) {
    Modal.confirm({
      title: "确定要删除此文件吗？",
      okText: "确定",
      cancelText: "取消",
      okButtonProps: { loading: submitLoading, danger: true },
      icon: <ExclamationCircleOutlined style={{ color: "#ff4d4f" }} />,
      centered: true,
      onOk: async () => {
        setSubmitLoading(true);
        try {
          const data = await deleteExecuteRecord(fileId);
          if (data !== -1) {
            message.success("文件已删除");
            debouncedFetchFiles();
          } else {
            message.error("文件删除失败");
          }
        } finally {
          setSubmitLoading(false);
        }
      },
    });
  }

  // 预览文件
  async function handlePreview(fileId: number) {
    setPreviewFile(null);
    setPreviewModalVisible(true);
    setPreviewModalLoading(true);
    try {
      const limit = 5;
      const res = await previewExecuteFile(fileId, limit);
      const columns = res?.tableData?.columns?.map((col) => ({
        ...col,
        dataIndex: col?.key,
      }));
      setPreviewFile({
        ...res,
        tableData: {
          ...res.tableData,
          columns,
          data: convertTableData(res.tableData.data),
        },
      });
    } finally {
      setPreviewModalLoading(false);
    }
  }

  return (
    <div className="data-management">
      <div className="data-management__header">
        <h2 className="data-management__title">数据管理</h2>
      </div>
      <Space className="data-management__filters" size={[16, 8]}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="搜索文件名"
          value={fileName}
          onChange={(e) => setFileName(e.target.value)}
          style={{ minWidth: 180 }}
          allowClear
          className="data-management__filters-input"
        />
        <Select
          prefix={<FilterOutlined />}
          value={toolId}
          style={{ width: 160, minWidth: 120 }}
          onChange={setToolId}
          className="data-management__filters-select"
        >
          <Select.Option value={allToolId}>所有工具</Select.Option>
          {tools.map((t) => (
            <Select.Option key={t.toolId} value={t.toolId}>
              {t.toolName}
            </Select.Option>
          ))}
        </Select>
        <DatePicker.RangePicker
          value={dateRange}
          onChange={(dates) => setDateRange(dates as [Dayjs, Dayjs] | null)}
          format="YYYY-MM-DD"
          style={{ minWidth: 220 }}
          className="data-management__filters-date"
        />
      </Space>
      <Table
        dataSource={files}
        columns={columns}
        rowKey="fileId"
        className="data-management__table"
        pagination={{
          pageSize,
          current: pageNum,
          total,
          onChange: (page, size) => {
            setPageNum(page);
            setPageSize(size || defaultPageSize);
          },
        }}
        loading={loading}
      />
      <Modal
        title={`数据预览： ${previewFile?.fileName || "--"}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={null}
        className="data-management__modal"
        width="auto"
        style={{ minWidth: 520, maxWidth: "90vw" }}
        centered={true}
        loading={previewModalLoading}
      >
        {previewFile ? (
          <div className="data-management__preview">
            <div className="data-management__file-info-row">
              <div className="data-management__file-info-col">
                <div className="data-management__file-info-item">
                  <div className="data-management__file-info-label">文件名</div>
                  <div className="data-management__file-info-value">
                    {previewFile?.fileName || "--"}
                  </div>
                </div>
                <div className="data-management__file-info-item">
                  <div className="data-management__file-info-label">
                    采集时间
                  </div>
                  <div className="data-management__file-info-value">
                    {previewFile?.collectionTime || "--"}
                  </div>
                </div>
              </div>
              <div className="data-management__file-info-col">
                <div className="data-management__file-info-item">
                  <div className="data-management__file-info-label">工具名</div>
                  <div className="data-management__file-info-value">
                    {previewFile?.toolName || '--'}
                  </div>
                </div>
                <div className="data-management__file-info-item">
                  <div className="data-management__file-info-label">
                    数据行数
                  </div>
                  <div className="data-management__file-info-value">
                    {previewFile?.collectionLine || "--"}
                  </div>
                </div>
              </div>
            </div>
            <div className="data-management__table-container">
              <Table
                dataSource={previewFile.tableData.data}
                columns={previewFile.tableData.columns}
                rowKey="key"
                pagination={false}
                className="data-management__preview-table"
                bordered
                scroll={{ x: "max-content" }}
              />
            </div>
            <div className="data-management__table-tip">
              注: 此处仅显示前5行数据，完整数据请下载文件查看
            </div>
          </div>
        ) : null}
      </Modal>
    </div>
  );
};

export default DataManagement;
