import React, { useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate, useLocation, Outlet } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import './App.scss';
import Dashboard from './pages/Dashboard';
import TaskManagement from './pages/TaskManagement';
import DataManagement from './pages/DataManagement';
import ToolManagement from './pages/ToolManagement';
import Settings from './pages/Settings';
import Layout from './components/Layout';
import MainPage from './pages/MainPage'
import { settingsAPI } from './api/settings';
import { getStorageItem, getStorageItemAsString } from './utils/storage';

// 配置 dayjs 使用中文
dayjs.locale('zh-cn');

// 路由守卫组件
const RequireAuth: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const isSettings = getStorageItemAsString('userName', '');
  if (!isSettings) {
    return <Navigate to="/settings" state={{ from: location }} replace />;
  }
  return <>{children}</>;
};

const App: React.FC = () => {
  // 应用启动时自动加载localStorage中的settings到内存
  useEffect(() => {
    const loadSettingsToMemory = async () => {
      try {
        const settings = getStorageItem('settings', null);
        if (settings) {
          await settingsAPI.saveSettings(settings);
          console.log('Settings loaded to memory successfully');
        }
      } catch (error) {
        console.error('Failed to load settings to memory:', error);
      }
    };

    loadSettingsToMemory();
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/dev" element={<MainPage />} />
          <Route path="/" element={<Navigate to="/collection/dashboard" replace />} />
          <Route path="/settings" element={<Settings />} />
          <Route
            path="/collection"
            element={
              <RequireAuth>
                <Layout>
                  <Outlet />
                </Layout>
              </RequireAuth>
            }
          >
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="tasks" element={<TaskManagement />} />
            <Route path="data" element={<DataManagement />} />
            <Route path="tools" element={<ToolManagement />} />
            <Route path="settings" element={<Settings />} />
            <Route path="*" element={<Navigate to="dashboard" replace />} />
          </Route>
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
