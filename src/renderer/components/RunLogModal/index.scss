.run-log-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.run-log-modal__title {
  display: flex;
  align-items: center;
}
.run-log-modal__close {
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
  &:hover {
    color: #ff4d4f;
  }
}
.run-log-modal__meta {
  margin-bottom: 12px;
}
.run-log-modal__meta-block {
  display: flex;
  gap: 14px;
  margin: 15px 0;
}
.run-log-modal__meta-item {
  flex: 1;
}
.run-log-modal__meta-label {
  color: #8c8c8c;
  font-weight: 400;
  line-height: 1.2;
  margin-bottom: 4px;
}
.run-log-modal__meta-value {
  color: #111;
  font-weight: 700;
  line-height: 1.3;
  word-break: break-all;
}
.run-log-modal__list-title {
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.run-log-modal__item {
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid #e6f4ea;
}
.run-log-modal__item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  font-weight: 500;
  background: none;
}
.run-log-modal__item-status {
  display: flex;
  align-items: center;
  font-weight: normal;
}
.run-log-modal__item-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px 18px 24px;
  background: none;
}
.run-log-modal__item-desc {
  color: rgb(55, 65, 81);
}
.run-log-modal__item-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.run-log-modal__item-time {
  color: #999;
  font-weight: normal;
}
.run-log-modal__item-view {
  color: rgb(37, 99, 235);
  cursor: pointer;
  font-weight: 500;
  margin-left: 16px;
}
.run-log-modal__close-btn {
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 18px;
  cursor: pointer;
  transition: background 0.2s;
  float: right;
  margin-top: 8px;
  &:hover {
    background: #40a9ff;
  }
}