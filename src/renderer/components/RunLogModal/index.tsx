import React, { useEffect, useState } from "react";
import { Empty, Modal } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  LoadingOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import "./index.scss";
import { TaskLogResponse, TaskStatus } from "../../types/task";
import { queryTaskRecord } from "../../api/common";

interface RunLogModalProps {
  taskId: number;
  visible: boolean;
  onClose: () => void;
  onViewData: (fileName: string) => void;
}

const RunLogModal: React.FC<RunLogModalProps> = ({
  taskId,
  visible,
  onClose,
  onViewData,
}) => {
  const taskStatusMap = {
    [TaskStatus.Running]: {
      icon: <LoadingOutlined style={{ color: "#0958d9" }} />,
      text: "运行中",
      backgroundColor: "#e6f4ff",
      color: "#0958d9",
    },
    [TaskStatus.Success]: {
      icon: <CheckCircleOutlined style={{ color: "rgb(21,128,61)" }} />,
      text: "成功",
      backgroundColor: "rgb(240,253,244)",
      color: "rgb(21,128,61)",
    },
    [TaskStatus.Fail]: {
      icon: <CloseCircleOutlined style={{ color: "rgb(185,28,28)" }} />,
      text: "失败",
      backgroundColor: "rgb(254,242,242)",
      color: "rgb(185,28,28)",
    },
  };
  const [taskLog, setTaskLog] = useState<TaskLogResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const fetchTaskLogData = async () => {
    try {
      setLoading(true);
      const data = await queryTaskRecord(taskId);
      setTaskLog(data);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchTaskLogData();
  }, [taskId]);
  return (
    <Modal
      open={visible}
      title={null}
      onCancel={onClose}
      footer={null}
      width={700}
      className="run-log-modal"
      loading={loading}
      centered={true}
    >
      <div className="run-log-modal__header">
        <span className="run-log-modal__title">
          <ClockCircleOutlined style={{ marginRight: 8 }} />
          运行日志：{taskLog?.taskName || "--"}
        </span>
      </div>
      {Boolean(taskLog) ? (
        <div>
        <div className="run-log-modal__meta">
          <div className="run-log-modal__meta-block">
            <div className="run-log-modal__meta-item">
              <div className="run-log-modal__meta-label">任务ID</div>
              <div className="run-log-modal__meta-value">{taskLog?.taskId}</div>
            </div>
            <div className="run-log-modal__meta-item">
              <div className="run-log-modal__meta-label">目标URL</div>
              <div
                className="run-log-modal__meta-value"
                style={{ wordBreak: "break-all" }}
              >
                {taskLog?.taskTarget?.map(item => item.url)?.join(", ")}
              </div>
            </div>
          </div>
          <div className="run-log-modal__meta-block">
            <div className="run-log-modal__meta-item">
              <div className="run-log-modal__meta-label">日期范围</div>
              <div className="run-log-modal__meta-value">
                {taskLog?.startDate} 至 {taskLog?.endDate}
              </div>
            </div>
            <div className="run-log-modal__meta-item">
              <div className="run-log-modal__meta-label">采集时间</div>
              <div className="run-log-modal__meta-value">
                {taskLog?.collectionTime?.join(", ")}
              </div>
            </div>
          </div>
        </div>
        <div className="run-log-modal__list">
          <div className="run-log-modal__list-title">
            <CalendarOutlined style={{ marginRight: 4, color: 'rgb(107,114,128)' }} />
            运行历史记录
          </div>
          {taskLog?.taskRecord?.map((item, idx) => (
            <div key={idx} className={`run-log-modal__item`}>
              <div
                className="run-log-modal__item-header"
                style={{
                  backgroundColor: taskStatusMap?.[item?.taskStatus]?.backgroundColor,
                }}
              >
                <span className={`run-log-modal__item-status`}>
                  <span style={{ marginRight: 4 }}>
                    {taskStatusMap?.[item?.taskStatus]?.icon}
                  </span>
                  <span
                    className={`run-log-modal__item-status-text`}
                    style={{ color: taskStatusMap?.[item?.taskStatus]?.color, fontWeight: 500 }}
                  >
                    {taskStatusMap?.[item?.taskStatus]?.text}
                  </span>
                </span>
                <span className="run-log-modal__item-time">
                  {item?.taskStartDate}
                </span>
              </div>
              <div className="run-log-modal__item-body">
                <span className="run-log-modal__item-desc">
                  {item?.taskSummary}
                </span>
                {item?.taskStatus === TaskStatus.Success && (
                  <a
                    className="run-log-modal__item-view"
                    onClick={() => onViewData?.(item?.fileName)}
                  >
                    <EyeOutlined /> 查看数据
                  </a>
                )}
              </div>
            </div>
            ))}
        </div>
      </div>
      ) : (
        <div className="run-log-modal__empty">
          <Empty description="暂无运行日志" />
        </div>
      )}
    </Modal>
  );
};

export default RunLogModal;
