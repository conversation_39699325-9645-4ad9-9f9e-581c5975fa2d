.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  &__header {
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px #f0f1f2;
    height: 56px;
    z-index: 100;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  &__header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  &__header-home-btn {
    font-size: 16px;
    color: #666;
    &:hover {
      color: #1677ff;
    }
  }
  &__body {
    display: flex;
    flex: 1 1 0;
    min-height: 0;
    background: #f5f6fa;
    margin-top: 56px;
    height: calc(100vh - 56px);
    overflow: hidden;
  }
  &__sider {
    background: #fff;
    box-shadow: 2px 0 8px #f0f1f2;
    min-height: 100%;
    z-index: 10;
    position: relative;
    flex-shrink: 0;
    overflow: hidden;
    margin: 24px 0 24px 24px;
    border-radius: 8px;
  }
  &__content-wrap {
    flex: 1 1 0;
    min-width: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  &__content-main {
    padding: 24px;
    min-height: 360px;
    border-radius: 8px;
    flex: 1 1 0;
    overflow: auto;
  }
  &__content {
    padding: 24px;
    background: #fff;
    min-height: 360px;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
  }
  &__trigger {
    font-size: 18px;
    margin-right: 16px;
  }
  &__header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  &__header-btn {
    font-size: 18px;
  }
  &__user {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
  }
  &__avatar {
    background: #e6f4ff;
    color: #1677ff;
  }
  &__username {
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }
}

.main-layout__drawer-close {
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 18px;
  cursor: pointer;
  z-index: 10;
  color: #222;
}

@media (max-width: 991px) {
  .main-layout__content {
    padding: 12px;
  }
  .main-layout__header {
    padding: 0 12px;
  }
  .main-layout__body {
    flex-direction: column;
    margin-top: 56px;
    height: calc(100vh - 56px);
    overflow: hidden;
  }
  .main-layout__sider {
    display: none;
    overflow: hidden;
  }
  .main-layout__menu {
    border: none !important;
  }
  .ant-drawer-close:hover {
    background: none !important;
  }
}