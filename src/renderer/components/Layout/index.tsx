import React, { useState, useEffect } from 'react';
import { Layout as AntLayout, <PERSON>u, Dropdown, Avatar, But<PERSON>, Drawer, G<PERSON>, Badge } from 'antd';
import {
  UnorderedListOutlined,
  DatabaseOutlined,
  ToolOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  MenuOutlined,
  HomeOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotifications } from '../../hooks/useNotifications';
import Notifications from '../Notifications';
import { getStorageItem } from '../../utils/storage';
import './index.scss';

const { Header, Sider, Content } = AntLayout;
const { useBreakpoint } = Grid;

const menuItems = [
  { key: '/collection/dashboard', icon: <HomeOutlined />, label: '仪表盘' },
  { key: '/collection/tasks', icon: <UnorderedListOutlined />, label: '任务管理' },
  { key: '/collection/data', icon: <DatabaseOutlined />, label: '数据管理' },
  { key: '/collection/tools', icon: <ToolOutlined />, label: '工具管理' },
  { key: '/collection/settings', icon: <SettingOutlined />, label: '设置' },
];

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [username, setUsername] = useState<string>('-');
  const { unreadCount } = useNotifications();
  const navigate = useNavigate();
  const location = useLocation();
  const screens = useBreakpoint();
  const isMobile = !screens.lg;

  // 监听 localStorage 中 userName 的变化
  useEffect(() => {
    const updateUsername = () => {
      const storedUsername = getStorageItem('userName', '-');
      setUsername(storedUsername);
    };

    // 初始化时读取一次
    updateUsername();

    // 监听 storage 事件（当其他标签页或窗口修改 localStorage 时）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'userName') {
        updateUsername();
      }
    };

    // 监听自定义事件（当前页面修改 localStorage 时）
    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === 'userName') {
        updateUsername();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('localStorageChange', handleCustomStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('localStorageChange', handleCustomStorageChange as EventListener);
    };
  }, []);

  const handleMenuClick = (e: any) => {
    navigate(e.key);
    if (isMobile) setDrawerVisible(false);
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  const handleHomeClick = () => {
    navigate('/dev');
  };

  return (
    <div className="main-layout">
      <Header className="main-layout__header">
        <div className="main-layout__header-left">
          {/* TODO: 删除回首页 */}
          <Button
            type="text"
            icon={<HomeOutlined />}
            className="main-layout__header-home-btn"
            onClick={handleHomeClick}
            title="回首页"
          />
          <div className="main-layout__header-title">酒店千里眼</div>
        </div>
        <div className="main-layout__header-right">
          <Badge count={unreadCount} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              className="main-layout__header-btn"
              onClick={handleNotificationClick}
            />
          </Badge>
          <span className="main-layout__user">
            <Avatar icon={<UserOutlined />} className="main-layout__avatar" />
            <span className="main-layout__username">{username}</span>
          </span>
          {isMobile && (
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setDrawerVisible(true)}
              className="main-layout__trigger"
            />
          )}
        </div>
      </Header>
      <div className="main-layout__body">
        {!isMobile && (
          <Sider
            className="main-layout__sider"
            collapsible
            collapsed={collapsed}
            onCollapse={setCollapsed}
            breakpoint="lg"
            trigger={null}
            width={220}
          >
            <Menu
              theme="light"
              mode="inline"
              selectedKeys={[location.pathname]}
              items={menuItems}
              onClick={handleMenuClick}
              className="main-layout__menu"
            />
          </Sider>
        )}
        <div className="main-layout__content-wrap">
          {isMobile && (
            <Drawer
              title={null}
              placement="left"
              onClose={() => setDrawerVisible(false)}
              open={drawerVisible}
              closeIcon={<CloseOutlined className="main-layout__drawer-close"/>}
            >
              <Menu
                theme="light"
                mode="inline"
                selectedKeys={[location.pathname]}
                items={menuItems}
                onClick={handleMenuClick}
                className="main-layout__menu"
              />
            </Drawer>
          )}
          <Content className="main-layout__content-main">
            {showNotifications && (
              <div style={{ marginBottom: '24px' }}>
                <Notifications onClose={() => setShowNotifications(false)} />
              </div>
            )}
            <div className="main-layout__content">{children}</div>
          </Content>
        </div>
      </div>
    </div>
  );
};

export default Layout;