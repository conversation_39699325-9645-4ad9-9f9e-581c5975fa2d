import React, { useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import NotificationContent from '../NotificationContent';
import './index.scss';

interface NotificationsProps {
  onClose?: () => void;
  embedded?: boolean;
}

const Notifications: React.FC<NotificationsProps> = ({ onClose, embedded = false }) => {

  return (
    <div className={`notifications-container ${embedded ? 'embedded' : 'standalone'}`}>
      {!embedded && (
        <div className="notifications-header">
          <h3 className="notifications-title">消息通知</h3>
          <button 
            onClick={onClose} 
            className="notifications-close-button"
          >
            <CloseOutlined />
          </button>
        </div>
      )}
      
      <NotificationContent />
    </div>
  );
};

export default Notifications; 