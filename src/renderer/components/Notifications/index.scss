.notifications-container {
  background: white;
  padding: 16px;
  
  &.embedded {
    border-radius: 0;
    box-shadow: none;
  }
  
  &.standalone {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.notifications-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.notifications-close-button {
  padding: 4px;
  border: none;
  border-radius: 50%;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  .anticon {
    font-size: 16px;
  }
}