.task-form {
  // 表单整体样式可自定义

  // 设置所有Form.Item的margin-bottom为16px
  .ant-form-item {
    margin-bottom: 16px;
  }

  // 设置Form.List的margin-bottom为16px
  .ant-form-item-list {
    margin-bottom: 16px;
  }

  &__urls-label {
    display: block;
    margin-bottom: 8px;
  }

  &__url-row {
    display: flex;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 6px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-sizing: border-box;

    .task-form__inputs {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      min-width: 0; // 防止flex子元素溢出

      .task-form__url,
      .task-form__hotel-name {
        width: 100%;
        margin-bottom: 0;

        .ant-input {
          width: 100%;
          box-sizing: border-box;
        }
      }
    }

    .task-form__remove-url {
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      height: 100%;
      flex-shrink: 0; // 防止删除按钮被压缩
    }
  }

  &__url {
    width: 100%;
    margin-right: 0;
  }
  &__url-row-item {
    display: flex;
    align-items: center;
  }
  &__url-row-item-left {
    flex: 1;
  }

  &__hotel-name {
    width: 100%;
    margin-right: 0;
  }

  &__remove-url {
    color: #ff4d4f;
    cursor: pointer;
  }

  &__add-url-btn {
    width: auto;
    margin-bottom: 16px;
    border: none;
    height: auto;
    padding-left: 0;
    .anticon {
      margin-right: 4px;
    }
  }

  &__range-picker {
    width: 100%;
  }

  &__collect-date {
    width: 100%;
  }

  &__collect-times {
    width: 100%;
  }

  &__collect-times-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__collect-times-icon {
    color: #999;
    flex-shrink: 0;
  }

  &__collect-times-select {
    flex: 1;
  }

  &__indicators {
    margin-bottom: 16px;
  }

  &__indicators-list {
    margin: 8px 0 0 0;
    padding: 0;
    list-style: none;
    color: #333;
  }

  &__indicator-item {
    line-height: 28px;
    background-color: rgb(249 250 251);
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 13px;

    &--empty {
      color: #aaa;
    }
  }

  &__indicators-desc {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
  }
  &__urls-label-row,
  &__label-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__count-tip {
    color: #999;
    font-size: 12px;
    margin-left: 0;
    flex-shrink: 0;
  }
  &__count-tip-collection-time {
    position: absolute;
    right: 0px;
    top: -25px;
  }
  &__required {
    color: #ff4d4f;
    margin-right: 2px;
    font-size: 10px;
    font-weight: bold;
  }

  &__tool-tip {
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    font-size: 12px;
    color: #52c41a;

    &-title {
      font-weight: 500;
      margin-bottom: 4px;
    }
  }

  &__collection-dimension-tip {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }
}
