// src/renderer/components/TaskForm/index.tsx
import React, { useEffect, useRef, useState } from "react";
import { Form, Input, Button, Select, DatePicker, Modal, Radio } from "antd";
import {
  PlusOutlined,
  MinusCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { ToolConfigItem } from "../../types/tools";
import "./index.scss";
import { TaskEditItem } from "../../types/task";
import dayjs from "dayjs";
import { fetchBoundToolIds, queryAllTaskNames } from "../../api/common";

// 生成采集时间枚举
function generateTimeArray() {
  const result = [];
  for (let h = 0; h < 24; h++) {
    const value = `${h.toString().padStart(2, "0")}:00`;
    let period = h <= 12 ? "上午" : "下午";
    // 这里直接用h，不做%12
    let hour12Str = (h > 12 ? h % 12 : h).toString().padStart(2, "0");
    let minStr = "00";
    const label = `${period} ${hour12Str}:${minStr}`;
    result.push({ value, label });
  }
  return result;
}

interface TaskFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  initialValues?: TaskEditItem | null;
  toolList: ToolConfigItem[];
  submitLoading?: boolean;
  isEdit?: boolean;
}

const TaskForm: React.FC<TaskFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  initialValues,
  toolList,
  isEdit = false,
  submitLoading = false,
}) => {
  const taskNameMaxLength = 30;
  const taskTargetMaxLength = 15;
  const collectionTimeMaxLength = 3;
  const collectionTimeMaxGap = 3;
  const collectionDateList = [3, 7, 14, 30];
  const [form] = Form.useForm();
  const [selectedToolId, setSelectedToolId] = useState<number | undefined>(
    initialValues?.toolId
  );
  const [filteredToolList, setFilteredToolList] = useState<ToolConfigItem[]>(
    []
  );
  const [boundToolIdsLoading, setBoundToolIdsLoading] = useState(false);
  const [collectionTime, setCollectionTime] = useState<string[]>(
    initialValues?.collectionTime || []
  );
  const [selectedToolInfo, setSelectedToolInfo] =
    useState<ToolConfigItem | null>(null);
  const timeOptions = useRef(generateTimeArray());
  const hasInitForm = useRef(false);
  const [allTaskNames, setAllTaskNames] = useState<string[]>([]);

  // 获取已绑定任务的工具ID列表
  useEffect(() => {
    if (visible && toolList.length > 0) {
      setBoundToolIdsLoading(true);
      fetchBoundToolIds()
        .then((boundToolIds) => {
          // TODO: 加回过滤
          setFilteredToolList(toolList);
          // setFilteredToolList(toolList.filter(tool =>
          //   tool.toolId === initialValues?.toolId || !boundToolIds.includes(tool.toolId)))
        })
        .finally(() => {
          setBoundToolIdsLoading(false);
        });
    }
  }, [visible, toolList, initialValues]);
  useEffect(() => {
    if (selectedToolId) {
      const toolInfo = toolList.find((t) => t.toolId === selectedToolId);
      setSelectedToolInfo(toolInfo || null);
    }
  }, [selectedToolId]);

  // 拉取所有任务名
  useEffect(() => {
    queryAllTaskNames().then((names) => setAllTaskNames(names || []));
  }, []);

  // 校验采集时间间隔
  const validateCollectionTime = (_: any, value: string[]) => {
    if (!value || value?.length < 1) {
      return Promise.reject("请至少选择1个采集时间");
    }
    if (value?.length > collectionTimeMaxLength) {
      return Promise.reject(`最多可选${collectionTimeMaxLength}个采集时间`);
    }
    // 检查间隔
    const minutes = value
      .map((t) => {
        const [h, m] = t.split(":").map(Number);
        return h * 60 + m;
      })
      .sort((a, b) => a - b);
    for (let i = 1; i < minutes.length; i++) {
      if (minutes[i] - minutes[i - 1] < 180) {
        return Promise.reject(
          `任意两次采集时间间隔不得少于${collectionTimeMaxGap}小时`
        );
      }
    }
    return Promise.resolve();
  };

  const onFinish = (values: any) => {
    const submitData = {
      ...values,
      // 确保 taskId 字段存在
      taskId: values?.taskId || initialValues?.taskId || null,
      startDate: values?.startDate
        ? values?.startDate?.format("YYYY-MM-DD")
        : undefined,
      endDate: values?.endDate ? values?.endDate?.format("YYYY-MM-DD") : undefined,
      toolName:
        toolList?.find((t) => t?.toolId === values?.toolId)?.toolName || "",
    };
    onSubmit(submitData);
  };
  if (!hasInitForm.current) {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        startDate: initialValues.startDate
          ? dayjs(initialValues.startDate, "YYYY-MM-DD")
          : null,
        endDate: initialValues.endDate
          ? dayjs(initialValues.endDate, "YYYY-MM-DD")
          : null,
      });
    } else {
      form.setFieldsValue({
        taskName: "",
        toolId: undefined,
        taskTarget: [{ url: "", name: "" }],
        startDate: null,
        endDate: null,
        collectionDate: 14,
        collectionTime: [],
        collectionMetric: [],
      });
    }
    hasInitForm.current = true;
  }

  return (
    <Modal
      open={visible}
      title={isEdit ? "编辑任务" : "新增任务"}
      onCancel={onCancel}
      onOk={() => form.submit()}
      confirmLoading={submitLoading}
      width={600}
      okText={isEdit ? "更新" : "创建"}
      cancelText="取消"
      centered={true}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        // @ts-ignore
        initialValues={initialValues}
        className="task-form"
      >
        <Form.Item
          label="任务名称"
          name="taskName"
          rules={[
            { required: true, message: "请输入任务名称" },
            {
              max: taskNameMaxLength,
              message: `任务名称不能超过${taskNameMaxLength}个字`,
            },
            {
              validator: async (_, value) => {
                if (!value) return Promise.resolve();
                // 编辑时允许与自身重名
                if (isEdit && value === initialValues?.taskName)
                  return Promise.resolve();
                if (allTaskNames.includes(value)) {
                  return Promise.reject("任务名称已存在，请更换");
                }
                return Promise.resolve();
              },
            },
          ]}
          validateFirst
        >
          <Input
            placeholder="例如：北京王府井希尔顿酒店价格监控"
            showCount
            maxLength={taskNameMaxLength}
            allowClear
          />
        </Form.Item>

        <Form.Item
          label="工具"
          name="toolId"
          rules={[{ required: true, message: "请选择工具" }]}
        >
          <Select
            placeholder="请选择工具"
            onChange={(v) => setSelectedToolId(v)}
            loading={boundToolIdsLoading}
            options={filteredToolList.map((t) => ({
              label: t.toolName,
              value: t.toolId,
            }))}
          />
        </Form.Item>

        {/* 显示已绑定工具的提示信息 */}
        {filteredToolList.length < toolList.length && (
          <div className="task-form__tool-tip">
            <div>
              {`以下工具已被其他任务绑定，无法选择：${toolList
                .filter((t) => !filteredToolList.includes(t))
                .map((t) => t.toolName)
                .join(", ")}`}
            </div>
          </div>
        )}

        <Form.List name="taskTarget">
          {(fields, { add, remove }, { errors }) => (
            <>
              <div className="task-form__urls-label-row">
                <label className="task-form__urls-label">
                  <span className="task-form__required">*</span>
                  目标酒店URL及名称（最多{taskTargetMaxLength}个）
                </label>
                <span className="task-form__count-tip">
                  已添加 {fields.length}/{taskTargetMaxLength}
                </span>
              </div>
              {fields.map((field) => (
                <div key={field.key} className="task-form__url-row">
                  <div className="task-form__inputs">
                    <Form.Item
                      {...field}
                      name={[field.name, "url"]}
                      rules={[
                        { required: true, message: "请输入酒店URL" },
                        ({ getFieldValue }) => ({
                          validator: (_, value) => {
                            // 如果selectedToolInfo存在且urlRegex不为空，则校验
                            if (selectedToolInfo && selectedToolInfo.urlRegex) {
                              if (!value) return Promise.resolve(); // 为空交给required校验
                              try {
                                const reg = new RegExp(
                                  selectedToolInfo.urlRegex
                                );
                                if (!reg.test(value)) {
                                  return Promise.reject("URL格式不符合要求");
                                }
                              } catch (e) {
                                // 正则有误时跳过校验
                                return Promise.resolve();
                              }
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      className="task-form__url"
                    >
                      <Input
                        placeholder="例如：https://example.com/hotel/123"
                        allowClear
                      />
                    </Form.Item>
                    <Form.Item
                      {...field}
                      name={[field.name, "name"]}
                      rules={[{ required: true, message: "请输入酒店名称" }]}
                      className="task-form__hotel-name"
                    >
                      <Input
                        placeholder="请输入酒店名称，例如：北京王府井希尔顿酒店"
                        allowClear
                      />
                    </Form.Item>
                  </div>
                  <div className="task-form__remove-url">
                    {fields.length > 1 && (
                      <MinusCircleOutlined
                        onClick={() => remove(field.name)}
                        style={{
                          color: "red",
                          fontSize: 18,
                          cursor: "pointer",
                        }}
                      />
                    )}
                  </div>
                </div>
              ))}
              <Form.ErrorList errors={errors} />
              <Button
                type="link"
                onClick={() => add({ url: "", name: "" })}
                icon={<PlusOutlined />}
                disabled={fields.length >= taskTargetMaxLength}
                className="task-form__add-url-btn"
              >
                添加更多URL
              </Button>
            </>
          )}
        </Form.List>

        <Form.Item label="任务起止时间" required style={{ marginBottom: 16 }}>
          <Form.Item
            name="startDate"
            noStyle
            rules={[{ required: true, message: "请选择开始时间" }]}
          >
            <DatePicker
              style={{ width: 180 }}
              placeholder="开始时间"
              format="YYYY-MM-DD"
              disabledDate={(current) =>
                current && current < dayjs().startOf("day")
              }
            />
          </Form.Item>
          <span style={{ margin: "0 8px" }}>至</span>
          <Form.Item
            name="endDate"
            noStyle
            dependencies={["startDate"]}
            rules={[
              { required: true, message: "请选择结束时间" },
              () => ({
                validator(_, value) {
                  const start = form?.getFieldValue("startDate");
                  if (!value || !start) return Promise.resolve();
                  // 只在两者都是 dayjs 对象时校验
                  if (
                    typeof value.isBefore === "function" &&
                    typeof start.isBefore === "function"
                  ) {
                    if (value.isBefore(start, "day")) {
                      return Promise.reject("结束时间不能早于开始时间");
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 180 }}
              placeholder="结束时间"
              format="YYYY-MM-DD"
              disabledDate={(current) =>
                current && current < dayjs().startOf("day")
              }
            />
          </Form.Item>
          <div className="task-form__collection-dimension-tip">
            <InfoCircleOutlined className="task-form__collection-dimension-icon" />
            <span>采集维度：按天采集（暂不支持修改）</span>
          </div>
        </Form.Item>

        <Form.Item
          label="采集日期"
          name="collectionDate"
          rules={[{ required: true, message: "请选择采集日期" }]}
        >
          <Radio.Group className="task-form__collect-date-radio-group">
            {collectionDateList.map((days) => (
              <Radio key={days} value={days}>
                近{days}天
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label={
            <div className="task-form__label-row">
              <span>
                <span className="task-form__required">*</span>
                采集时间（每天最多{collectionTimeMaxLength}次， 间隔不低于
                {collectionTimeMaxGap}小时）
              </span>
            </div>
          }
          name="collectionTime"
          rules={[{ validator: validateCollectionTime }]}
        >
          <span className="task-form__count-tip task-form__count-tip-collection-time">
            已添加 {collectionTime.length}/{collectionTimeMaxLength}
          </span>
          <div className="task-form__collect-times-wrapper">
            <ClockCircleOutlined className="task-form__collect-times-icon" />
            <Select
              value={collectionTime}
              mode="multiple"
              placeholder="请选择采集时间"
              options={timeOptions.current}
              maxTagCount={collectionTimeMaxLength}
              allowClear
              className="task-form__collect-times-select"
              onChange={(value) => {
                setCollectionTime(value);
                form.setFieldsValue({ collectionTime: value });
              }}
            />
          </div>
        </Form.Item>

        <div className="task-form__indicators">
          <span className="task-form__indicators-title">
            采集指标（当前工具可采集的指标）
          </span>
          <ul className="task-form__indicators-list">
            {selectedToolInfo?.toolMetric?.length &&
            selectedToolInfo?.toolMetric?.length > 0 ? (
              selectedToolInfo?.toolMetric?.map((ind, idx) => (
                <li key={idx} className="task-form__indicator-item">
                  {ind.label}
                </li>
              ))
            ) : (
              <li className="task-form__indicator-item task-form__indicator-item--empty">
                请先选择工具
              </li>
            )}
          </ul>
        </div>
      </Form>
    </Modal>
  );
};

export default TaskForm;
