.notification-content {
  max-height: 240px;
  overflow-y: auto;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  // 通知样式
  &__notifications {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__notification-item {
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: background-color 0.2s;

    &--unread {
      border-left: 4px solid #ef4444;
    }

    &--read {
      border-left: 4px solid #d1d5db;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }

  &__notification-message {
    font-size: 14px;
    display: block;
    margin-bottom: 4px;

    &--unread {
      color: #374151;
    }

    &--read {
      color: #6b7280;
    }
  }

  &__notification-time {
    font-size: 12px;
  }

  &__no-notifications {
    text-align: center;
    padding: 32px 0;
    color: #6b7280;
  }

  // 公告样式
  &__announcements {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__announcement-item {
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  &__announcement-title {
    display: block;
    margin-bottom: 4px;
  }

  &__announcement-desc {
    font-size: 14px;
    color: #6b7280;
    display: block;
    margin-bottom: 4px;
  }

  &__announcement-date {
    font-size: 12px;
  }

  // 最近任务状态样式
  &__recent-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: none;
  }

  &__recent-tasks {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: #f9fafb;
    border-radius: 8px;
  }

  &__task-name {
    display: block;
    margin-bottom: 4px;
  }

  &__task-target {
    font-size: 14px;
  }

  &__task-status {
    border-radius: 12px;
    font-size: 12px;
    padding: 2px 8px;
  }
}