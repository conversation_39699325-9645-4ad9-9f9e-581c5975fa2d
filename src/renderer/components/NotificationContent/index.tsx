import React from "react";
import { Typography } from "antd";
import { useNotifications } from "../../hooks/useNotifications";
import "./index.scss";

const { Text } = Typography;

const NotificationContent: React.FC = () => {
  const { notificationList, markAsRead, loading } = useNotifications();

  const handleMarkAsRead = (id: number) => {
    markAsRead(id);
  };

  if (loading) {
    return <div className="notification-content">加载中...</div>;
  }

  return (
    <div className="notification-content">
      {notificationList.length > 0 ? (
        <div className="dashboard__notifications">
          {notificationList.map((notification) => (
            <div
              key={notification.id}
              className={`dashboard__notification-item ${
                notification.read
                  ? "dashboard__notification-item--read"
                  : "dashboard__notification-item--unread"
              }`}
              onClick={() => handleMarkAsRead(notification.id)}
            >
              <Text
                className={`dashboard__notification-message ${
                  notification.read
                    ? "dashboard__notification-message--read"
                    : "dashboard__notification-message--unread"
                }`}
              >
                {notification.message}
              </Text>
              <Text type="secondary" className="dashboard__notification-time">
                {notification.time}
              </Text>
            </div>
          ))}
        </div>
      ) : (
        <div className="dashboard__no-notifications">暂无通知</div>
      )}
    </div>
  );
};

export default NotificationContent;
