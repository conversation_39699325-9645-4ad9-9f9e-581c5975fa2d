import { ipc<PERSON>enderer } from 'electron';

/**
 * 渲染进程中提供的存储API
 */
export const settingsAPI = {
  /**
   * 保存设置
   * @param settings 要保存的设置
   */
  async saveSettings(settings: Record<string, any>) {
    await ipcRenderer.invoke('save-settings', settings);
  },

  /**
   * 获取所有设置
   */
  async getAllSettings(): Promise<Record<string, any>> {
    return await ipcRenderer.invoke('get-settings');
  },

  async clearSettings() {
    await ipcRenderer.invoke('clear-settings');
  }

};