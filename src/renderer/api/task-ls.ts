import { ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * 渲染进程中提供的存储API
 */
export const taskAPI = {
  /**
   * 保存任务
   * @param task 要保存的任务
   */
  async saveTask(task: Record<string, any>) {
    await ipcRenderer.invoke(`save-task-${task.taskId}`, task);
  },

  /**
   * 获取任务
   */
  async getTask(taskId: number): Promise<Record<string, any>> {
    return await ipcRenderer.invoke(`get-task-${taskId}`);
  },

  /**
   * 删除任务
   * @param taskId 要删除的任务ID
   */
  async clearTask(taskId: number) {
    await ipcRenderer.invoke(`clear-task-${taskId}`);
  }
};