import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { settingsAPI } from './settings';

// 声明 window 对象类型

export interface ApiResponse<T = any> {
  code: number;
  data: T;
  error: {
    msg?: string;
    [key: string]: any;
  };
  traceId?: number | string;
}

interface RequestOptions extends AxiosRequestConfig {
  intercept?: boolean;
}
// 修改为新的 API 服务器地址
const BASE_URL = 'http://localhost:3001'

const getUserName = async () => {
  const settings = await settingsAPI.getAllSettings()
  return settings?.userName
}

export async function apiRequest(config: RequestOptions) {
  const { intercept, ...axiosConfig } = config;
  // 自动补全 baseURL
  axiosConfig.baseURL = axiosConfig.baseURL || BASE_URL;

  // 从localStorage获取userName并添加到请求头
  // @ts-ignore
  const userName = await getUserName()
  if (userName) {
    axiosConfig.headers = {
      ...axiosConfig.headers,
      'User-Name': userName
    };
  }
  const defaultErrorMsg = '接口请求失败';

  try {
    const response = await axios(axiosConfig);
    if (!response || !response.data) {
      throw new Error(defaultErrorMsg)
    }
    if (intercept) {
      return response.data;
    }
    if (response.data.code === 10000) {
      return response.data.data;
    } else {
      const errorMsg = response.data.error?.msg;
      throw new Error(errorMsg)
    }
  } catch (error: any) {
    const errorMsg = error?.message || defaultErrorMsg;
    message.error(errorMsg)
    throw new Error(errorMsg)
  }
}