import {
  ExecuteRecordListParams,
  ExecuteRecordListResponse,
  PreviewExecuteFileResponse,
} from "../types/file";
import type { ToolConfigItem } from "../types/tools";
import type { SettingsModel } from "../types/settings";
import { apiRequest } from "./apiRequest";
import { TaskEditItem, TaskListRequestParams, TaskListResponse, TaskLogResponse } from "../types/task";
import { DashboardDataModel } from "../types/dashboard";
import { ipcRenderer } from "electron";

// 查询任务列表
export function fetchTaskList(params: TaskListRequestParams): Promise<TaskListResponse> {
  return apiRequest({
    url: "/api/clairvoyant/queryTaskList",
    method: "GET",
    params,
  });
}

// 新建或编辑任务
export function createOrUpdateTask(data: TaskEditItem): Promise<number> {
  return apiRequest({
    url: "/api/clairvoyant/createOrUpdateTask",
    method: "POST",
    data,
  });
}

// 删除任务
export function deleteTask(taskId: number): Promise<number> {
  return apiRequest({
    url: "/api/clairvoyant/deleteTask",
    method: "POST",
    data: { taskId },
  });
}

// 工具管理 - 获取工具列表s
export function fetchToolConfig(): Promise<ToolConfigItem[]> {
  return apiRequest({
    url: "/api/clairvoyant/queryToolConfig",
    method: "GET",
  });
}

// 仪表盘 - 获取核心指标
export function fetchCoreMetric(): Promise<DashboardDataModel> {
  return apiRequest({
    url: "/api/clairvoyant/queryCoreMetric",
    method: "GET",
  });
}

// 数据管理 - 获取执行记录列表
export function fetchExecuteRecordList(
  params: ExecuteRecordListParams
): Promise<ExecuteRecordListResponse> {
  return apiRequest({
    url: '/api/clairvoyant/queryAllExecuteRecord',
    method: 'GET',
    params,
  });
}

// 数据管理 - 删除执行记录
export function deleteExecuteRecord(fileId: number): Promise<number> {
  return apiRequest({
    url: "/api/clairvoyant/deleteExecuteRecord",
    method: "POST",
    data: { fileId },
  });
}

// 数据管理 - 预览文件内容
export function previewExecuteFile(
  fileId: number,
  limit?: number
): Promise<PreviewExecuteFileResponse> {
  return apiRequest({
    url: "/api/clairvoyant/previewExecuteFile",
    method: "GET",
    params: { fileId, limit },
  });
}


// 保存腾讯云存储设置
export function saveTecentCosConfig(data: SettingsModel) {
  return apiRequest({
    url: "/api/clairvoyant/saveTecentCosConfig",
    method: "POST",
    data: { ...data },
  });
}

// 获取已绑定任务的工具ID列表
export function fetchBoundToolIds(): Promise<number[]> {
  return apiRequest({
    url: "/api/clairvoyant/queryAllToolId",
    method: "GET",
  });
}
/**
 * 查询任务执行记录
 * @param taskId 任务id
 * @returns 任务执行记录
 */
export function queryTaskRecord(taskId: number) {
  return apiRequest({
    url: '/api/clairvoyant/queryTaskRecord',
    method: 'GET',
    params: { taskId },
  });
}

/**
 * 查询所有工具id
 * @returns 所有工具id
 */
export function queryAllToolId(): Promise<number[]> {
  return apiRequest({
    url: '/api/clairvoyant/queryAllToolId',
    method: 'GET',
  });
}

/**
 * 保存腾讯云配置
 * @param data 腾讯云配置
 * @returns 保存结果
 */
export function saveTencentCosConfig(data: SettingsModel) {
  return apiRequest({
    url: '/api/clairvoyant/saveTencentCosConfig',
    method: 'POST',
    data,
  })
}

/**
 * 获取腾讯云配置
 */
export function getTencentCosConfig() {
  return apiRequest({
    url: '/api/clairvoyant/getTencentCosConfig',
    method: 'GET',
  })
}

// 获取本地消息缓存
export const getCachedMessages = async () => {
  return await ipcRenderer.invoke('get-cached-messages');
};

// 获取消息提醒
export const getNotification = async (
  params?: { startDate?: string; endDate?: string }
) => {
  return apiRequest({
    url: '/api/clairvoyant/getNotification',
    method: 'GET',
    params,
  })
};
/** 获取同设备在线所有任务名 */
export const queryAllTaskNames = async (): Promise<string[]> => {
  return apiRequest({
    url: '/api/clairvoyant/queryAllTaskNames',
    method: 'GET',
  })
}
