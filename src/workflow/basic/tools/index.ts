import { xcToolEmit } from '../../xc'

/** 工具配置 */
export const TOOL_ID_CONFIG = {
  xc: {
    id: 1,
    emitter: xcToolEmit,
  },
} as const

/** 获取工具触发器 */
export const getToolConfigEmitter = (toolId: number) => {
  const keyList = Object.keys(TOOL_ID_CONFIG) as (keyof typeof TOOL_ID_CONFIG)[]
  const toolKey = keyList.filter((key) => TOOL_ID_CONFIG[key].id === toolId)[0]

  return TOOL_ID_CONFIG[toolKey].emitter
}
