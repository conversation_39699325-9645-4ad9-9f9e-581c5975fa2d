import { spawn } from 'child_process'
import * as fs from 'fs/promises'
import * as path from 'path'
import * as os from 'os'
import * as https from 'https'
import * as http from 'http'

/**
 * 操作结果类型定义
 */
export interface OperationResult {
  /** 状态码：200 表示成功，500 表示失败 */
  status: 200 | 500
  /** 操作结果消息 */
  message: string
  /** 可选的额外数据 */
  data?: {
    filePath?: string
    fileSize?: number
    downloadTime?: number
    chromePath?: string
    platform?: string
  }
}

/**
 * 下载JSON文件并在浏览器中打开
 * @param jsonUrl JSON文件的URL地址
 * @returns Promise<OperationResult> 操作结果
 */
export async function downloadAndOpenJsonFile(
  jsonUrl: string
): Promise<OperationResult> {
  const startTime = Date.now()

  try {
    console.log('开始下载JSON文件:', jsonUrl)

    // 输入验证
    if (!jsonUrl || typeof jsonUrl !== 'string') {
      return {
        status: 500,
        message: '无效的URL参数',
      }
    }

    if (!jsonUrl.startsWith('http://') && !jsonUrl.startsWith('https://')) {
      return {
        status: 500,
        message: '无效的URL格式，必须以 http:// 或 https:// 开头',
      }
    }

    // 1. 获取桌面路径（兼容 Windows 和 Mac）
    const desktopPath = getDesktopPath()
    const fileName = '千里眼数据采集工作流.automa.ue.json'
    const filePath = path.join(desktopPath, fileName)

    console.log('目标文件路径:', filePath)

    // 2. 下载JSON数据
    const jsonData = await downloadJson(jsonUrl)

    if (!jsonData) {
      return {
        status: 500,
        message: '下载的JSON数据为空',
      }
    }

    // 3. 保存到桌面（自动覆盖现有文件）
    await fs.writeFile(filePath, jsonData, 'utf-8')
    console.log('✅ JSON文件已保存到桌面:', fileName)

    // 4. 用浏览器打开文件（使用备用方法确保成功）
    await openFileWithFallback(filePath)
    console.log('✅ 已在浏览器中打开文件')

    const downloadTime = Date.now() - startTime

    // 获取 Chrome 信息（仅用于信息显示，不影响成功结果）
    const chromeInfo = await checkChromeInstallation()

    return {
      status: 200,
      message: `JSON文件下载并用 Chrome 浏览器打开成功，文件名：${fileName}`,
      data: {
        filePath,
        fileSize: Buffer.byteLength(jsonData, 'utf-8'),
        downloadTime,
        chromePath: chromeInfo.data?.chromePath,
        platform: chromeInfo.data?.platform,
      },
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    console.error('❌ 下载或打开JSON文件失败:', error)

    return {
      status: 500,
      message: `操作失败：${errorMessage}`,
    }
  }
}

/**
 * 获取桌面路径
 * @returns 桌面文件夹的绝对路径
 */
function getDesktopPath(): string {
  const homeDir = os.homedir()
  const platform = os.platform()

  let desktopPath: string

  if (platform === 'win32') {
    // Windows 系统
    desktopPath = path.join(homeDir, 'Desktop')
  } else if (platform === 'darwin') {
    // macOS 系统
    desktopPath = path.join(homeDir, 'Desktop')
  } else {
    // Linux 或其他系统
    desktopPath = path.join(homeDir, 'Desktop')
  }

  return desktopPath
}

/**
 * 下载JSON数据
 * @param url JSON文件的URL地址
 * @returns Promise<string> JSON字符串
 */
function downloadJson(url: string): Promise<string> {
  console.log('开始下载 JSON 文件:', url)

  return new Promise((resolve, reject) => {
    // 判断是 HTTP 还是 HTTPS
    const isHttps = url.startsWith('https')
    const client = isHttps ? https : http

    const options = {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        Accept: 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
      },
    }

    const req = client.get(url, options, (res) => {
      // 检查响应状态码
      if (res.statusCode !== 200) {
        reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`))
        return
      }

      let data = ''

      // 处理 gzip 压缩
      let stream = res
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib')
        stream = res.pipe(zlib.createGunzip())
      }

      stream.on('data', (chunk) => {
        data += chunk
      })

      stream.on('end', () => {
        try {
          // 验证是否为有效的JSON
          JSON.parse(data)
          resolve(data)
        } catch (error) {
          reject(new Error(`无效的JSON格式: ${(error as Error).message}`))
        }
      })
    })

    req.on('error', (error) => {
      reject(new Error(`下载失败: ${error.message}`))
    })

    // 设置超时
    req.setTimeout(30000, () => {
      req.destroy()
      reject(new Error('下载超时（30秒）'))
    })
  })
}

/**
 * 使用 Chrome 浏览器打开文件
 * @param filePath 文件的绝对路径
 * @returns Promise<void>
 */
function openFileInBrowser(filePath: string): Promise<void> {
  return new Promise(async (resolve, reject) => {
    // 首先检查文件是否存在
    try {
      await fs.access(filePath)
    } catch (error) {
      reject(new Error(`文件不存在: ${filePath}`))
      return
    }

    const platform = os.platform()
    let command: string
    let args: string[]

    // 转换为 file:// URL 格式
    let fileUrl = `file://${filePath.replace(/\\/g, '/')}`

    console.log('当前 JSON 文件的路径为：', fileUrl)

    if (platform === 'darwin') {
      // NOTE: Mac 上不支持在启动的时候使用 file 头
      fileUrl = fileUrl.replace('file://', '')
      // macOS - 专门使用 Chrome 打开
      command = 'open'
      args = ['-a', 'Google Chrome', fileUrl]
    } else if (platform === 'win32') {
      // Windows - 专门启动 Chrome
      command = 'cmd'
      args = ['/c', 'start', 'chrome', `"${fileUrl}"`]
    } else {
      // Linux - 使用 google-chrome 命令
      command = 'google-chrome'
      args = [fileUrl]
    }

    console.log('执行命令:', command, args.join(' '))

    const child = spawn(command, args, {
      detached: true,
      stdio: 'pipe', // 改为 pipe 以便捕获错误信息
    })

    child.on('error', (error) => {
      console.error('spawn 错误:', error)
      reject(new Error(`无法启动浏览器: ${error.message}`))
    })

    // 使用标志避免多次 resolve
    let resolved = false

    child.on('close', (code) => {
      if (!resolved) {
        if (code === 0) {
          console.log('浏览器进程正常退出')
        } else {
          console.log(`浏览器进程退出，退出码: ${code}`)
        }
        // 不在这里 resolve，因为进程退出不代表浏览器启动失败
      }
    })

    child.on('spawn', () => {
      if (!resolved) {
        resolved = true
        child.unref() // 允许父进程退出
        console.log('浏览器启动进程已创建')
        // 给一点时间让浏览器启动
        setTimeout(() => {
          resolve()
        }, 500)
      }
    })

    // 设置超时，防止无限等待
    setTimeout(() => {
      if (!resolved) {
        resolved = true
        console.log('浏览器启动超时，但假设成功')
        resolve()
      }
    }, 3000)
  })
}

/**
 * 尝试用多种方法启动 Chrome 浏览器打开文件
 * @param filePath 文件路径
 * @returns Promise<void>
 */
async function openFileWithFallback(filePath: string): Promise<void> {
  const methods = [
    () => openFileInBrowser(filePath), // 方法1: 系统命令启动 Chrome
    () => openWithSystemDefault(filePath), // 方法2: 系统关联启动 Chrome
    () => openWithBrowserPath(filePath), // 方法3: 直接调用 Chrome 可执行文件
  ]

  let lastError: Error | null = null

  for (let i = 0; i < methods.length; i++) {
    try {
      console.log(`尝试方法 ${i + 1}: 启动 Chrome 浏览器`)
      await methods[i]()
      console.log('✅ Chrome 浏览器成功启动并打开文件')
      return
    } catch (error) {
      lastError = error as Error
      console.log(`❌ 方法 ${i + 1} 失败: ${error}`)
      if (i < methods.length - 1) {
        console.log(`尝试下一种方法...`)
      }
    }
  }

  throw lastError || new Error('所有启动 Chrome 浏览器的方法都失败了')
}

/**
 * 使用系统方式启动 Chrome 浏览器
 * @param filePath 文件路径
 * @returns Promise<void>
 */
function openWithSystemDefault(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const platform = os.platform()
    let command: string
    let args: string[]

    // 构建完整的 file URL
    let fileUrl = `file://${filePath.replace(/\\/g, '/')}`

    if (platform === 'darwin') {
      // NOTE: Mac 上不支持在启动的时候使用 file 头
      fileUrl = fileUrl.replace('file://', '')
      // macOS - 使用系统的 Chrome 关联
      command = 'open'
      args = ['-a', `"Google Chrome"`, `"${fileUrl}"`]
    } else if (platform === 'win32') {
      // Windows - 尝试通过注册表找到的 Chrome
      command = 'cmd'
      args = ['/c', 'start', 'chrome', `"${fileUrl}"`]
    } else {
      // Linux - 使用系统安装的 Chrome
      command = 'google-chrome'
      args = ['--new-tab', fileUrl]
    }

    console.log('尝试系统方式启动 Chrome:', command, args.join(' '))

    const child = spawn(command, args, {
      detached: true,
      stdio: 'ignore',
    })

    child.on('error', reject)
    child.on('spawn', () => {
      child.unref()
      resolve()
    })
  })
}

/**
 * 直接查找并启动 Chrome 浏览器可执行文件
 * @param filePath 文件路径
 * @returns Promise<void>
 */
function openWithBrowserPath(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const platform = os.platform()
    let chromePaths: string[] = []

    let fileUrl = `file://${filePath.replace(/\\/g, '/')}`

    // 只查找 Chrome 浏览器的路径
    if (platform === 'darwin') {
      // NOTE: Mac 上不支持在启动的时候使用 file 头
      fileUrl = fileUrl.replace('file://', '')
      chromePaths = [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary',
      ]
    } else if (platform === 'win32') {
      chromePaths = [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        process.env.LOCALAPPDATA + '\\Google\\Chrome\\Application\\chrome.exe',
        process.env.PROGRAMFILES + '\\Google\\Chrome\\Application\\chrome.exe',
      ]
    } else {
      chromePaths = [
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium-browser',
        '/usr/local/bin/google-chrome',
        '/opt/google/chrome/google-chrome',
      ]
    }

    // 尝试第一个可用的 Chrome 浏览器
    const tryChrome = async (index: number): Promise<void> => {
      if (index >= chromePaths.length) {
        reject(new Error('未找到可用的 Chrome 浏览器'))
        return
      }

      const chromePath = chromePaths[index]

      try {
        await fs.access(chromePath)
        console.log('尝试直接启动 Chrome:', chromePath)

        // Chrome 特定的启动参数
        const chromeArgs = [
          '--new-tab',
          '--disable-web-security',
          '--allow-file-access-from-files',
          fileUrl,
        ]

        const child = spawn(chromePath, chromeArgs, {
          detached: true,
          stdio: 'ignore',
        })

        child.on('error', () => {
          tryChrome(index + 1)
        })

        child.on('spawn', () => {
          child.unref()
          resolve()
        })
      } catch (error) {
        tryChrome(index + 1)
      }
    }

    tryChrome(0)
  })
}

/**
 * 检查文件是否存在
 * @param filePath 文件路径
 * @returns Promise<boolean>
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath)
    return true
  } catch {
    return false
  }
}

/**
 * 获取文件信息
 * @param filePath 文件路径
 * @returns Promise<{exists: boolean, size?: number, modified?: Date}>
 */
export async function getFileInfo(filePath: string): Promise<{
  exists: boolean
  size?: number
  modified?: Date
}> {
  try {
    const stats = await fs.stat(filePath)
    return {
      exists: true,
      size: stats.size,
      modified: stats.mtime,
    }
  } catch {
    return { exists: false }
  }
}

/**
 * 删除桌面上的JSON文件
 * @returns Promise<boolean> 删除成功返回true
 */
export async function deleteJsonFileFromDesktop(): Promise<boolean> {
  try {
    const desktopPath = getDesktopPath()
    const fileName = '千里眼数据采集工作流.json'
    const filePath = path.join(desktopPath, fileName)

    const exists = await fileExists(filePath)
    if (exists) {
      await fs.unlink(filePath)
      console.log('✅ 已删除桌面文件:', fileName)
      return true
    } else {
      console.log('ℹ️ 桌面文件不存在:', fileName)
      return false
    }
  } catch (error) {
    console.error('❌ 删除桌面文件失败:', error)
    throw error
  }
}

/**
 * 检查系统中是否安装了 Chrome 浏览器
 * @returns Promise<OperationResult>
 */
export async function checkChromeInstallation(): Promise<OperationResult> {
  try {
    const platform = os.platform()
    let chromePaths: string[] = []

    // 根据操作系统获取 Chrome 可能的安装路径
    if (platform === 'darwin') {
      chromePaths = [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary',
      ]
    } else if (platform === 'win32') {
      chromePaths = [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        process.env.LOCALAPPDATA + '\\Google\\Chrome\\Application\\chrome.exe',
        process.env.PROGRAMFILES + '\\Google\\Chrome\\Application\\chrome.exe',
      ]
    } else {
      chromePaths = [
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium-browser',
        '/usr/local/bin/google-chrome',
        '/opt/google/chrome/google-chrome',
      ]
    }

    // 检查是否存在可用的 Chrome 安装
    for (const chromePath of chromePaths) {
      try {
        await fs.access(chromePath)
        return {
          status: 200,
          message: `找到 Chrome 浏览器: ${chromePath}`,
          data: {
            chromePath,
            platform,
          },
        }
      } catch (error) {
        // 继续检查下一个路径
      }
    }

    return {
      status: 500,
      message: `未在系统中找到 Chrome 浏览器。请确保已安装 Google Chrome 浏览器。`,
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    return {
      status: 500,
      message: `检查 Chrome 安装时出错: ${errorMessage}`,
    }
  }
}

// 打开 Chrome 扩展
export const openChromeUrl = (httpPath: string) => {
  // 根据操作系统确定 Chrome 路径
  let chromePath: string
  const platform = process.platform

  if (platform === 'darwin') {
    // macOS
    chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
  } else if (platform === 'win32') {
    // Windows
    chromePath = 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
  } else {
    // Linux
    chromePath = 'google-chrome'
  }

  try {
    const chromeProcess = spawn(chromePath, [httpPath], {
      detached: true,
      stdio: 'ignore',
    })

    chromeProcess.unref()
    console.log('Chrome 浏览器已启动，正在访问扩展页面')

    return { status: 200, message: 'chrome 启动成功' }
  } catch (error) {
    console.error('启动 Chrome 失败:', error)
    return {
      status: 501,
      message: `chrome 启动失败，请检查 Chrome 的安装路径是否为：${chromePath}`,
    }
  }
}
