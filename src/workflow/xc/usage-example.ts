const {
  processAutomaWorkflow,
  getWorkflowStats,
} = require('./generate-workflow')

// 示例使用函数
async function example() {
  // 示例数据
  const workflowUrl = 'https://example.com/workflow-template.json' // 替换为实际的工作流模板 URL
  const hotelData = [
    {
      name: '黄龙饭店',
      date: '2025-07-16',
      url: 'https://hotels.ctrip.com/hotels/373059.html?cityid=17&checkIn=2025-07-16&checkOut=2025-07-17&crn=1&adult=2&children=0',
    },
    {
      name: '黄龙饭店',
      date: '2025-07-17',
      url: 'https://hotels.ctrip.com/hotels/373059.html?cityid=17&checkIn=2025-07-17&checkOut=2025-07-18&crn=1&adult=2&children=0',
    },
    {
      name: '黄龙饭店',
      date: '2025-07-18',
      url: 'https://hotels.ctrip.com/hotels/373059.html?cityid=17&checkIn=2025-07-18&checkOut=2025-07-19&crn=1&adult=2&children=0',
    },
  ]

  try {
    console.log('开始处理工作流...')
    const result = await processAutomaWorkflow(workflowUrl, hotelData)

    if (result.success) {
      console.log('✅ 工作流处理成功!')
      console.log('统计信息:', result.statistics)

      // 获取更详细的工作流统计
      const stats = getWorkflowStats(result.workflow)
      console.log('工作流详细统计:', stats)

      // 保存处理后的工作流 (可选)
      const fs = require('fs')
      const outputPath = './processed-workflow.json'
      fs.writeFileSync(outputPath, JSON.stringify(result.workflow, null, 2))
      console.log(`已保存处理后的工作流到: ${outputPath}`)
    } else {
      console.log('❌ 工作流处理失败:', result.error)
    }
  } catch (error) {
    console.error('发生错误:', error)
  }
}

// 使用本地文件测试的示例
async function testWithLocalFile() {
  const path = require('path')
  const fs = require('fs')

  // 读取本地的工作流模板
  const localWorkflowPath = path.join(__dirname, 'xc-workflow.json')

  if (fs.existsSync(localWorkflowPath)) {
    // 创建一个临时的 HTTP 服务器来模拟在线 JSON
    const http = require('http')
    const workflowContent = fs.readFileSync(localWorkflowPath, 'utf8')

    const server = http.createServer((req, res) => {
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(workflowContent)
    })

    server.listen(3001, async () => {
      console.log('临时服务器启动在 http://localhost:3001')

      const hotelData = [
        {
          name: '黄龙饭店',
          date: '2025-07-16',
          url: 'https://hotels.ctrip.com/hotels/373059.html?cityid=17&checkIn=2025-07-16&checkOut=2025-07-17&crn=1&adult=2&children=0',
        },
        {
          name: '黄龙饭店',
          date: '2025-07-20',
          url: 'https://hotels.ctrip.com/hotels/373059.html?cityid=17&checkIn=2025-07-20&checkOut=2025-07-21&crn=1&adult=2&children=0',
        },
      ]

      try {
        const result = await processAutomaWorkflow(
          'http://localhost:3001',
          hotelData
        )
        console.log('测试结果:', result.success ? '✅ 成功' : '❌ 失败')
        if (result.success) {
          console.log('统计信息:', result.statistics)
        } else {
          console.log('错误:', result.error)
        }
      } catch (error) {
        console.error('测试失败:', error)
      } finally {
        server.close()
        console.log('临时服务器已关闭')
      }
    })
  } else {
    console.log('本地工作流文件不存在:', localWorkflowPath)
  }
}

// 错误处理示例
async function errorHandlingExample() {
  console.log('\n=== 错误处理示例 ===')

  // 测试无效 URL
  let result = await processAutomaWorkflow('invalid-url', [])
  console.log('无效 URL 测试:', result.success ? '意外成功' : '✅ 正确捕获错误')

  // 测试空数组
  result = await processAutomaWorkflow('https://example.com/test.json', [])
  console.log('空数组测试:', result.success ? '意外成功' : '✅ 正确捕获错误')

  // 测试无效数据结构
  result = await processAutomaWorkflow('https://example.com/test.json', [
    { name: 'test' },
  ]) // 缺少 date 和 url
  console.log(
    '无效数据结构测试:',
    result.success ? '意外成功' : '✅ 正确捕获错误'
  )
}

// 导出函数供其他文件使用
module.exports = {
  example,
  testWithLocalFile,
  errorHandlingExample,
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  console.log('运行工作流处理器示例...\n')

  // 运行错误处理示例
  errorHandlingExample().then(() => {
    console.log('\n错误处理示例完成')

    // 如果存在本地文件，则运行本地测试
    testWithLocalFile()
  })
}
