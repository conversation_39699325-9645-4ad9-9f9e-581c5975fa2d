import type { XcWorkflowConfig, XcHotelConfig } from './type'
import { validateWorkflowStructure, validateHotelConfig } from './utils'

/**
 * 处理 Automa 工作流模板，将酒店数据注入到指定节点
 * @param {string} workflowUrl - 工作流模板的 JSON URL
 * @param {Array} hotelData - 酒店数据数组
 * @returns {Promise<Object>} 返回处理后的工作流对象
 */
export const generateXcAutomaWorkflow = (payload: {
  workflowData: XcWorkflowConfig
  hotelConfig: XcHotelConfig[]
}) => {
  try {
    const { workflowData, hotelConfig } = payload

    // 验证工作流结构
    const workflowValidation = validateWorkflowStructure(workflowData)
    if (!workflowValidation.valid) {
      throw new Error(`采集工作流生成异常: ${workflowValidation.error}`)
    }

    // 校验酒店配置数据
    let formatHotelConfig: XcHotelConfig[] = hotelConfig

    const validation = validateHotelConfig(formatHotelConfig)
    if (validation.valid) {
    } else {
      throw new Error(`采集工作流生成异常: ${validation.error}`)
    }

    // 2. 查找并修改 loop-data 节点
    const loopDataNode =
      workflowData.drawflow.nodes.find(
        (node) =>
          node.label === 'loop-data' &&
          /hotelConfig/.test(node.data?.description || '')
      ) || null

    if (!loopDataNode) {
      throw new Error('采集工作流生成异常: 未找到 loop-data 节点')
    }

    // 将酒店数据写入 loopData
    loopDataNode.data.loopData = JSON.stringify(hotelConfig)
    console.log('已更新酒店详情的 loop-data 节点')

    return {
      status: 0,
      message: '工作流处理完成',
      workflow: workflowData,
    }
  } catch (error: any) {
    console.error('工作流处理失败:', error.message)
    return {
      status: 1,
      message: error.message,
      workflow: null,
    }
  }
}
