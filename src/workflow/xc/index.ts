import type { XcHotelConfig, XcWorkflowConfig } from './type'

import dayjs from 'dayjs'
import { downloadAndOpenJsonFile } from '../basic/chrome'
import { generateXcAutomaWorkflow } from './generate-workflow'
import { CosStorage } from '../../main/cos-storage'
import { genStandardHotelConfig } from './utils'

// TODO: 这里的入参要换成 toolConfig 类型
/**
 * 携程采集工具触发
 * @param payload
 * @returns
 */
export const xcToolEmit = async (
  workflow: XcWorkflowConfig,
  customConfig: {
    insertData: XcHotelConfig[]
    days: number
  }
) => {
  try {
    const workflowLoopData = genStandardHotelConfig(
      customConfig.insertData,
      customConfig.days
    )
    if (workflowLoopData.length === 0) {
      console.error('携程采集工作流生成异常，工作流的插入数据为空')
      throw new Error('携程采集工作流生成异常')
    }

    const {
      status: genStatus,
      workflow: genWorkflow,
      message: genMessage,
    } = generateXcAutomaWorkflow({
      workflowData: workflow,
      hotelConfig: workflowLoopData,
    })

    if (genStatus !== 0) {
      console.error('携程采集工作流生成异常，错误信息为：', genMessage)
      throw new Error(genMessage)
    }

    // 获取工作流配置
    const cosStorage = new CosStorage()
    const customFileName = `xc_${dayjs().format(
      'YYYY-MM-DD_HH:mm:ss'
    )}.automa.upd_exe.json`

    try {
      await cosStorage.uploadJson(genWorkflow, customFileName)
    } catch (error: any) {
      console.error('携程采集工作流上传 json 失败，错误信息为：', error)
      throw new Error('携程采集工作流上传 json 失败')
    }

    const staticUrl = cosStorage.getStaticWebsiteUrl(customFileName)

    if (
      !staticUrl ||
      !/\.automa\.(upd|exe|ins|upd_exe|ins_exe)\.json$/.test(staticUrl)
    ) {
      console.error(
        '携程采集工作流获取 JSON 地址失败，当前获取到的 JSON 地址为：',
        staticUrl
      )
      throw new Error('携程采集工作流获取 JSON 地址失败')
    }
    console.log('本次启动的携程工作流 JSON 地址为：', staticUrl)

    const openChromeResult = await downloadAndOpenJsonFile(staticUrl)

    if (openChromeResult.status !== 200) {
      console.error(
        '携程采集工作流打开 Chrome 扩展失败，错误信息为：',
        openChromeResult.message
      )
      throw new Error(openChromeResult.message)
    }

    return {
      status: 200,
      message: '携程采集工具执行成功',
    }
  } catch (error: any) {
    console.error('携程采集工具执行失败，错误信息为：', error.message)
    return {
      status: 500,
      message: error.message,
    }
  }
}
