import type { XcHotelConfig, XcWorkflowConfig, ValidUtilResult } from './type'
import dayjs from 'dayjs'

/**
 * 生成带 checkIn 和 checkOut 参数的新 URL 列表（自动覆盖原有参数，支持 hash）
 * @param {string[]} urlList - 原始 URL 数组
 * @param {number} days - 未来天数
 * @returns {string[]} - 生成的新 URL 列表
 */
export const genStandardHotelConfig = (
  hotelConfig: XcHotelConfig[],
  days: number
) => {
  if (!Array.isArray(hotelConfig) || typeof days !== 'number' || days < 1) {
    return []
  }

  const dummyBase = 'http://dummy-base'
  const result = [] as XcHotelConfig[]

  for (let i = 0; i < days; i++) {
    // 计算 checkIn 和 checkOut 日期
    const checkInDate = dayjs().add(i, 'day').format('YYYY-MM-DD')
    const checkOutDate = dayjs(checkInDate).add(1, 'day').format('YYYY-MM-DD')

    for (const config of hotelConfig) {
      let url
      try {
        // 兼容相对路径
        url = new URL(config.url, dummyBase)
      } catch (e) {
        // 如果 URL 解析失败，跳过
        continue
      }

      // 设置/覆盖参数
      url.searchParams.set('checkIn', checkInDate)
      url.searchParams.set('checkOut', checkOutDate)

      // 还原为原始协议（如果是相对路径则去掉 dummy-base）
      let finalUrl = config.url.startsWith('http')
        ? url.toString()
        : url.pathname + url.search + url.hash

      // 如果是加了 dummy-base 的，去掉前缀
      if (finalUrl.startsWith(dummyBase)) {
        finalUrl = finalUrl.replace(dummyBase, '')
      }

      config.url = finalUrl

      result.push(config)
    }
  }

  return result
}

/**
 * 验证酒店配置数据
 * @param {string} workflowUrl - 工作流 URL
 * @param {Array} hotelData - 酒店数据
 * @returns {Object} 验证结果
 */
export const validateHotelConfig = (hotelConfig: XcHotelConfig[]) => {
  // 验证酒店数据
  if (!Array.isArray(hotelConfig)) {
    return { valid: false, error: 'xc 酒店配置数据必须是数组' }
  }

  if (hotelConfig.length === 0) {
    return { valid: false, error: 'xc 酒店配置数据数组不能为空' }
  }

  // 验证酒店数据结构
  for (let i = 0; i < hotelConfig.length; i++) {
    const hotel = hotelConfig[i]
    if (typeof hotel?.name !== 'string') {
      return {
        valid: false,
        error: `xc 酒店配置数据第 ${i + 1} 项缺少 name 字段或格式不正确`,
      }
    }

    if (typeof hotel?.url !== 'string') {
      return {
        valid: false,
        error: `xc 酒店配置数据第 ${i + 1} 项缺少 url 字段或格式不正确`,
      }
    }

    // 验证 URL 格式
    try {
      new URL(hotel.url)
    } catch (error) {
      return {
        valid: false,
        error: `xc 酒店配置数据第 ${i + 1} 项的 url 格式不正确`,
      }
    }
  }

  return { valid: true }
}

/**
 * 验证工作流结构
 * @param {Object} workflow - 工作流对象
 * @returns {Object} 验证结果
 */
export const validateWorkflowStructure = (
  workflow: XcWorkflowConfig
): ValidUtilResult => {
  // 检查基本结构
  if (!workflow || !Array.isArray(workflow.drawflow?.nodes)) {
    return { valid: false, error: '工作流缺少有效的 drawflow.nodes 结构' }
  }

  return { valid: true }
}
