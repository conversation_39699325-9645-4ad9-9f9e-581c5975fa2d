export interface XcToolConfig {
  /** 工具名称 */
  name: string
  /** 工具 ID */
  toolId: number
}

/** 工作流配置类型 */
export interface XcWorkflowConfig {
  /** 工作流名称 */
  name: string
  /** 执行工作流 */
  drawflow: {
    /** 节点 */
    nodes: {
      /** 节点类型 */
      label: string
      /** 节点描述 */
      description: string
      /** 节点 ID */
      id: string
      /** 一般为 BlockBasic */
      type: string
      /** 工作流配置 */
      data: Record<string, any>
    }[]
  }
  /** 工作流设置 */
  settings: {
    /** 工作流 publicId */
    publicId: string
    /** 错误处理 */
    onError: string
  }
  /** 全局数据，stringify 后 */
  globalData: string
  /** 工作流描述 */
  description: string
}

/** 携程酒店配置数据 */
export interface XcHotelConfig {
  /** 酒店名称 */
  name: string
  /** 酒店详情地址 */
  url: string
}

/** 验证工具结果 */
export interface ValidUtilResult {
  valid: boolean
  error?: string
}
