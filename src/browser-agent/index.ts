import {
  DefaultBrowserOperator,
  RemoteBrowserOperator,
  SearchEngine,
} from '@ui-tars/operator-browser';
import { G<PERSON>Agent, GUIAgentData, type GUIAgentConfig } from '@ui-tars/sdk';
import { UITarsModelConfig } from '@ui-tars/sdk/core';
import { type Conversation } from '@ui-tars/shared/types';
import { filterAndTransformWithMap, getAuthHeader, getRemoteVLMProvider, getRemoteVLMResponseApiSupport, getSpByModelVersion, markClickPosition } from './util';
import { ArkConfig, ConversationWithSoM, FREE_MODEL_BASE_URL, getModelVersion, SearchEngineForSettings, StatusEnum } from './config';

// const conv = [];

const stateSetting = {
    conv: [],
    sessionHistoryMessages: [],
} as any;


async function getConfig() {
    // const useResponsesApi = await getRemoteVLMResponseApiSupport();
    // const modelConfig = {
    //     baseURL: FREE_MODEL_BASE_URL,
    //     apiKey: '',
    //     model: '',
    //     useResponsesApi,
    // };
    // const modelAuthHdrs = await getAuthHeader();
    // const modelVersion = await getRemoteVLMProvider();

    const useResponsesApi = false;
    const modelConfig = {
        baseURL: ArkConfig.vlmBaseUrl,
        apiKey: ArkConfig.vlmApiKey,
        model: ArkConfig.vlmModelName,
        useResponsesApi,
    };
    const modelAuthHdrs = {};
    const modelVersion = getModelVersion(ArkConfig.modelVersion);

    return {
        modelConfig,
        modelAuthHdrs,
        modelVersion,
    };
}


async function handleData(params: { data: GUIAgentData }, cb = Function.prototype) {
    // const lastConv = getState().messages[getState().messages.length - 1];
    const lastConv = stateSetting.conv[stateSetting.conv.length - 1];
    const { data } = params;
    const { status, conversations, ...restUserData } = data;
    console.info('[onGUIAgentData] status', status, conversations.length);
    // add SoM to conversations
    const conversationsWithSoM: ConversationWithSoM[] = await Promise.all(
      conversations.map(async (conv) => {
        const { screenshotContext, predictionParsed } = conv;
        const tgItem: Set<string> = new Set();
        if (
          lastConv?.screenshotBase64 &&
          screenshotContext?.size &&
          predictionParsed
        ) {
          const screenshotBase64WithElementMarker = await markClickPosition({
            screenshotContext,
            base64: lastConv?.screenshotBase64,
            parsed: predictionParsed,
          }).catch((e) => {
            console.error('[markClickPosition error]:', e);
            return '';
          });
          const roundMessage = predictionParsed.filter(e => {
            if (tgItem.has(e.thought)) {
                return false;
            }
            tgItem.add(e.thought);
            return true;
          }).map(p => p.thought).join('\n');
          cb(roundMessage, conversations);
          return {
            ...conv,
            screenshotBase64WithElementMarker,
          };
        }
        return conv;
      }),
    ).catch((e) => {
      console.error('[conversationsWithSoM error]:', e);
      return conversations;
    });

    const {
      screenshotBase64,
      predictionParsed,
      screenshotContext,
      screenshotBase64WithElementMarker,
      ...rest
    } = conversationsWithSoM?.[conversationsWithSoM.length - 1] || {};
    console.info(
      '[onGUIAgentData] ======data======\n',
      predictionParsed,
      screenshotContext,
      rest,
      status,
      '\n========',
    );

    Object.assign(stateSetting, {
      status,
      restUserData,
      conv: [...(stateSetting.conv || []), ...conversationsWithSoM],
    });
  }


async function createGuiAgent(callback: (status: 'error' | 'success' | 'finish', message: string, history: ConversationWithSoM[]) => void) {
    const { modelConfig, modelAuthHdrs, modelVersion } = await getConfig();
    const systemPrompt = getSpByModelVersion(modelVersion, 'zh', 'browser');
    const abortController = new AbortController();
    // await checkBrowserAvailability();
    //   const { browserAvailable } = getState();
    //   if (!browserAvailable) {
    //     setState({
    //       ...getState(),
    //       status: StatusEnum.ERROR,
    //       errorMsg:
    //         'Browser is not available. Please install Chrome and try again.',
    //     });
    //     return;
    //   }

    const operator = await DefaultBrowserOperator.getInstance(
        false, //highlight
        false, //showActionInfo
        false, //showWaterflow
        false, //isCallUser
        SearchEngineForSettings.BAIDU as unknown as SearchEngine,
        // getState().status === StatusEnum.CALL_USER,
        // getLocalBrowserSearchEngine(settings.searchEngineForBrowser),
    );
    const operatorType = 'browser';
    const guiAgent = new GUIAgent({
        model: modelConfig,
        systemPrompt: systemPrompt,
        logger: console,
        signal: abortController?.signal,
        operator: operator,
        onData: params => handleData(params, (msg: string, context: []) => callback('success', msg, context)),
        onError: (params) => {
            const { error } = params;
            // console.error('[onGUIAgentError]', error);
            Object.assign(stateSetting, {
                status: StatusEnum.ERROR,
                errorMsg: JSON.stringify({
                    status: error?.status,
                    message: error?.message,
                    stack: error?.stack,
                }),
            });
            callback('error', error.message, []);
        },
        retry: {
            model: {
                maxRetries: 5,
            },
            screenshot: {
                maxRetries: 5,
            },
            execute: {
                maxRetries: 3,
            },
        },
        maxLoopCount: 100,
        loopIntervalInMs: 1000,
        uiTarsVersion: modelVersion,
  });
  return guiAgent;
    // afterAgentRun('Local Browser Operator');
}

export async function runAgent(
    instructions: string,
    history: ConversationWithSoM[],
    progressCallback: (status: 'error' | 'success' | 'finish', message: string, history: ConversationWithSoM[]) => void,
) {
    const initialMessages: Conversation[] = [
      {
        from: 'human',
        value: instructions,
        timing: { start: Date.now(), end: Date.now(), cost: 0 },
      },
    ];
    // TODO: handle Permissions
    stateSetting.conv = initialMessages;
    stateSetting.sessionHistoryMessages = filterAndTransformWithMap(history);
    const guiAgent = await createGuiAgent(progressCallback);
    //   return guiAgent;

    // // GUIAgentManager.getInstance().setAgent(guiAgent);
    // // UTIOService.getInstance().sendInstruction(instructions);

    // const { sessionHistoryMessages } = stateSetting;

    // beforeAgentRun('Local Browser Operator');

    const startTime = Date.now();

    // // 可以先 Planning 
    // const planningList = await reasoningModel.invoke({
    //     conversations: [
    //         {
    //         role: 'user',
    //         content: 'buy a ticket from beijing to shanghai',
    //         }
    //     ]
    //     })
    //     /**
    //      * [
    //      *  'open chrome',
    //      *  'open trip.com',
    //      *  'click "search" button',
    //      *  'select "beijing" in "from" input',
    //      *  'select "shanghai" in "to" input',
    //      *  'click "search" button',
    //      * ]
    //      */

    //     for (const planning of planningList) {
    //         await guiAgent.run(planning);
    //     }

    await guiAgent
        .run(instructions, stateSetting.sessionHistoryMessages, {})
        .catch((e) => {
            console.error('[runAgentLoop error]', e);
            Object.assign(stateSetting, {
                status: StatusEnum.ERROR,
                errorMsg: e.message,
            });
        }).then(result => {
            console.log('Result::', result);
        });

    console.info('[runAgent Total cost]: ', (Date.now() - startTime) / 1000, 's');
    return stateSetting.conv[stateSetting.conv.length - 1]?.predictionParsed?.[0]?.action_inputs?.content ?? '执行完成！'
}
// getRemoteVLMResponseApiSupport
// runAgent(
//     '帮我找下国务院 2025 放假安排，转换为按日维度的 JSON 列表，返回给我',
//     [],
//     (type: 'success' | 'error' | "finish", msg: string) => {
//         switch (type) {
//             case 'success': 
//                 console.log(`🤔 ${msg}`);
//                 break;
//             case 'error':
//                 console.log(`❌ ${msg}`);
//                 break;
//             default:
//                 break;
//         }
//     },
// );
