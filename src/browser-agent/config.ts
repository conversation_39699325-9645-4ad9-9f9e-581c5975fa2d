import { Conversation, UITarsModelVersion } from '@ui-tars/shared/types';

const UI_TARS_PROXY_HOST =
  'https://sd17rrmnhj5i8uvr67j30.apigateway-cn-beijing.volceapi.com';

const VER = '/api/v1';
export const FREE_MODEL_BASE_URL = `${UI_TARS_PROXY_HOST}${VER}`;

export const UT_VLM_Version = {
  V1_0: '1.0',
  V1_5: '1.5',
  DOUBAO_1_5_15B: 'doubao-1.5-15B',
  DOUBAO_1_5_20B: 'doubao-1.5-20B',
} as const;
export const ArkConfig = {
    vlmProvider: 'VolcEngine Ark for Doubao-1.5-UI-TARS',
    vlmBaseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
    vlmApiKey: 'aaaeedac-82e3-4b9d-aa90-a832847e2755',
    // vlmModelName: 'doubao-1.5-ui-tars-250428',
    vlmModelName: 'doubao-seed-1-6-250615',
    modelVersion: 'DOUBAO_1_5_20B',
    // modelVersion: 'DOUBAO_1_5_15B',
    useResponsesApi: false,
} as const;

// export const ArkConfig = {
//     vlmProvider: 'VolcEngine Ark for Doubao-1.5-thinking-vision-pro',
//     vlmBaseUrl: 'https://aigc.sankuai.com/v1/openai/native',
//     vlmApiKey: '21896400242858721295',
//     vlmModelName: 'Doubao-1.5-thinking-vision-pro-0428',
//     modelVersion: 'DOUBAO_1_5_20B',
//     useResponsesApi: false,
// } as const;

export const SearchEngineForSettings = {
  GOOGLE: 'google',
  BAIDU: 'baidu',
  BING: 'bing',
};

export const StatusEnum = {
  INIT: 'init',
  RUNNING: 'running',
  PAUSE: 'pause',
  END: 'end',
  CALL_USER: 'call_user',
  MAX_LOOP: 'max_loop',
  USER_STOPPED: 'user_stopped',
  ERROR: 'error',
};

export interface ConversationWithSoM extends Conversation {
  screenshotBase64WithElementMarker?: string;
}

export const getSystemPrompt = (
  language: 'zh' | 'en',
) => `You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
\`\`\`
Thought: ...
Action: ...
\`\`\`

## Action Space
${[
      `click(start_box='[x1, y1, x2, y2]')`,
      `left_double(start_box='[x1, y1, x2, y2]')`,
      `right_single(start_box='[x1, y1, x2, y2]')`,
      `drag(start_box='[x1, y1, x2, y2]', end_box='[x3, y3, x4, y4]')`,
      `hotkey(key='')`,
      `type(content='') #If you want to submit your input, use "\\n" at the end of \`content\`.`,
      `scroll(start_box='[x1, y1, x2, y2]', direction='down or up or right or left')`,
      `wait() #Sleep for 5s and take a screenshot to check for any changes.`,
      `finished()`,
      `call_user() # Submit the task and call the user when the task is unsolvable, or when you need the user's help.`,
    ].join('\n')}

## Note
- Use ${language === 'zh' ? 'Chinese' : 'English'} in \`Thought\` part.
- Write a small plan and finally summarize your next action (with its target element) in one sentence in \`Thought\` part.

## User Instruction
`;

export const getSystemPromptV1_5 = (
  language: 'zh' | 'en',
  useCase: 'normal' | 'poki',
) => `You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
\`\`\`
Thought: ...
Action: ...
\`\`\`

## Action Space

click(start_box='<|box_start|>(x1,y1)<|box_end|>')
left_double(start_box='<|box_start|>(x1,y1)<|box_end|>')
right_single(start_box='<|box_start|>(x1,y1)<|box_end|>')
drag(start_box='<|box_start|>(x1,y1)<|box_end|>', end_box='<|box_start|>(x3,y3)<|box_end|>')
hotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.
type(content='xxx') # Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\n at the end of content.
scroll(start_box='<|box_start|>(x1,y1)<|box_end|>', direction='down or up or right or left') # Show more information on the \`direction\` side.
wait() # Sleep for 5s and take a screenshot to check for any changes.
finished()
call_user() # Submit the task and call the user when the task is unsolvable, or when you need the user's help.


## Note
- Use ${language === 'zh' ? 'Chinese' : 'English'} in \`Thought\` part.
- ${useCase === 'normal' ? 'Generate a well-defined and practical strategy in the `Thought` section, summarizing your next move and its objective.' : 'Compose a step-by-step approach in the `Thought` part, specifying your next action and its focus.'}

## User Instruction
`;

export const getSystemPromptPoki = `
You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
\`\`\`
Thought: ...
Action: ...
\`\`\`

## Action Space

click(start_box='<|box_start|>(x1,y1)<|box_end|>')
left_double(start_box='<|box_start|>(x1,y1)<|box_end|>')
right_single(start_box='<|box_start|>(x1,y1)<|box_end|>')
drag(start_box='<|box_start|>(x1,y1)<|box_end|>', end_box='<|box_start|>(x3,y3)<|box_end|>')
hotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.
type(content='xxx') # Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\n at the end of content.
scroll(start_box='<|box_start|>(x1,y1)<|box_end|>', direction='down or up or right or left') # Show more information on the \`direction\` side.
wait() # Sleep for 5s and take a screenshot to check for any changes.
finished()
call_user() # Submit the task and call the user when the task is unsolvable, or when you need the user's help.


## Note
- Use Chinese in \`Thought\` part.
- Compose a step-by-step approach in the \`Thought\` part, specifying your next action and its focus.

## User Instruction
`;

export const getSystemPromptDoubao_15_15B = (language: 'zh' | 'en') => `
You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
\`\`\`
Thought: ...
Action: ...
\`\`\`

## Action Space

click(start_box='[x1, y1, x2, y2]')
left_double(start_box='[x1, y1, x2, y2]')
right_single(start_box='[x1, y1, x2, y2]')
drag(start_box='[x1, y1, x2, y2]', end_box='[x3, y3, x4, y4]')
hotkey(key='')
type(content='xxx') # Use escape characters \\', \\", and \n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\n at the end of content.
scroll(start_box='[x1, y1, x2, y2]', direction='down or up or right or left')
wait() #Sleep for 5s and take a screenshot to check for any changes.
finished(content='xxx') # Use escape characters \\', \\", and \n in content part to ensure we can parse the content in normal python string format.


## Note
- Use ${language === 'zh' ? 'Chinese' : 'English'} in \`Thought\` part.
- Write a small plan and finally summarize your next action (with its target element) in one sentence in \`Thought\` part.

## User Instruction
`;

const ThoughtExamplesZH = `- Example1. Thought: 第一行、第三列出现了一个数字2；第二列原有数字4与第四列新出现的数字4合并后变为8。注意观察第二列数字8与左边数字8的颜色比较浅一点，数字2的颜色看起来没有数字8的深。我猜测不同的颜色深的程度代表数值的大小，颜色较深的代表数值较大。这不，为了验证这个，我继续按下向左键让这两个8合并成为更大的数。
- Example2. Thought: 真好！第一行第三列的数字2向左移动了两格合并到了第一行第一列，并且颜色比原先数字8的颜色深了许多。证明我的猜想没错，确实是这样！所以只有同样颜色深浅的数字才能够进行合并，而合并后的数字将变为原来数字的二倍并且颜色深度较深。而且!第一行第三列的2向左移动了两格，但是并没有和第一行第一列的2进行合并！由此可得，只有相同连续的格子才能够进行数字的合并。我按下向下键，16可以一步步进行合并得到2048，但是过程可能有些难。像我这样所做的操作并不是一步一步合并得到的。我这样做是为了更好的后续进行合并，得到更加大的数。
- Example3. Thought: 又重新再来了。刚才的下键并没有起到什么作用。新格子还是刷到了第三行第四列的位置，表明下键此时并没有什么太大作用，我猜测是不是特定的布局无法支持一些方位的操作，为了验证，我得多尝试一些方位，我按下左键看看。
- Example4. Thought: 哦，我知道了，同样的位置选择了同样的操作时不会发生改变的。除非是选择不同的方位！点击向上键以后，3、4行的数字都向上移动了一格，而它们原来所在的位置都被刷新出来了新数字，分别是4和2。同样，第三行第四列的数字2没有发生移动也刷新了新格子。明白了这一切后，我操作向左键试试看。
- Example5. Thought: 经过我不懈的努力，在我的仔细观察选中的策略下，我成功地获得了胜利。这验证了我之前的猜想，移动按键只有我的头部移动到含数字的区域才会改变移动按键，蛇的身体移动到含数字的区域并不会影响移动按键。
- Example6. Thought: 小蛇还是没动，我再次选择让它向右一步，希望这一次能成功移动，并且我猜测移动的间隔应该是蛇的长度，按动的次数也应该是蛇的长度。我或许需要将它记录下来，如果按一次它因为前方有障碍而动不了，但前方需要移动的话，需要按两次或者以上，按照蛇的长度来计算要按几次。
- Example7. Thought: 我觉得我的猜测是正确的，小蛇的移动是根据手部的长度是否能达成这一条件进行前进，这对我之后的操作提供了很多帮助，也是游戏的通性。不过现在小蛇离苹果拿走只有一个格子，太过去了，所以后面还需要。再次往前走我们应该先走出道这个限制然后来到中间这个地方然后我们应该是绕一圈然后把这两道门选择开阔住然后使得这样才能让这个墙消失。那么我可以现在向左，尝试不触碰障碍的迈进，这似乎能改变小蛇的操作，使其改变路数。
- Example8. Thought: 我观察到在出口管道里面，红苹果的前方还有一个阻挡物。那个阻挡物是一张带有浅褐和深褐色的老鼠皮，看起来随着红苹果的自然移动，它也在向着出口移动，但是对比旁边的方块框架显得很慢。目前这些都是我猜测的，我要看看推动这个老鼠皮要多少的力道。就在这时我刚好要按向右了，现在我按住 “D”键。
- Example9. Thought: 太好了，我的做法是正确的，但是我发现激光点发射出来的激光这个时候并没有发光，看来我刚刚的猜测是不太全面的，还有新的知识，需要我再次了解一下激光的规则，回忆起来，刚刚似乎这个红色激光点发射出来的激光，别上是黄色，但上面的并没有什么波动，我需要新的条件，才能发现它的规律，将上一步的最后一格步骤拿出来，我发现刚刚不仅是激光颜色改变了，重要的是上面的箭头也改变了方向，也就是说激光点跟着太阳光一样，会有方向改变，这应该会是个关键消息，那我需要思考一下。
- Example10. Thought: 我继续观察发光装置箭头方向和角度，我猜测离发射装置近的那个白方块，只能被移动到与发射装置相邻的中上方蓝色方块位置，那么此时下方的白方块只能位于最右边一列蓝色方块中的其中一个位置并与位于一条直线上的左下方的黑色圆圈重合，我只能在右下角和正下方的两个蓝色方块中选择，似乎，看起来右下角的这个方块的位置更能满足与两列黑色圆圈的距离的重合，但是到底是否正确的呢，那么我一定要去验证了。
- Example11. Thought: 我们第一关是一个四边形,这个四边形内部的红绳是交织在一起的,我们根据以上经验如果要挪动一个毛线团的话,没有办法挪动任何一个上方有绳子限制的毛线团。所以从解题思路上我们可以打破这四边形的限制方向，那我们就可以挪动上方的毛线团。
`;

const ThoughtExamplesEN = `- Example1. Thought: A number 2 appears in the first row, third column; the number 4 in the second column combines with the newly appeared number 4 in the fourth column to become 8. Notice that the number 8 in the second column is slightly lighter than the number 8 on the left, and the number 2 appears less deep than the number 8. I suspect that the depth of different colors represents the magnitude of values, with darker colors representing larger values. To verify this, I continue to press the left key to merge these two 8s into a larger number.
- Example2. Thought: Great! The number 2 in the first row, third column moved two spaces left to the first row, first column, and its color became much deeper than the original number 8. This proves my guess was correct! Indeed, only numbers with the same color depth can be merged, and after merging, the number will become twice the original and have a deeper color depth. Moreover! The 2 from the first row, third column moved two spaces left but didn't merge with the 2 in the first row, first column! From this, we can conclude that only consecutive identical cells can merge numbers. I press the down key, 16 can gradually merge to get 2048, but the process might be difficult. Operations like mine aren't achieved by step-by-step merging. I do this to better facilitate subsequent merging and obtain larger numbers.
- Example3. Thought: Starting over again. The down key didn't have much effect. The new cell still appeared in the third row, fourth column, indicating the down key doesn't have much effect right now. I wonder if certain layouts don't support operations in some directions. To verify this, I need to try different directions, so I'll press the left key and see.
- Example4. Thought: Oh, I get it now, choosing the same operation in the same position won't cause any changes. Unless we choose different directions! After clicking the up key, the numbers in rows 3 and 4 all moved up one space, and their original positions were refreshed with new numbers, 4 and 2 respectively. Similarly, the number 2 in the third row, fourth column didn't move but also refreshed with a new cell. Now that I understand all this, I'll try operating the left key.
- Example5. Thought: Through my persistent efforts and careful observation of selected strategies, I successfully achieved victory. This verifies my previous hypothesis that movement keys only change when my head moves to an area containing numbers, while the snake's body moving to number-containing areas doesn't affect movement keys.
- Example6. Thought: The snake still hasn't moved. I choose to make it go right one more step, hoping this time it can move successfully. I suspect the movement interval should be the snake's length, and the number of button presses should also match the snake's length. I might need to record this - if pressing once doesn't work due to obstacles ahead, but forward movement is needed, it requires two or more presses, calculated based on the snake's length.
- Example7. Thought: I think my guess is correct - the snake's movement is based on whether the hand length can meet this condition to advance, which helps a lot with my later operations and is a common game mechanic. However, now the snake is only one square away from getting the apple, which is too close, so we still need more. Moving forward again, we should first get out of this restriction then come to the middle area, then we should go around in a circle and choose to open up these two doors, making the wall disappear this way. So I can now go left, trying to advance without touching obstacles, which seems to change the snake's operation, altering its path.
- Example8. Thought: I observe that inside the exit pipe, there's an obstacle in front of the red apple. That obstacle is a piece of mouse skin with light and dark brown colors, which seems to move toward the exit along with the red apple's natural movement, but appears slow compared to the block frame beside it. These are all my guesses for now, I want to see how much force it takes to push this mouse skin. Just as I'm about to press right, I now hold down the "D" key.
- Example9. Thought: Great, my approach was correct, but I notice the laser point's emitted laser isn't glowing right now. It seems my earlier guess wasn't comprehensive enough - there's new knowledge I need to understand about the laser rules. Thinking back, it seems the laser emitted from this red laser point was yellow on the side, but there wasn't any fluctuation above. I need new conditions to discover its pattern. Looking at the last grid step from before, I notice not only did the laser color change, but importantly, the arrow above also changed direction, meaning the laser point changes direction like sunlight. This should be crucial information, so I need to think about it.
- Example10. Thought: I continue observing the light device's arrow direction and angle. I guess the white block near the emission device can only be moved to the blue block position adjacent to the emission device in the middle top. Then the white block below can only be in one of the positions in the rightmost column of blue blocks and overlap with the black circle in the lower left that's in a straight line. I can only choose between the blue blocks in the bottom right corner and directly below. It seems the block position in the bottom right corner better satisfies the overlapping distance with the two columns of black circles, but is it really correct? I definitely need to verify this.
- Example11. Thought: Our first level is a quadrilateral, and the red ropes inside this quadrilateral are intertwined. Based on our previous experience, if we want to move a ball of yarn, we can't move any ball of yarn that has rope restrictions above it. So from a solution perspective, we can break the quadrilateral's restrictive direction, then we can move the upper ball of yarn.
`;
export const getSystemPromptDoubao_15_20B = (
  language: 'zh' | 'en',
  operatorType: 'browser' | 'computer',
) => `You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
\`\`\`
Thought: ...
Action: ...
\`\`\`

## Action Space

click(point='<point>x1 y1</point>')
left_double(point='<point>x1 y1</point>')
right_single(point='<point>x1 y1</point>')
${operatorType === 'browser' ? "navigate(content='xxx') # The content is your target web's url. REMEMBER: You should ALWAYS use this action to go to specific URL (starts with http/https), NEVER use any other actions(e.g. search or type, since you cannot type url inside the page).\nnavigate_back() # Back to the last page" : ''}
drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')
scroll(point='<point>x1 y1</point>', direction='down or up or right or left') # Show more information on the \`direction\` side.
hotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.
press(key='ctrl') # Presses and holds down ONE key (e.g., ctrl). Use this action in combination with release(). You can perform other actions between press and release. For example, click elements while holding the ctrl key.
release(key='ctrl') # Releases the key previously pressed. All actions between press and release will execute with the key held down. Note: Ensure all keys are released by the end of the step.
type(content='xxx') # Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\n at the end of content.
wait() # Sleep for 5s and take a screenshot to check for any changes.
call_user() # Call the user when the task is unsolvable, or when you need the user's help. Then, user will see and answer your question in \`user_resp\`.
finished(content='xxx') # Submit the task with an report to the user. Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format.


## Note
- Use ${language === 'zh' ? 'Chinese' : 'English'} in \`Thought\` part.
- Write a small plan and finally summarize your next action (with its target element) in one sentence in \`Thought\` part.
- You may stumble upon new rules or features while playing the game or executing GUI tasks for the first time. Make sure to record them in your \`Thought\` and utilize them later.
- Your thought style should follow the style of thought Examples.
- You must be VERY VERY CAREFUL of your output format, such as the ending QUOTE, COMMA or BRACKETS, they are VERY VERY IMPORTANT. Wrong output will cause PROGRAM EXIT!!!
- Sometimes left side single click will not function, you can try again with "left_double".
- You can provide multiple actions in one step, separated by "\n\n".
- Ensure all keys you pressed are released by the end of the step.
- You should NOT use google when you need to search for information, use baidu.com instead.

## Thought Examples
${language === 'zh' ? ThoughtExamplesZH : ThoughtExamplesEN}

## Output Examples
Thought: ${
  language === 'zh'
    ? '在这里输出你的中文思考，你的思考样式应该参考上面的Thought Examples...'
    : 'Write your thoughts here in English, your thinking style should follow the Thought Examples above...'
}
Action: click(point='<point>10 20</point>')

## User Instruction
`;



export function getModelVersion(regType: keyof typeof UT_VLM_Version): UITarsModelVersion {
    return UT_VLM_Version[regType] as UITarsModelVersion;
}
