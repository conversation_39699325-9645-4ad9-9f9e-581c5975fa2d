/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { machineId } from 'node-machine-id';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import { UITarsModelVersion } from '@ui-tars/shared/constants';
import { Conversation, type PredictionParsed } from '@ui-tars/shared/types';
import { FREE_MODEL_BASE_URL, getSystemPrompt, getSystemPromptDoubao_15_15B, getSystemPromptDoubao_15_20B, getSystemPromptV1_5 } from './config';
import { setOfMarksOverlays } from './image/setOfMarks';
import sharp from 'sharp';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const jose = require('jose');
// @ts-ignore
// import jose from 'jose';
// import sharp from 'sharp';

let SignJWT: any, importPKCS8: any;

class FetchError extends Error {
    xRequestId?: string | null;
    status?: number;
    constructor(message: string, xRequestId?: string | null, status?: number) {
        super(message);
        this.name = 'FetchError';
        this.xRequestId = xRequestId;
        this.status = status;
    }
}


(async () => {
    SignJWT = jose.SignJWT;
    importPKCS8 = jose.importPKCS8;
})();

const APP_DIR_NAME = '.ui-tars-desktop';
const LOCAL_KEY_PATH = path.join(os.homedir(), APP_DIR_NAME);

const LOCAL_PUB_KEY = 'local_public_v2.pem';
const LOCAL_PRIV_KEY = 'local_private_v2.pem';

const ALGO = 'RS256';

async function getLocalPrivKey(format: 'base64' | 'origin'): Promise<string> {
    const privateKeyPath = path.join(LOCAL_KEY_PATH, LOCAL_PRIV_KEY);
    if (!fs.existsSync(privateKeyPath)) {
        throw new Error('Private key not found');
    }
    const localPrivateKeyPem = fs.readFileSync(privateKeyPath, 'utf-8');
    if (format === 'origin') {
        return localPrivateKeyPem;
    }
    const privateKeyBase64 = Buffer.from(localPrivateKeyPem, 'utf-8').toString(
        'base64',
    );
    return privateKeyBase64;
}

let cachedDeviceId: string | null = null;
async function getDeviceId(): Promise<string> {
    if (!cachedDeviceId) {
        cachedDeviceId = await machineId();
        console.log('[Auth] getDeviceId:', cachedDeviceId);
    }
    return cachedDeviceId;
}
export async function getAuthHeader() {
    const deviceId = await getDeviceId();
    const ts = Date.now();

    const localDevicePrivBase64 = await getLocalPrivKey('origin');

    const localDevicePrivateKey = await importPKCS8(localDevicePrivBase64, ALGO);
    const authToken = await new SignJWT({
        deviceId,
        ts,
    })
        .setProtectedHeader({ alg: ALGO })
        .sign(localDevicePrivateKey);

    return {
        'X-Device-Id': await getDeviceId(),
        'X-Timestamp': ts.toString(),
        Authorization: `Bearer ${authToken}`,
    };
}

async function fetchWithAuth(
    url: string,
    options: RequestInit,
    retries = 1,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<any> {
    let xRequestId: string | null = 'undefined';
    try {
        if (!options.headers) {
            options.headers = {};
        }
        const authHeader = await getAuthHeader();
        Object.assign(options.headers, {
            ...authHeader,
        });
        const response = await fetch(url, options);
        xRequestId = response.headers.get('x-request-id');

        if (!response.ok) {
            throw new FetchError(
                `HTTP error! status: ${response.status}, xRequestId: ${xRequestId}`,
                xRequestId,
                response.status,
            );
        }

        const data = await response.json();
        return data;
    } catch (error) {
        if (retries <= 0) throw error;
        console.error(
            `[proxyClient] Retrying request..., xRequestId: ${xRequestId}`,
        );
        return fetchWithAuth(url, options, retries - 1);
    }
}


export async function getRemoteVLMResponseApiSupport(): Promise<boolean> {
    try {
        const res = await fetchWithAuth(
            `${FREE_MODEL_BASE_URL}/responses/support`,
            {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
            },
        );
        console.log(
            '[ProxyClient] Get Remote VLM Response API Support Response:',
            res,
        );
        return res.data;
    } catch (error) {
        console.error(
            '[ProxyClient] Get Remote VLM Response API Support Error:',
            (error as Error).message,
        );
        return false;
    }
}


export async function getRemoteVLMProvider(): Promise<UITarsModelVersion> {
    try {
        const data = await fetchWithAuth(`${FREE_MODEL_BASE_URL}/chat/provider`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
        });
        console.log('[ProxyClient] Get Remote VLM Provider Response:', data);
        const res = data.data;
        let modelVer = UITarsModelVersion.DOUBAO_1_5_20B;
        switch (res) {
            case 'UI-TARS-1.5':
                modelVer = UITarsModelVersion.V1_5;
                break;
            case 'UI-TARS-1.0':
                modelVer = UITarsModelVersion.V1_0;
                break;
            case 'Doubao-1.5-UI-TARS':
                modelVer = UITarsModelVersion.DOUBAO_1_5_15B;
                break;
            case 'Doubao-1.5-thinking-vision-pro':
                modelVer = UITarsModelVersion.DOUBAO_1_5_20B;
                break;
            default:
                modelVer = UITarsModelVersion.DOUBAO_1_5_20B;
        }
        return modelVer;
    } catch (error) {
        console.error(
            '[ProxyClient] Get Remote VLM Provider Error:',
            (error as Error).message,
        );
        return UITarsModelVersion.DOUBAO_1_5_20B;
    }
}

export const getSpByModelVersion = (
  modelVersion: UITarsModelVersion,
  language: 'zh' | 'en',
  operatorType: 'browser' | 'computer',
) => {
  switch (modelVersion) {
    case UITarsModelVersion.DOUBAO_1_5_20B:
      return getSystemPromptDoubao_15_20B(language, operatorType);
    case UITarsModelVersion.DOUBAO_1_5_15B:
      return getSystemPromptDoubao_15_15B(language);
    case UITarsModelVersion.V1_5:
      return getSystemPromptV1_5(language, 'normal');
    default:
      return getSystemPrompt(language);
  }
};


// TODO: use jimp to mark click position
export async function markClickPosition(data: {
  base64: string;
  screenshotContext: NonNullable<Conversation['screenshotContext']>;
  parsed: PredictionParsed[];
}): Promise<string> {
  if (!data?.parsed?.length) {
    return data.base64;
  }
  try {
    const imageBuffer = Buffer.from(data.base64, 'base64');
    const { overlays = [] } = setOfMarksOverlays({
      predictions: data.parsed,
      screenshotContext: data.screenshotContext,
    });
    const imageOverlays: sharp.OverlayOptions[] = overlays
      .map((o) => {
        if (o.yPos && o.xPos) {
          return {
            input: Buffer.from(o.svg),
            top: o.yPos + o.offsetY,
            left: o.xPos + o.offsetX,
          };
        }
        return null;
      })
      .filter((overlay) => !!overlay);

    if (!imageOverlays?.length) {
      return '';
    }

    const result = await sharp(imageBuffer).composite(imageOverlays).toBuffer();

    return result.toString('base64');
  } catch (error) {
    console.error('图片处理出错:', error);
    // return origin base64
    return '';
  }
}

export const filterAndTransformWithMap = (
  history: ConversationWithSoM[],
): Message[] => {
  return history
    .map((conv) => {
      if (conv.from === 'human' && conv.value && conv.value !== '<image>') {
        return {
          from: conv.from,
          value: conv.value,
        };
      } else if (conv.from === 'gpt' && conv.predictionParsed?.length) {
        const finished = conv.predictionParsed.find(
          (p) => p.action_type === 'finished' && p.action_inputs?.content,
        );
        if (finished) {
          return {
            from: conv.from,
            value: finished.action_inputs!.content!,
          };
        }

        const callUser = conv.predictionParsed.find(
          (p) => p.action_type === 'call_user' && p.thought,
        );
        if (callUser) {
          return {
            from: conv.from,
            value: callUser.thought!,
          };
        }
        return undefined;
      } else {
        return undefined;
      }
    })
    .filter((msg): msg is Message => msg !== undefined);
};
