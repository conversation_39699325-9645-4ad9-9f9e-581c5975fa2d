{"compilerOptions": {"target": "es2020", "module": "nodenext", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "nodenext", "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": true, "declarationMap": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*", "src/types/*"]}, "types": ["node"]}, "include": ["src/browser-agent/**/*"], "exclude": ["node_modules", "dist", "src/renderer"]}