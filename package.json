{"name": "qian-li-yan", "version": "1.0.0", "description": "千里眼应用端", "main": "dist/main/main.js", "scripts": {"preinstall": "node scripts/check-versions.js", "start": "electron .", "dev": "concurrently \"npm run build:watch\" \"wait-on dist/main/main.js && electron .\"", "build": "rm -rf dist && npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:agent": "tsc -p tsconfig.agent.json", "build:renderer": "webpack --mode production", "build:watch": "concurrently \"npm run build:main -- --watch\" \"npm run build:renderer -- --watch\"", "build:electron": "electron-builder", "build-win": "npm run build && electron-builder --win", "build-mac": "npm run build && electron-builder --mac", "build-linux": "npm run build && electron-builder --linux", "dist": "npm run build:electron", "pack": "npm run build && electron-builder --dir", "check-versions": "node scripts/check-versions.js", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "automa", "automation", "desktop", "communication", "workflow"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/lodash": "^4.17.19", "@types/node": "^20.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "^27.0.0", "electron-builder": "^24.6.4", "html-webpack-plugin": "^5.5.0", "node-machine-id": "^1.1.12", "sass": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "^3.3.0", "ts-loader": "^9.4.0", "typescript": "^5.2.0", "wait-on": "^7.0.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"axios": "^1.6.0", "@ant-design/icons": "^6.0.0", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@ui-tars/operator-browser": "^1.2.3", "@ui-tars/sdk": "^1.2.3", "@ui-tars/shared": "^1.2.3", "antd": "^5.26.4", "body-parser": "^2.2.0", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.16.0-beta.3", "dayjs": "^1.11.13", "electron-dl": "^4.0.0", "electron-store": "^8.2.0", "express": "^5.1.0", "jose": "^5.10.0", "lodash": "^4.17.21", "mysql2": "^3.14.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "sharp": "^0.33.5", "semver": "^7.5.4", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "uuidv4": "^6.2.13", "ws": "^8.14.0", "xlsx": "^0.18.5"}, "overrides": {"@agent-infra/browser": {"edge-paths": "^2.2.1"}}, "build": {"appId": "com.yourcompany.electron-automa-bridge", "productName": "Electron Automa Bridge", "directories": {"output": "dist"}, "files": ["dist/**/*", "package.json", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "asar": true, "asarUnpack": ["**/node_modules/sharp/**/*", "**/node_modules/@img/**/*"]}, "repository": {"type": "git", "url": "https://github.com/yourusername/electron-automa-bridge.git"}, "homepage": "https://github.com/yourusername/electron-automa-bridge#readme", "engines": {"node": ">=18.20.1", "pnpm": ">=8.15.9"}, "packageManager": "pnpm@8.15.9"}